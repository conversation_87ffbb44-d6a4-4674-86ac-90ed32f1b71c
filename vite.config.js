// vite.config.js
import { fileURLToPath, URL } from 'node:url'

import { resolve } from 'path'
import { defineConfig } from 'vite'
import tailwindcss from '@tailwindcss/vite'

import vue from '@vitejs/plugin-vue'
import VueDevTools from 'vite-plugin-vue-devtools'

export default defineConfig(({ mode }) => {
  // Embed mode
  if (mode === 'embed') {
    return {
      publicDir: false,
      build: {
        outDir: 'dist-embed',
        lib: {
          entry: resolve(__dirname, 'src/embed/embed.js'),
          name: 'embed',
          fileName: 'embed'
        }
      },
      resolve: {
        extensions: ['.js'],
        alias: {
          '@': fileURLToPath(new URL('./src', import.meta.url))
        }
      }
    }
  }
  
  // Browser (Playwright) test configuration
  if (mode === 'browser-test') {
    return {
      plugins: [vue()],
      resolve: {
        extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
        alias: {
          '@': fileURLToPath(new URL('./src', import.meta.url))
        }
      },
      test: {
        globals: true,
        testTimeout: 30000,       // Longer timeout for WebAssembly loading
        
        browser: {
          enabled: true,          // Enable browser mode
          provider: 'playwright', // Use Playwright as the browser provider
          instances: [
            { browser: 'chromium' }
          ],
          headless: true,
          screenshot: false
        },
        include: ['**/*.browser.test.js'],  // Only run *.browser.test.js files
        coverage: {
          provider: 'v8',
          reporter: ['text', 'json', 'html'],
          exclude: [
            'node_modules/',
            'dist/',
            '**/*.{test,spec}.{js,ts}',
            '**/mocks/**'
          ]
        }
      }
    }
  }
  
  // Standard production build
  return {
    
    // Stop hot-reloading when test files are edited
    server: {
      watch: {
        ignored: ['**/*.test.js', '**/*.spec.js', '**/*.json']
      }
    },
    plugins: [
      tailwindcss(),
      vue(),
      VueDevTools({
        launchEditor: 'pycharm'
      })
    ],
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    test: {
      environment: 'jsdom', // Default environment for regular tests
      globals: true,
      coverage: {
        provider: 'v8', // or 'istanbul'
        reporter: ['text', 'json', 'html'],
        exclude: [
          'node_modules/',
          'dist/',
          '**/*.{test,spec}.{js,ts}',
          '**/mocks/**'
        ]
      }
    }
  }
})
