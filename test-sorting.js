// Simple test to verify the sorting functionality
import { getSortColumns } from './src/db/query/utils.js'

// Test data
const testColumns = [
  { id: 1, sourceColumnId: 10, sort: { order: 1, desc: false } },
  { id: 2, sourceColumnId: 20, sort: { order: 0, desc: true } },
  { id: 3, sourceColumnId: 30 }, // no sort
]

console.log('Testing getSortColumns...')
const result = getSortColumns(testColumns)
console.log('Result:', result)

// Expected: [{ id: 2, desc: true }, { id: 1, desc: false }]
// Should be sorted by order: 0, 1
