module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  rules: {
    'no-console': 'off',
    'no-debugger': 'off',
    'no-unused-vars': 'off',
    'vue/no-unused-components': 'off',
    'vue/no-reserved-component-names': 'off' // https://github.com/tailwindlabs/headlessui/issues/55 - warns about e.g. headless "dialog" etc
  }
}
