{"name": "datahero", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-embed": "vite build --mode embed", "serve": "vite preview", "test": "vitest", "test:browser": "vitest --project browser", "test:jsdom": "vitest --project jsdom", "coverage": "vitest run --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore --fix src"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/lang-sql": "^6.8.0", "@codemirror/language": "^6.11.0", "@codemirror/view": "^6.36.4", "@date-fns/utc": "^2.1.0", "@duckdb/duckdb-wasm": "^1.29.0", "@headlessui/vue": "^1.7.22", "@kurkle/color": "^0.3.2", "@lezer/highlight": "^1.2.1", "@rushstack/eslint-patch": "^1.10.1", "@tailwindcss/forms": "^0.5.7", "@unhead/vue": "^1.10.0", "@vitejs/plugin-vue": "^5.0.4", "@vuepic/vue-datepicker": "^11.0.2", "ag-grid-community": "^32.0.2", "ag-grid-vue3": "^32.0.2", "axios": "1.8.2", "big.js": "^6.2.2", "browserslist": "^4.23.3", "chart.js": "^4.4.3", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-stacked100": "^1.5.3", "csv-writer-browser": "^1.0.0", "date-fns": "^3.6.0", "fast-deep-equal": "^3.1.3", "filenamify": "^6.0.0", "jwt-decode": "^4.0.0", "lucide-vue-next": "^0.483.0", "moment-guess": "^1.2.4", "npm": "^10.5.1", "papaparse": "^5.4.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "sortablejs": "^1.15.6", "sortablejs-vue3": "^1.2.11", "vue": "^3.4.35", "vue-chartjs": "^5.3.1", "vue-codemirror": "^6.1.1", "vue-router": "^4.1.6", "vue-turnstile": "^1.0.10", "vue3-sortablejs": "^1.0.7"}, "devDependencies": {"@pinia/testing": "^1.0.0", "@playwright/test": "^1.50.1", "@tailwindcss/postcss": "^4.0.15", "@tailwindcss/typography": "^0.5.14", "@tailwindcss/vite": "^4.0.15", "@types/big.js": "^6.2.2", "@vitest/browser": "^3.0.7", "@vitest/coverage-v8": "^3.0.7", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vue/compiler-sfc": "^3.2.45", "@vue/devtools-api": "^7.5.3", "@vue/eslint-config-standard": "^8.0.1", "@vue/test-utils": "^2.4.6", "eslint": "^8.57.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^9.2.0", "jsdom": "^26.0.0", "leaflet": "^1.9.4", "playwright": "^1.50.1", "vite": "^5.1.5", "vite-plugin-vue-devtools": "^7.5.3", "vitest": "^3.0.7"}}