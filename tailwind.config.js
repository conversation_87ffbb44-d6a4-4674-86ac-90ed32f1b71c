module.exports = {
  mode: 'jit',
  content: ['./public/**/*.html', './src/**/*.vue'],
  theme: {
    extend: {
      width: {
        104: '26rem',
        112: '28rem',
        128: '32rem'
      },
      
      /* adds in e.g. a bigger max height */
      spacing: {
        '128': '32rem',
        '144': '36rem',
        '192': '48rem'
      },
      
      cursor: {
        'row-resize': 'row-resize'
      }
    },
    fontFamily: {
      sans: ['Inter', 'sans-serif'],
      mono: ['ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', '"Liberation Mono"', '"Courier New"', 'monospace']
    } /*
    fontSize: {
      xs: ['0.7rem', 'px'],
      sm: ['0.8rem', 'px'],
      base: ['0.85rem', 'px'],
      lg: ['1.125rem', 'px'],
      xl: ['1.25rem', 'px'],
      '2xl': ['1.5rem', 'px'],
      '3xl': ['1.875rem', 'px'],
      '4xl': ['2.25rem', 'px'],
      '5xl': ['3rem', 'px'],
      '6xl': ['3.75rem', 'px'],
      '7xl': ['4.5rem', 'px'],
      '8xl': ['6rem', 'px'],
      '9xl': ['8rem', 'px']
    } */
  },
  variants: {
    extend: {}
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography')
  ]
}
