<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Embed test</title>
  <style>
    /* make the background dark if user in dark mode */
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #222;
        color: #fff;
      }
    }

    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
    }

    .datahero {
      margin: 10px 0 60px;
      height: 400px;
    }

    #root {
      max-width: 1000px;
      margin: 0 auto;
    }
  </style>
</head>
<body>
<div id="root">
  <div class="datahero" style="width: 100%; height: 768px" data-datahero-id="2muf6qaabBB3em9j5RvmR4"></div>
  <script id="datahero-2muf6qaabBB3em9j5RvmR4" type="application/json">{
    "cols": [
      {
        "id": 0,
        "header": "Fruit",
        "type": "text"
      },
      {
        "id": 2,
        "header": "Stock",
        "type": "number",
        "props": {
          "decimals": 1,
          "decimalsPad": false,
          "thousands": false,
          "compact": false
        }
      }
    ],
    "rows": [
      {
        "id": 7,
        "c0": "Kiwi",
        "c2": 4
      },
      {
        "id": 8,
        "c0": "Banana",
        "c2": 6
      },
      {
        "id": 5,
        "c0": "Apples",
        "c2": 5
      },
      {
        "id": 6,
        "c0": "Satsuma",
        "c2": 13
      }
    ],
    "datasets": [
      {
        "chartType": "bar",
        "series": [
          {
            "id": 2,
            "color": 1
          }
        ],
        "barOrientation": "v",
        "axis": {
          "show": true,
          "label": "Number in stock"
        }
      }
    ],
    "title": "Stock levels of the four BEST fruits",
    "horizontal": true,
    "labelAxis": {
      "show": true,
      "label": "Fruits",
      "column": 0
    }
  }</script>


  <div class="datahero" style="width: 640px; height: 480px" data-datahero-id="dAqmWySivqZ6ZKmcButcGh"></div>
  <script id="datahero-dAqmWySivqZ6ZKmcButcGh" type="application/json">{
    "cols": [
      {
        "id": 1,
        "header": "Cost",
        "type": "currency",
        "props": {
          "locale": "en-GB",
          "currency": "GBP",
          "accounting": false,
          "display": "symbol"
        }
      }
    ],
    "rows": [
      {
        "id": 3,
        "c1": 1.4
      },
      {
        "id": 4,
        "c1": 0.85
      },
      {
        "id": 7,
        "c1": 0.23
      },
      {
        "id": 8,
        "c1": 1.94
      },
      {
        "id": 5,
        "c1": 1.2
      },
      {
        "id": 6,
        "c1": 0.75
      },
      {
        "id": 9,
        "c1": 0.5
      },
      {
        "id": 10,
        "c1": 22.2
      }
    ],
    "datasets": [
      {
        "chartType": "bar",
        "series": [
          {
            "id": 1,
            "color": 0
          }
        ],
        "axis": {
          "show": false
        }
      }
    ]
  }</script>


  <div class="datahero" style="width: 640px; height: 480px" data-datahero-id="4aWwKe2ayA2UySgCtsZzJn"></div>

  <script id="datahero-4aWwKe2ayA2UySgCtsZzJn" type="application/json">{
    "title": "100% stacking / interesting title!",
    "cols": [
      {
        "id": 0,
        "header": "Fruit",
        "props": {},
        "type": "text"
      },
      {
        "id": 5,
        "header": "Stock Value",
        "props": {
          "locale": "en-GB",
          "currency": "GBP",
          "accounting": false,
          "display": "symbol"
        },
        "type": "currency"
      },
      {
        "id": 2,
        "header": "Stock",
        "props": {
          "decimals": 1,
          "decimalsPad": false,
          "thousands": false,
          "compact": false
        },
        "type": "number"
      }
    ],
    "rows": [
      {
        "id": 3,
        "c0": "Pear",
        "c5": 14,
        "c2": 10
      },
      {
        "id": 4,
        "c0": "Orange",
        "c5": 4.25,
        "c2": 5
      },
      {
        "id": 7,
        "c0": "Kiwi",
        "c5": 0.92,
        "c2": 4
      },
      {
        "id": 8,
        "c0": "Banana",
        "c5": 11.64,
        "c2": 6
      },
      {
        "id": 5,
        "c0": "Apples",
        "c5": 6,
        "c2": 5
      },
      {
        "id": 6,
        "c0": "Satsuma",
        "c5": 9.75,
        "c2": 13
      },
      {
        "id": 9,
        "c0": "Blueberries",
        "c5": 1,
        "c2": 2
      },
      {
        "id": 10,
        "c0": "New fruit",
        "c5": 66.6,
        "c2": 3
      }
    ],
    "datasets": [
      {
        "chartType": "bar",
        "series": [
          {
            "id": 2,
            "color": 0
          },
          {
            "id": 5,
            "color": 1
          }
        ],
        "values": [],
        "barOrientation": "v",
        "stacking": "stacked100",
        "axis": {
          "show": true,
          "label": "Number in stock"
        }
      }
    ],
    "subtitle": "which makes no sense",
    "byRow": false,
    "labelAxis": {
      "show": true,
      "label": "Fruits",
      "column": 0
    }
  }</script>


  <div class="datahero" style="height: 480px" data-datahero-id="RpNXrjUgkxag5yYYG8joiG">Not empty, so won't load!</div>


  <script id="datahero-RpNXrjUgkxag5yYYG8joiG" type="application/json">{
    "title": "Here's a complicated and honestly weird mixed chart",
    "cols": [
      {
        "id": 0,
        "header": "Fruit",
        "props": {},
        "type": "text"
      },
      {
        "id": 1,
        "header": "Cost",
        "props": {
          "locale": "en-GB",
          "currency": "GBP",
          "accounting": false,
          "display": "symbol"
        },
        "type": "currency"
      },
      {
        "id": 2,
        "header": "Stock",
        "props": {
          "decimals": 1,
          "decimalsPad": false,
          "thousands": false,
          "compact": false
        },
        "type": "number"
      }
    ],
    "rows": [
      {
        "id": 3,
        "c0": "Pear",
        "c1": 1.4,
        "c2": 10
      },
      {
        "id": 4,
        "c0": "Orange",
        "c1": 0.85,
        "c2": 5
      },
      {
        "id": 5,
        "c0": "Apples",
        "c1": 1.2,
        "c2": 5
      },
      {
        "id": 9,
        "c0": "Blueberries",
        "c1": 0.5,
        "c2": 2
      },
      {
        "id": 7,
        "c0": "Kiwi",
        "c1": 0.23,
        "c2": 4
      }
    ],
    "subtitle": "Then a sweet sweet sub",
    "datasets": [
      {
        "chartType": "bar",
        "defaultColor": 0,
        "series": [
          3,
          4,
          {
            "id": 7
          },
          9
        ],
        "values": [
          1,
          2
        ],
        "stacking": "",
        "axis": {
          "show": true,
          "label": "Up the Y mate LMAO"
        }
      },
      {
        "chartType": "line",
        "series": [
          {
            "id": 5,
            "color": {
              "index": 1
            }
          }
        ],
        "values": [],
        "lineFill": true,
        "axis": {
          "show": true,
          "label": "Other up lolol WOW zomg"
        }
      }
    ],
    "byRow": true,
    "labelAxis": {
      "show": true,
      "label": "Along the X mush",
      "column": 0
    }
  }</script>

  <div class="datahero" style="width: 640px; height: 480px" data-datahero-id="9vmYPnhST2sRyZ3hBQiMhF"></div>
  <script id="datahero-9vmYPnhST2sRyZ3hBQiMhF" type="application/json">{
    "title": "Pie of a row lol pie",
    "cols": [
      {
        "header": "Fruit",
        "props": {},
        "type": "text",
        "id": 0
      },
      {
        "header": "Cost",
        "props": {
          "locale": "en-GB",
          "currency": "GBP",
          "accounting": false,
          "display": "symbol"
        },
        "type": "currency",
        "id": 1
      },
      {
        "header": "Stock",
        "props": {
          "decimals": 1,
          "decimalsPad": false,
          "thousands": false,
          "compact": false
        },
        "type": "number",
        "id": 2
      },
      {
        "header": "Stock Value",
        "props": {
          "locale": "en-GB",
          "currency": "GBP",
          "accounting": false,
          "display": "symbol"
        },
        "type": "currency",
        "id": 5
      }
    ],
    "rows": [
      {
        "id": 8,
        "c0": "Banana",
        "c1": 1.95,
        "c2": 6,
        "c5": 11.7
      }
    ],
    "subtitle": "",
    "datasets": [
      {
        "chartType": "pie",
        "series": [
          8
        ],
        "defaultColor": 1,
        "values": [
          {
            "id": 2,
            "color": "black"
          },
          {
            "id": 5
          },
          {
            "id": 1,
            "color": 5
          }
        ],
        "donutCutout": 0,
        "axis": {
          "show": true,
          "label": "no such thing mate"
        }
      }
    ],
    "byRow": true,
    "labelAxis": {
      "show": true,
      "column": 0,
      "label": "llol"
    }
  }</script>

  <div class="datahero" style="width: 1280px; height: 1024px" data-datahero-id="ga5BPbjcrWGwq9Xjncp3uM"></div>
  <script id="datahero-ga5BPbjcrWGwq9Xjncp3uM" type="application/json">{
    "title": "Nonsensical mixed chart: stacked addition",
    "cols": [
      {
        "header": "Fruit",
        "props": {},
        "type": "text",
        "id": 0
      },
      {
        "header": "Cost",
        "props": {
          "locale": "en-GB",
          "currency": "GBP",
          "accounting": false,
          "display": "symbol"
        },
        "type": "currency",
        "id": 1
      },
      {
        "header": "Stock",
        "props": {
          "decimals": 1,
          "decimalsPad": false,
          "thousands": false,
          "compact": false
        },
        "type": "number",
        "id": 2
      },
      {
        "header": "Stock Value",
        "props": {
          "locale": "en-GB",
          "currency": "GBP",
          "accounting": false,
          "display": "symbol"
        },
        "type": "currency",
        "id": 5
      }
    ],
    "rows": [
      {
        "id": 3,
        "c0": "Pear",
        "c1": 1.4,
        "c2": 10,
        "c5": 14
      },
      {
        "id": 4,
        "c0": "Orange",
        "c1": 0.85,
        "c2": 5,
        "c5": 4.25
      },
      {
        "id": 5,
        "c0": "Apples",
        "c1": 1.2,
        "c2": 5,
        "c5": 6
      },
      {
        "id": 6,
        "c0": "Satsuma",
        "c1": 0.75,
        "c2": 13,
        "c5": 9.75
      },
      {
        "id": 8,
        "c0": "Banana",
        "c1": 1.95,
        "c2": 6,
        "c5": 11.7
      },
      {
        "id": 9,
        "c0": "Blueberries",
        "c1": 0.5,
        "c2": 2,
        "c5": 1
      },
      {
        "id": 7,
        "c0": "Kiwi",
        "c1": 0.23,
        "c2": 4,
        "c5": 0.92
      }
    ],
    "subtitle": "",
    "datasets": [
      {
        "defaultColor": 2,
        "chartType": "bar",
        "series": [
          2,
          5
        ],
        "values": [],
        "stacking": "stacked",
        "axis": {
          "show": true,
          "label": "Number in stock"
        }
      },
      {
        "chartType": "line",
        "series": [
          1
        ],
        "values": [],
        "lineFill": false,
        "axis": {
          "show": true,
          "label": "Cost in Stirling"
        }
      }
    ],
    "byRow": false,
    "labelAxis": {
      "show": true,
      "label": "Fruits",
      "column": 0
    }
  }</script>

  <!-- test multple scripts -->
  <script src="/src/embed/embed.js" type="module"></script>
  <script src="/src/embed/embed.js?2" type="module"></script>
  <script src="/src/embed/embed.js?3" type="module"></script>
</div>
</body>
</html>
