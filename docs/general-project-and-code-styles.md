# General Project and Code Styles

- Do not put semicolons at the end of lines
- Use 2 spaces for indentation
- Use single quotes for strings
- Use double quotes for JSX attributes
- Use double quotes for JSON
- Use double quotes for YAML
- Use double quotes for HTML attributes
- Use double quotes for CSS attributes
- Use double quotes for SQL
- Use double quotes for Markdown
- Use double quotes for CSV
- Use double quotes for TSV
- Use double quotes for JSON
- Use double quotes for YAML
- Use objects with enclosures, not javascript classes
- Always use absolute imports with the `@` prefix (e.g., `import Component from '@/components/Component.vue'`) instead
  of relative imports
- use ?? to check for nullish/undefined values wherever possible. We have lots of valid 0-value integers (ids, indexes)

# Vue style

- Never use mapStores.
- For a single use of an action or getter in a component, use an inline useStore().action(...)
- For multiple uses, always use an object mapping for `mapState`, `mapActions` and `mapWriteableState`. Do not do array
  mapping.z`
- Name every mapping with a prefix of the store - e.g. `tableStore.title` should be `tableStoreTitle`
- Do not repeat store name in the attribute: for example, `tableStore.tableId` should map to `tableStoreId`

An example of mapState:

```js
computed: {
  mapState(useTableStore, {
    tableStoreId: 'tableId',
    tableStoreTitle: 'title',
    tableStoreLabelColumnId: 'labelColumnId'
  })
}
```

# Tailwind style

- Primary color is fuchsia
- Zinc should be used for grays

# Test Styles

- Use co-located tests for unit tests and component tests. Name them file.test.js
- Use centralized tests for smoke tests, end-to-end tests and integration tests. Store in /src/tests/file.spec.js
