import { describe, expect, it } from 'vitest'

import { parseData } from './parse'
import { miliseconds } from '@/_testfiles/miliseconds.js'
import { detectDateFormats } from '@/utils/dates.js'

describe('parseDates', () => {
  it('Should import seven dates in the first column correctly', () => {
    const {
      rowData,
      colDefs,
      hasHeader
    } = parseData(miliseconds)
    expect(hasHeader).toBe(true)
    expect(rowData.length).toBe(8)
    expect(colDefs[0].type).toBe('datetime')
    
    // make sure every row has a date that returns a format in detectDateFormats
    rowData.slice(1).forEach(row => {
      expect(detectDateFormats(row[0])).not.toHaveLength(0)
    })
  })
})
