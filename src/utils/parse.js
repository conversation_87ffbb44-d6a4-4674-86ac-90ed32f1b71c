import Papa from 'papaparse'

import { detectColumns } from './detect'
import { convertFileNameToTitle } from './helpers'
import { columnTypes, convert } from '@/utils/formats.js'

const csvDelimiters = ['\t', ',', '|', ';', Papa.RECORD_SEP, Papa.UNIT_SEP] // tab over comma precedence

const papaParams = {
  header: false, // this means data results will be an array of arrays, not an array of objects
  delimitersToGuess: csvDelimiters,
  skipEmptyLines: true
}

const isConverted = (v, repr, colType, props) => {
  return convert(v, colType, props) !== null
}

function processParsedData (rowData, title) {
  /*
  Given the results.data from Papa.parse, detects columns and returns:
  - title, just echoing what's passed in right now (in the future, could sniff from table structure)
  - colDefs, an array of column definitions based on the detected columns
  - rowData, stripped out empty or near-empty rows, but otherwise untouched.
  - hasHeader, a recommendation on whether the data has a header row or not
   */
  // Strip out any empty rows in rowData
  rowData = rowData.filter(row => row.some(cell => cell.trim()))
  const colDefs = detectColumns(rowData)
  
  // Filter out rows that have just 1 col when colDefs is 3 or more, or that have just 2 cols in when colDefs 5 or more
  rowData = rowData.filter(row => {
    const len = row.length
    return (len > 1 || colDefs.length < 3) && (len > 2 || colDefs.length < 5)
  })
  
  // Only consider autoheader if we have more than one row and not every colType is text
  let hasHeader = false
  if (rowData.length > 1 && colDefs.some(col => col.type !== 'text')) {
    const topRow = rowData[0]
    hasHeader = topRow.every((cell, i) => {
      const cd = colDefs[i]
      if (cd.type === 'text') {
        // Text is a header
        return true
      } else if (cd.type === 'datetime') {
        // Headers of datetime should *fail* a conversion with the sniffed format
        return columnTypes[cd.type].convert(cell.trim(), cd.props) === null
      } else {
        // Otherwise, calls to native should fail for numeric cols (convert is too
        // lenient, as it will convert strings like `F5` to 5 and thus succeed)
        return columnTypes[cd.type].native(cell.trim()) === null
      }
    })
  }
  
  if (!title) {
    title = ''
  }
  return { title, rowData, hasHeader, colDefs }
}

export function parseFile (file) {
  /* Promise means we can await its response in an async function */
  // https://stackoverflow.com/a/56706780
  const title = convertFileNameToTitle(file.name)
  return new Promise(resolve => {
    Papa.parse(file, {
      complete: results => {
        resolve(processParsedData(results.data, title))
      }
    })
  })
}

// runs some pasted data thru papaparse and returns its data
export function parseData (val) {
  if (!val) return
  const rowData = Papa.parse(val, papaParams).data
  return processParsedData(rowData)
}

function parseWithBestDelimiter (text, allowEmpty = false) {
  /*
  PapaParse doesn't allow you to remove comma and have a 'guesses' list of delimiters, so
  we have to try each in turn. We don't want to split on commans on a paste due to values
  like $1,234.56 -(and the euro version, €1234,56). In the future we could implement a
  more sophisticated approach that does split on commas when it looks more like CSV than
  some numeric data in a single column - tho who is to say is "12,23" is one col or two?
   */
  const delimiters = ['\t', '|', ';']
  let bestResult = null
  let maxColumns = 0
  
  for (const delimiter of delimiters) {
    const params = {
      header: false,
      delimiter: delimiter,
      skipEmptyLines: !allowEmpty
    }
    
    const result = Papa.parse(text, params)
    
    // Count maximum number of columns found
    const columnsCount = Math.max(...result.data.map(row => row.length))
    
    if (columnsCount > maxColumns) {
      maxColumns = columnsCount
      bestResult = result
    }
  }
  
  return bestResult
}

/**
 * Process clipboard paste data into a structured format
 * @param {ClipboardEvent} e - The clipboard event containing paste data
 * @param allowEmpty - Allow empty rows
 * @returns {Array<Array<string>>|null} 2D array of parsed data or null if invalid
 */
export function processPasteData (e, allowEmpty = false) {
  if (!e?.clipboardData) return null
  
  const text = e.clipboardData.getData('text/plain')
  if (!allowEmpty && !text?.trim()) return null
  const result = parseWithBestDelimiter(text, allowEmpty)
  if (!result?.data?.length) return null
  
  const cleanData = result.data
    .filter(row => allowEmpty || row.some(cell => cell?.trim()))
    .map(row => row.map(cell => cell?.trim() || ''))
  
  return cleanData.length > 0 ? cleanData : null
}

