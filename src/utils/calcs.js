import { getDefaultColProps } from './formats'

export function findAffectedColumns (graph, colId) {
  /*
  Finds all columns that are affected by a change to the given column. Does not
  guarantee correct order.
   */
  const affectedColumns = []
  const node = graph.get(colId)
  if (!node) return []
  const queue = [...node]
  while (queue.length > 0) {
    const column = queue.shift()
    
    if (!affectedColumns.includes(column)) {
      affectedColumns.push(column)
      
      const dependents = graph.get(column)
      if (dependents) {
        dependents.forEach(dependent => {
          if (!affectedColumns.includes(dependent) && !queue.includes(dependent)) {
            queue.push(dependent)
          }
        })
      }
    }
  }
  return affectedColumns
}

export const nullToZero = (val) => {
  // Coalesce is a SQL function that returns the first non-null value in a list; this turns
  // any null values into 0s so things like 'sum' still acts as you'd expect
  return `COALESCE(${val}, 0)`
}

export const calculatedColumns = {
  cumulative: {
    type: 'cumulative',
    label: 'Cumulative',
    desc: 'The total of all cells up to and including the current cell',
    category: 'Common',
    sources: 'single',
    isWindow: true,
    column: (sourceCols, data, orderCol = 'pos') => {
      const sourceCol = sourceCols[0]
      return `SUM(c${sourceCol}) OVER (ORDER BY ${orderCol} ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)`
    }
  },
  
  changeAbs: {
    type: 'changeAbs',
    label: 'Change',
    desc: 'The difference between the current cell and the previous cell',
    category: 'Change',
    sources: 'single',
    isWindow: false,
    column: (sourceCols, data, orderCol = 'pos') => {
      const sourceCol = sourceCols[0]
      return `c${sourceCol} - LAG(c${sourceCol}, 1) OVER (ORDER BY ${orderCol})`
    }
  },
  
  change: {
    type: 'change',
    label: 'Change %',
    category: 'Change',
    desc: 'The percentage difference between the current cell and one higher up.',
    sources: 'single',
    colType: 'percent',
    isWindow: true,
    column: (sourceCols, data = {}, orderCol = 'pos') => {
      const sourceCol = sourceCols[0]
      const up = data.up || 1
      return `CAST((c${sourceCol} - LAG(c${sourceCol}, ${up}) OVER (ORDER BY ${orderCol})) / NULLIF(LAG(c${sourceCol}, ${up}) OVER (ORDER BY ${orderCol}), 0) AS DECIMAL)`
    },
    
    props: [
      {
        key: 'up',
        label: 'Compare up how many rows?',
        type: 'combo',
        default: 1,
        options: [1, 4, 12]
      }
    ],
    presets: [
      {
        label: 'Compare to row above',
        key: 'daily',
        data: {
          up: 1
        }
      },
      {
        label: '4 above',
        help: 'Useful for quarters compared year-over-year',
        key: 'quarterly',
        data: {
          up: 4
        }
      },
      {
        label: '7 above',
        help: 'Useful for days compared week-over-week',
        key: 'weekly',
        data: {
          up: 7
        }
      },
      {
        label: '12 above',
        help: 'Useful for months compared year-over-year',
        key: 'monthly',
        data: {
          up: 12
        }
      }
    ]
  },
  
  moving: {
    type: 'moving',
    label: 'Moving Average',
    category: 'Change',
    desc: 'The average of the current cell and recent ones prior',
    sources: 'single',
    isWindow: true,
    column: (sourceCols, data = {}, orderCol = 'pos') => {
      const sourceCol = sourceCols[0]
      const moving = data.moving || 10
      return `CAST(AVG(c${sourceCol}) OVER (ORDER BY ${orderCol} ROWS BETWEEN ${moving - 1} PRECEDING AND CURRENT ROW) AS DECIMAL)`
    },
    props: [
      {
        key: 'moving',
        label: 'How many rows to average?',
        type: 'combo',
        default: 10,
        options: [5, 10, 20, 50, 200]
      }
    ]
  },
  
  sum: {
    category: 'Rows',
    type: 'sum',
    label: 'Sum',
    sources: 'multi',
    desc: 'Add together cells of all selected columns in this row',
    isWindow: false,
    column: (sourceCols) => {
      return sourceCols.map(col => nullToZero(`c${col}`)).join(' + ')
    }
  },
  
  max: {
    category: 'Rows',
    type: 'max',
    label: 'Max',
    sources: 'multi',
    desc: 'Maximum value of all cells in selected columns in this row',
    isWindow: false,
    column: (sourceCols) => {
      return `GREATEST(${sourceCols.map(col => `c${col}`).join(', ')})`
    }
  },
  
  min: {
    category: 'Rows',
    type: 'min',
    label: 'Min',
    sources: 'multi',
    desc: 'Minimum value of all cells in selected columns in this row',
    isWindow: false,
    column: (sourceCols) => {
      return `LEAST(${sourceCols.map(col => `c${col}`).join(', ')})`
    }
  },
  
  avg: {
    category: 'Rows',
    type: 'avg',
    label: 'Average',
    sources: 'multi',
    desc: 'Average of all selected columns in this row',
    isWindow: false,
    column: (sourceCols) => {
      return `CAST((${sourceCols.map(col => `c${col}`).join(' + ')}) / ${sourceCols.length} AS DECIMAL)`
    }
  },
  
  diff: {
    category: 'Rows',
    type: 'diff',
    label: 'Difference',
    sources: 'multi',
    desc: 'Difference between the largest and smallest values in columns in this row',
    isWindow: false,
    column: (sourceCols) => {
      return `GREATEST(${sourceCols.map(col => `c${col}`).join(', ')}) - LEAST(${sourceCols.map(col => `c${col}`).join(', ')})`
    }
  },
  
  multiply: {
    category: 'Rows',
    type: 'multiply',
    label: 'Multiply',
    sources: 'multi',
    desc: 'Multiply all selected columns in this row',
    isWindow: false,
    column: (sourceCols) => {
      return sourceCols.map(col => `c${col}`).join(' * ')
    }
  }
}

// Get list of all calculatedColumns definitions where sources === 'single
export const oneSourceCalcs = Object.values(calculatedColumns).filter((c) => c.sources === 'single')
export const multiSourceCalcs = Object.values(calculatedColumns).filter((c) => c.sources === 'multi')

export const oneSourceCalcTypes = oneSourceCalcs.map(c => c.type)
export const multiSourceCalcTypes = multiSourceCalcs.map(c => c.type)

// This is a VERY rudamentary list of column types we can't calc on; this needs to be improved to be more
// sophisticated
export const cantCalcOnTypes = ['text', 'boolean']

// create a new object where each key is a category, and inside is a list of all calculations in that category
export const calculatedColumnsByCategory = Object.values(calculatedColumns).reduce((acc, c) => {
  if (!acc[c.category]) {
    acc[c.category] = []
  }
  acc[c.category].push(c)
  return acc
}, {})

// Defines a precedence order for the different column types -
// the output of a calc that includes this type will be of this type
const colTypeHierarchy = ['date', 'currency', 'number', 'perc']

export const getColTypeAndPropsForCalc = (calc, sourceColumns) => {
  /*
  Some simple logic on setting the col type and props for a calculation.

  If the calc has a colType, use those. If not, go through all colTypeHierarchy
  and find the first column that matches. Copy that column's props.
  */
  let colType, colProps
  if (calc.colType) {
    colType = calc.colType
    colProps = getDefaultColProps(colType)
  } else {
    for (let i = 0; i < colTypeHierarchy.length; i++) {
      const col = sourceColumns.find(c => c && c.type === colTypeHierarchy[i])
      if (col) {
        colType = col.type
        colProps = { ...col.props }
        break
      }
    }
  }
  if (colType === undefined) {
    colType = sourceColumns[0].type
    colProps = { ...sourceColumns[0].props }
  }
  return {
    colType,
    colProps
  }
}

export function generateAutoTitle (calc, sourceColumnHeaders) {
  if (!calc) return ''
  
  switch (sourceColumnHeaders.length) {
    case 1:
      return `${calc.label} of ${sourceColumnHeaders[0]}`
    case 2:
      return `${calc.label} of ${sourceColumnHeaders[0]} and ${sourceColumnHeaders[1]}`
    case 3:
      return `${calc.label} of ${sourceColumnHeaders[0]}, ${sourceColumnHeaders[1]} and ${sourceColumnHeaders[2]}`
    default:
      return `${calc.label}`
  }
}

export function verifyCalcData (calc) {
  // This adds in-place on a calc object and properties in `data` that don't exist,
  // setting them to their defaults
  const data = calc.data ? { ...calc.data } : {}
  calculatedColumns[calc.type].props?.forEach(prop => {
    if (!(prop.key in data)) {
      data[prop.key] = prop.default
    }
  })
  
  // if we have any keys in data, then set it to calc.data
  if (Object.keys(data).length) {
    calc.data = data
  }
}
