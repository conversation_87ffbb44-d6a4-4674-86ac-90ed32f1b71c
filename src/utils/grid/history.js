import { native } from '@/utils/formats.js'

const MAX_HISTORY_SIZE = 50

export class TableOperation {
  constructor (type, cells) {
    this.type = type
    this.timestamp = Date.now()
    this.cells = cells.map(({ rowId, rowIndex, colId, oldVal, newVal }) => ({
      rowId: Number(rowId),
      rowIndex: Number(rowIndex),
      colId: Number(colId),
      oldVal,
      newVal
    }))
  }
  
  toDiff (isUndo = false) {
    return this.cells.reduce((acc, cell) => {
      if (cell.rowIndex === undefined || cell.rowIndex === null || cell.rowIndex === '' || isNaN(cell.rowIndex)) {
        throw new Error(`Missing rowIndex in cell operation: ${JSON.stringify(cell)}`)
      }
      
      if (!acc[cell.rowIndex]) {
        acc[cell.rowIndex] = { id: cell.rowId }
      }
      acc[cell.rowIndex][`c${cell.colId}`] = isUndo
                                             ? (cell.oldVal ?? cell.oldVal)
                                             : (cell.newVal ?? cell.newVal)
      
      if (acc[cell.rowIndex][`c${cell.colId}`] === undefined) {
        acc[cell.rowIndex][`c${cell.colId}`] = null
      }
      return acc
    }, {})
  }
  
  toDbUpdates (columns, isUndo = false) {
    return Object.entries(
      this.cells.reduce((acc, cell) => {
        const column = columns.find(col => col.id === cell.colId)
        if (column.calc) return acc
        
        if (!acc[cell.rowId]) acc[cell.rowId] = []
        const rawValue = isUndo ? cell.oldVal : cell.newVal
        
        // This should have already gone thru editor checks for either null or valid value
        const cleanValue = native(rawValue, column.type)
        
        acc[cell.rowId].push({
          columnId: cell.colId,
          rawValue,
          cleanValue,
          type: column.type
        })
        return acc
      }, {})
    ).map(([rowId, cells]) => ({
      rowId: Number(rowId),
      cells
    }))
  }
}

export class TableHistory {
  constructor () {
    this.operations = []
    this.currentIndex = -1
  }
  
  clear () {
    this.operations = []
    this.currentIndex = -1
  }
  
  get canUndo () {
    return this.currentIndex >= 0
  }
  
  get canRedo () {
    return this.currentIndex < this.operations.length - 1
  }
  
  add (operation) {
    // Remove any future operations if we're not at the end
    if (this.currentIndex < this.operations.length - 1) {
      this.operations = this.operations.slice(0, this.currentIndex + 1)
    }
    
    // Add new operation and maintain maximum size
    // console.log('addHistory:', operation.type, operation.timestamp, operation.cells)
    this.operations.push(operation)
    this.currentIndex++
    
    if (this.operations.length > MAX_HISTORY_SIZE) {
      this.operations = this.operations.slice(-MAX_HISTORY_SIZE)
      this.currentIndex = this.operations.length - 1
    }
  }
  
  async undo () {
    if (!this.canUndo) return null
    const operation = this.operations[this.currentIndex]
    this.currentIndex--
    return operation
  }
  
  async redo () {
    if (!this.canRedo) return null
    this.currentIndex++
    return this.operations[this.currentIndex]
  }
  
}

/**
 * Creates a TableOperation for clearing cells
 */
export function createClearOperation (rowData, colIdsToClear) {
  const historyCells = []
  if (!rowData.length || !colIdsToClear.length) return
  rowData.forEach(row => {
    colIdsToClear.forEach(colId => {
      if (row[`c${colId}`] !== undefined && row[`c${colId}`] !== null) {
        historyCells.push({
          rowId: row.id,
          rowIndex: row.pos,
          colId: colId,
          oldVal: row[`c${colId}`],
          newVal: null
        })
      }
    })
  })
  return new TableOperation('clear', historyCells)
}
