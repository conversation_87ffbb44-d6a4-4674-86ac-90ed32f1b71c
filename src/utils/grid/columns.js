import { getExcelColumnName } from '@/utils/helpers.js'
import { aggregates } from '@/utils/aggregation.js'
import { columnTypes } from '@/utils/formats.js'
import { getEditor, getRenderer } from '@/utils/colTypeComponents.js'

/**
 * Definition of the row drag column for ag-grid
 */
export const rowDragDef = {
  rowDrag: true,
  headerName: '',
  lockPosition: 'left',
  suppressNavigable: true,
  field: 'rowDrag',
  colId: 'rowDrag',
  width: 25,
  pinned: 'left',
  sortable: false,
  filter: false,
  resizable: false,
  editable: false,
  cellClass: 'ag-grid-auto-cell ag-grid-row-drag',
  colSpan: params => params.node.rowPinned ? 2 : 1,
  cellRenderer: params => params.node.rowPinned ? 'Σ' : ''
}

/**
 * Definition of the row position column for ag-grid
 */
export const rowPositionDef = {
  headerName: '',
  lockPosition: 'left',
  suppressNavigable: true,
  field: 'pos',  // we map row.pos to this value
  colId: 'pos',
  width: 30,
  pinned: 'left',
  editable: false,
  sortable: false,
  filter: false,
  resizable: false,
  cellClass: 'ag-grid-auto-cell ag-grid-row-pos',
  cellRenderer: params => params.value !== undefined ? params.value + 1 : '' // make it 1-indexed
}

/**
 * Returns true if the column is an auto column (rowDrag or rowPosition)
 */
export const colIsAuto = (colId) => colId === 'rowDrag' || colId === 'pos'

/**
 * Converts a column object to an ag-grid column definition
 */
export const colToColDef = function (col, index, interactive, hideFilter) {
  const editable = (interactive && col.calc === undefined)
  const colDef = {
    headerName: col.header || getExcelColumnName(index),
    field: col.field ?? 'c' + col.id,
    colId: col.id,
    width: col.width,
    flex: col.flex,
    filter: true,
    editable,
    pinned: col.pin === 'l' ? 'left' : (col.pin === 'r' ? 'right' : undefined),
    
    // we set every cell to object and customize formatter, parser and editor ourselves
    cellDataType: 'object',
    
    // We always stop reordering on all columns; mouse over a drag handle can enable it temporarily
    suppressMovable: (!interactive),
    
    // not standard ag-grid fields, but referenced later
    context: {
      type: col.type,
      props: col.props,
      aggregate: col.aggregate,
      hasFilter: col.filters?.length > 0,
      hideFilter: hideFilter
    },
    
    // define which cell renderer to choose
    cellRendererSelector: params => {
      
      let rendererParams = {
        colType: col.type,
        colProps: { ...col.props },
        value: params.value,
        error: true
      }
      if (params.node.rowPinned && aggregates[col.aggregate]) {
        const agg = aggregates[col.aggregate]
        if (agg.type) {
          rendererParams.colType = agg.type
          rendererParams.colProps = {}
        }
        rendererParams.aggregate = { ...agg, header: col.header }
      }
      
      // HACK the ? sometimes briefly on a view edit, colType can come thru as undefined -
      // couldn't track it down; this stops it breaking, there's a refreshed call to this with the right col...
      const nativeVal = columnTypes[rendererParams.colType]?.native(params.value)
      
      if (nativeVal !== null) {
        rendererParams.error = false
        rendererParams.value = nativeVal
      }
      return {
        component: getRenderer(rendererParams.colType),
        params: rendererParams
      }
    },
    
    // This is needed to prevent ag-grid warning :shrug: - it is never called
    // `AG Grid: Cell data type is "object" but no value parser has been provided. Please either provide an object data type definition with a value parser, or set "colDef.valueParser"`
    valueParser: () => undefined,
    
    // This can set a valueFormatted property that is passed through to the cell renderer
    // Right now we do all that in the renderer class itself
    valueFormatter: () => undefined
  }
  
  if (editable) {
    // define which cell editor to choose
    colDef.cellEditor = getEditor(col.type, col.props || {})
    colDef.cellEditorParams = {
      colType: col.type,
      colProps: col.props || {}
    }
  }
  return colDef
}
