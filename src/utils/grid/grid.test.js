import { describe, expect, it } from 'vitest'
import { calculatePasteRange, validatePasteSelection } from './grid.js'

describe('validatePasteSelection', () => {
  const pasteData = [
    ['a', 'b', 'c'],
    ['d', 'e', 'f']
  ]
  
  it('handles invalid paste data', () => {
    expect(validatePasteSelection({ pasteData: null })).toEqual({
      valid: false,
      message: 'No paste data provided'
    })
    
    expect(validatePasteSelection({ pasteData: [] })).toEqual({
      valid: false,
      message: 'No paste data provided'
    })
    
    expect(validatePasteSelection({ pasteData: [[]] })).toEqual({
      valid: false,
      message: 'No paste data provided'
    })
  })
  
  it('handles single cell focus with no selection', () => {
    const result = validatePasteSelection({
      pasteData,
      focusedCell: { colIndex: 1, rowIndex: 0 }
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [0, 1],
        cols: [1, 3]
      }
    })
  })
  
  it('handles single cell range selection', () => {
    const result = validatePasteSelection({
      pasteData,
      selectedCells: {
        rows: [5, 5],
        cols: [2, 2]
      }
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [5, 6],
        cols: [2, 4]
      }
    })
  })
  
  it('validates exact cell range selection', () => {
    const result = validatePasteSelection({
      pasteData,
      selectedCells: {
        rows: [0, 1],
        cols: [0, 2]
      }
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [0, 1],
        cols: [0, 2]
      }
    })
  })
  
  it('handles single column selection', () => {
    const result = validatePasteSelection({
      pasteData,
      selectedColumnIndexes: [1]
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [0, 1],
        cols: [1, 1]
      }
    })
  })
  
  it('validates exact column selection', () => {
    const result = validatePasteSelection({
      pasteData,
      selectedColumnIndexes: [1, 2, 3],
      focusedCell: { colIndex: 1, rowIndex: 0 }
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [0, 1],
        cols: [1, 3]
      }
    })
  })
  
  it('handles single row selection', () => {
    const result = validatePasteSelection({
      pasteData,
      selectedRowIndexes: [5]
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [5, 5],
        cols: [0, 2]
      }
    })
  })
  
  it('validates exact row selection', () => {
    const result = validatePasteSelection({
      pasteData,
      selectedRowIndexes: [3, 4]
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [3, 4],
        cols: [0, 2]
      }
    })
  })
  
  it('handles paste data larger than selection range', () => {
    const result = validatePasteSelection({
      pasteData: [
        ['a', 'b', 'c'],
        ['d', 'e', 'f']
      ],
      selectedCells: {
        rows: [0, 1],
        cols: [0, 1] // Only 2 columns selected but paste data has 3
      }
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [0, 1],
        cols: [0, 1]
      }
    })
  })
  
  it('handles paste data larger than column selection', () => {
    const result = validatePasteSelection({
      pasteData: [
        ['a', 'b', 'c'],
        ['d', 'e', 'f']
      ],
      selectedColumnIndexes: [1, 2],
      focusedCell: { colIndex: 1, rowIndex: 0 }
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [0, 1],
        cols: [1, 2]
      }
    })
  })
  
  it('handles paste data larger than row selection', () => {
    const result = validatePasteSelection({
      pasteData: [
        ['a', 'b', 'c'],
        ['d', 'e', 'f'],
        ['g', 'h', 'i']
      ],
      selectedRowIndexes: [3, 4],
      focusedCell: { colIndex: 1, rowIndex: 3 }
    })
    
    expect(result).toEqual({
      valid: true,
      range: {
        rows: [3, 4],
        cols: [0, 2]
      }
    })
  })
})

describe('calculatePasteRange', () => {
  it('calculates required new rows and columns', () => {
    const result = calculatePasteRange(
      {
        rows: [8, 10],
        cols: [3, 5]
      },
      10, // total rows
      4   // total columns
    )
    
    expect(result).toEqual({
      newRows: 1,     // Need 1 new row since paste ends at row 10
      newColumns: 2   // Need 2 new columns since paste ends at col 5
    })
  })
  
  it('returns zero when no new rows or columns needed', () => {
    const result = calculatePasteRange(
      {
        rows: [0, 2],
        cols: [0, 2]
      },
      5,  // total rows
      5   // total columns
    )
    
    expect(result).toEqual({
      newRows: 0,
      newColumns: 0
    })
  })
  
  it('handles paste range at grid boundaries', () => {
    const result = calculatePasteRange(
      {
        rows: [8, 9],
        cols: [3, 4]
      },
      10, // total rows
      5   // total columns
    )
    
    expect(result).toEqual({
      newRows: 0,
      newColumns: 0
    })
  })
})
