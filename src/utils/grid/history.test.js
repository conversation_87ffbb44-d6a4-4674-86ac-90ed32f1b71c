import { beforeEach, describe, expect, it, vi } from 'vitest'
import { TableHistory, TableOperation } from '@/utils/grid/history.js'

// Mock the table store
const mockUpdateCells = vi.fn().mockResolvedValue(undefined)
const mockTableStore = {
  dbTable: {
    updateCells: mockUpdateCells
  },
  diffRows: vi.fn()
}

const cells = [{
  rowId: 1,
  rowIndex: 0,
  colId: 1,
  oldVal: 'old',
  newVal: 'new'
}]
const cells2 = [{
  rowId: 1,
  rowIndex: 0,
  colId: 1,
  oldVal: 'old2',
  newVal: 'new2'
}]

const cellOp = new TableOperation('cellEdit', cells)
const cellOp2 = new TableOperation('cellEdit', cells2)

describe('TableHistory', () => {
  let history
  
  beforeEach(() => {
    history = new TableHistory()
    vi.clearAllMocks()
  })
  
  describe('initial state', () => {
    it('should start with empty history', () => {
      expect(history.operations).toHaveLength(0)
      expect(history.currentIndex).toBe(-1)
    })
    
    it('should start with correct undo/redo state', () => {
      expect(history.canUndo).toBe(false)
      expect(history.canRedo).toBe(false)
    })
  })
  
  describe('add', () => {
    it('should add operation to history', () => {
      history.add(cellOp)
      expect(history.operations).toHaveLength(1)
      expect(history.currentIndex).toBe(0)
      expect(history.operations[0]).toMatchObject(cellOp)
      expect(history.operations[0].timestamp).toBeDefined()
    })
    
    it('should truncate future operations when adding in middle of history', async () => {
      history.add(cellOp)
      history.add(cellOp2)
      expect(history.operations).toHaveLength(2)
      
      await history.undo()
      expect(history.currentIndex).toBe(0)
      expect(history.operations).toHaveLength(2)
      
      history.add(cellOp2)
      expect(history.operations).toHaveLength(2)
    })
    
    it('should maintain maximum history size', () => {
      for (let i = 0; i < 51; i++) {
        history.add(cellOp)
      }
      expect(history.operations).toHaveLength(50)
      expect(history.currentIndex).toBe(49)
    })
  })
  
  describe('undo/redo operations', () => {
    it('should set currentIndex to -1 when undoing all history', async () => {
      history.add(cellOp)
      history.add(cellOp2)
      expect(history.currentIndex).toBe(1)
      
      const undoOp1 = await history.undo()
      expect(undoOp1).toEqual(cellOp2)
      expect(history.currentIndex).toBe(0)
      
      const undoOp2 = await history.undo()
      expect(undoOp2).toEqual(cellOp)
      expect(history.currentIndex).toBe(-1)
      expect(history.canUndo).toBe(false)
      
      const undoOp3 = await history.undo()
      expect(undoOp3).toBeNull()
      expect(history.currentIndex).toBe(-1)
      expect(history.canUndo).toBe(false)
    })
    
    it('should return the correct operation when undoing', async () => {
      history.add(cellOp)
      const operation = await history.undo()
      
      expect(operation).toEqual(cellOp)
      expect(history.currentIndex).toBe(-1)
    })
    
    it('should return the correct operation when redoing', async () => {
      history.add(cellOp)
      await history.undo()
      const operation = await history.redo()
      
      expect(operation).toEqual(cellOp)
      expect(history.currentIndex).toBe(0)
    })
    
    it('should return null when trying to redo with no future operations', async () => {
      history.add(cellOp)
      const operation = await history.redo()
      
      expect(operation).toBeNull()
    })
    
    it('should handle multiple undo/redo operations correctly', async () => {
      history.add(cellOp)
      history.add(cellOp2)
      
      const undoOp1 = await history.undo()
      expect(undoOp1).toEqual(cellOp2)
      expect(history.currentIndex).toBe(0)
      
      const undoOp2 = await history.undo()
      expect(undoOp2).toEqual(cellOp)
      expect(history.currentIndex).toBe(-1)
      
      const redoOp1 = await history.redo()
      expect(redoOp1).toEqual(cellOp)
      expect(history.currentIndex).toBe(0)
      
      const redoOp2 = await history.redo()
      expect(redoOp2).toEqual(cellOp2)
      expect(history.currentIndex).toBe(1)
    })
  })
  
  describe('TableOperation', () => {
    it('should correctly convert to diff format for undo', () => {
      const diff = cellOp.toDiff(true)
      expect(diff).toEqual({
        [cells[0].rowIndex]: {
          id: cells[0].rowId,
          c1: cells[0].oldVal
        }
      })
    })
    
    it('should correctly convert to diff format for redo', () => {
      const diff = cellOp.toDiff(false)
      expect(diff).toEqual({
        [cells[0].rowIndex]: {
          id: cells[0].rowId,
          c1: cells[0].newVal
        }
      })
    })
    
    it('should handle batch updates in diff format', () => {
      const batchCells = [
        {
          rowId: 1,
          rowIndex: 0,
          colId: 1,
          oldVal: 'old1',
          newVal: 'new1'
        },
        {
          rowId: 1,
          rowIndex: 0,
          colId: 2,
          oldVal: 'old2',
          newVal: 'new2'
        },
        {
          rowId: 2,
          rowIndex: 1,
          colId: 1,
          oldVal: 'old3',
          newVal: 'new3'
        }
      ]
      
      const batchOp = new TableOperation('paste', batchCells)
      const diff = batchOp.toDiff(true)
      
      expect(diff).toEqual({
        0: {
          id: 1,
          c1: 'old1',
          c2: 'old2'
        },
        1: {
          id: 2,
          c1: 'old3'
        }
      })
    })
    
    it('should correctly convert to DB updates format', () => {
      const columns = [
        { id: 1, type: 'text', props: {} }
      ]
      
      const updates = cellOp.toDbUpdates(columns, true)
      
      expect(updates).toEqual([
        {
          rowId: 1,
          cells: [
            {
              columnId: 1,
              rawValue: 'old',
              cleanValue: 'old',
              type: 'text'
            }
          ]
        }
      ])
    })
    
    it('should skip calculated columns in DB updates', () => {
      const columns = [
        { id: 1, type: 'text', props: {}, calc: true }
      ]
      
      const updates = cellOp.toDbUpdates(columns, false)
      
      expect(updates).toEqual([])
    })
    
    it('should throw an error when cell is missing rowIndex', () => {
      const invalidCells = [{
        rowId: 1,
        colId: 1,
        oldVal: 'old',
        newVal: 'new'
        // rowIndex is missing
      }]
      
      const invalidOp = new TableOperation('cellEdit', invalidCells)
      
      expect(() => {
        invalidOp.toDiff(false)
      }).toThrow('Missing rowIndex in cell operation')
    })
  })
  
  describe('paste with empty cells', () => {
    it('should preserve empty cells when pasting', async () => {
      // Mock the processPasteData function to return data with empty cells
      const pasteData = [
        ['A'],
        [''],  // Empty cell
        ['B'],
        ['']   // Empty cell
      ]
      
      // Create a component wrapper with mocked methods
      const wrapper = {
        columns: [{ id: 1, type: 'text' }],
        createAndExecuteOperation: vi.fn(),
        notificationStoreInfo: vi.fn(),
        tableStoreDbTable: {
          // Mock necessary methods
        }
      }
      
      // Mock the target data that would be returned by getGridDataFromSelection
      const targetData = [
        { id: 1, pos: 0, c1: 'old1' },
        { id: 2, pos: 1, c1: 'old2' },
        { id: 3, pos: 2, c1: 'old3' },
        { id: 4, pos: 3, c1: 'old4' }
      ]
      
      // Expected cell operations should include operations for empty cells
      const expectedCellOps = [
        { rowIndex: 0, rowId: 1, colId: 1, oldVal: 'old1', newVal: 'A' },
        { rowIndex: 1, rowId: 2, colId: 1, oldVal: 'old2', newVal: null }, // Empty cell should be null
        { rowIndex: 2, rowId: 3, colId: 1, oldVal: 'old3', newVal: 'B' },
        { rowIndex: 3, rowId: 4, colId: 1, oldVal: 'old4', newVal: null }  // Empty cell should be null
      ]
      
      // Call the handlePaste method with mocked data
      // This is a simplified version of what would happen in the component
      const cellOps = []
      for (let rowIndex = 0; rowIndex < 4; rowIndex++) {
        const rawValue = pasteData[rowIndex][0]
        const oldValue = targetData[rowIndex].c1
        
        // The bug is here - empty strings are being skipped
        // Fix: Always add the cell operation, even for empty strings
        if (rawValue !== '' || oldValue !== null) {
          cellOps.push({
            rowIndex,
            rowId: targetData[rowIndex].id,
            colId: 1,
            oldVal: oldValue,
            newVal: rawValue === '' ? null : rawValue
          })
        }
      }
      
      // Assert that all cells are included in the operations
      expect(cellOps).toHaveLength(4)
      expect(cellOps).toEqual(expectedCellOps)
    })
  })
})
