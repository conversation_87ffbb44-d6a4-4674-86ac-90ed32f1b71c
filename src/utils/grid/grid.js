import { range } from '@/utils/helpers.js'
import { repr } from '@/utils/formats.js'
import { createClearOperation } from '@/utils/grid/history.js'
import { filterQuerySpec } from '@/db/query/utils.js'

/**
 * Returns an array of column IDs for calculated columns
 */
const getCalcColIds = (columns) => {
  return columns.filter(col => col.calc).map(col => col.id)
}

/**
 *
 * Returns data for the given columnIds.
 *
 * Unless skipHeaders is true, the first row will be column headers if there's any data.
 */
async function getColumnSelectionData ({ columnIds, columns, dbTable, querySpec, skipHeaders, asObjects = false }) {
  const headers = columns.filter(col => columnIds.includes(col.id)).map(col => col.header)
  const data = asObjects
               ? await dbTable.rowObjects(columnIds, querySpec)
               : await dbTable.rowArray(columnIds, querySpec, true)
  
  if (data.length && !skipHeaders) {
    if (asObjects) {
      throw new Error('getColumnSelectionData: Cannot return objects with headers; not implemented')
    }
    data.unshift(headers)
  }
  return data
}

/**
 * Returns data for the given rowIds.
 */
async function getRowSelectionData ({ rowIds, posRange, columns, dbTable, querySpec, asObjects = false }) {
  const spec = filterQuerySpec(querySpec, { rowIds, posRange })
  const colIds = columns.map(col => col.id)
  return asObjects
         ? await dbTable.rowObjects(colIds, spec)
         : await dbTable.rowArray(colIds, spec, true)
}

/**
 * Returns data for the given cell range.
 *
 * The range is specified as a range of column indexes and a range of row indexes.
 *
 * The range is inclusive on both ends.
 */
async function getRangeSelectionData ({ cellRange, columns, dbTable, querySpec, asObjects = false }) {
  
  // Given the range of rows, look up their ids, with column filtering applied
  const posRange = {
    from: cellRange.rows[0],
    to: cellRange.rows[1]
  }
  const rowIds = await dbTable.rowIds(posRange, querySpec)
  const spec = filterQuerySpec(querySpec, { rowIds })
  const colIds = range(...cellRange.cols).map(colIndex => columns[colIndex]).map(col => col.id)
  return (asObjects)
         ? await dbTable.rowObjects(colIds, spec)
         : await dbTable.rowArray(colIds, spec, true)
}

/**
 * Returns data for the given cell.
 *
 * If the cell is in the pinned row, it will return the aggregate value for the column.
 */
async function getCellSelectionData ({ cell, columns, dbTable, querySpec, asObjects = false }) {
  const column = columns.find(col => col.id === cell.colId)
  if (cell.rowPinned === 'bottom') {
    if (asObjects) {
      throw new Error('getCellSelectionData: Cannot return objects for pinned rows; not implemented')
    }
    const row = await dbTable.aggregate(column.id, [column.aggregate], querySpec)
    const v = row[column.aggregate]
    return [[repr(v, column.type, column.props) || v]]
  }
  const spec = filterQuerySpec(querySpec, { rowIds: [cell.rowId] })
  return (asObjects)
         ? await dbTable.rowObjects([column.id], spec)
         : await dbTable.rowArray([column.id], spec, true)
}

/**
 * Returns data for the current selection state.
 *
 * If there is no selection, returns an empty array.
 * If there is a column selection, returns data for all rows in the selected columns.
 * If there is a row selection, returns data for all columns in the selected rows.
 * If there is a cell range selection, returns data for the selected cells.
 *
 * **/
export async function getGridDataFromSelection ({
  columnIds,
  rowIds,
  cellRange,
  cell,
  columns,
  dbTable,
  querySpec,
  asObjects = false,
  skipHeaders = false
}) {
  if (columnIds?.length) {
    return getColumnSelectionData({ columnIds, columns, dbTable, querySpec, skipHeaders, asObjects })
  } else if (rowIds?.length) {
    return getRowSelectionData({ rowIds, columns, dbTable, querySpec, asObjects })
  } else if (cellRange !== undefined && cellRange !== null) {
    return getRangeSelectionData({ cellRange, columns, dbTable, querySpec, asObjects })
  } else if (cell !== undefined) {
    return getCellSelectionData({ cell, columns, dbTable, querySpec, asObjects })
  } else {
    return []
  }
}

/**
 * Clears values in selected columns without removing the columns.
 * Returns the table operation.
 */
async function clearCellsInColumns ({ columnIds, columns, dbTable, querySpec }) {
  const calcColIds = getCalcColIds(columns)
  columnIds = columnIds.filter(id => !calcColIds.includes(id))
  if (columnIds.length === 0) return
  const rowData = await getColumnSelectionData({
    columnIds, columns, dbTable, querySpec, skipHeaders: true, asObjects: true
  })
  return createClearOperation(rowData, columnIds)
}

/**
 * Clears values in a selected range of cells.
 * Returns the table operation.
 */
async function clearCellsInRange ({ cellRange, columns, dbTable, querySpec }) {
  const rowData = await getRangeSelectionData({ cellRange, columns, dbTable, querySpec, asObjects: true })
  
  const calcColIds = getCalcColIds(columns)
  const columnIds = range(...cellRange.cols)
    .map(colIndex => columns[colIndex].id)
    .filter(id => !calcColIds.includes(id))
  
  return createClearOperation(rowData, columnIds)
}

/**
 * Clears values in selected rows without removing the rows.
 * Returns the table operation.
 */
async function clearCellsInRows ({ rowIds, columns, dbTable, querySpec }) {
  const calcColIds = getCalcColIds(columns)
  const rowData = await getRowSelectionData({ rowIds, columns, dbTable, querySpec, asObjects: true })
  const columnIds = columns.filter(col => !calcColIds.includes(col.id)).map(col => col.id)
  return createClearOperation(rowData, columnIds)
}

/**
 * Clears values in a single cell.
 * Returns the table operation.
 */
async function clearCell ({ cell, columns, dbTable, querySpec }) {
  const column = columns.find(col => col.id === cell.colId)
  const rowData = await getCellSelectionData({ cell, columns, dbTable, querySpec, asObjects: true })
  return createClearOperation(rowData, [column.id])
}

/**
 * Clears values in the current selection state.
 * Returns the table operation.
 *
 * If there is no selection, returns an empty array.
 * If there is a column selection, returns data for all rows in the selected columns.
 * If there is a row selection, returns data for all columns in the selected rows.
 * If there is a cell range selection, returns data for the selected cells.
 *
 * Note: This doesn't take a view, because you can't clear data from views.
 * **/
export async function clearCellsFromSelection ({
  columnIds,
  rowIds,
  cellRange,
  cell,
  columns,
  dbTable,
  querySpec
}) {
  if (columnIds?.length) {
    return await clearCellsInColumns({ columnIds, columns, dbTable, querySpec })
    
  } else if (rowIds?.length) {
    return await clearCellsInRows({ rowIds, columns, dbTable, querySpec })
    
  } else if (cellRange) {
    return await clearCellsInRange({ cellRange, columns, dbTable, querySpec })
    
  } else if (cell) {
    return await clearCell({ cell, columns, dbTable, querySpec })
    
  }
}

function isContiguous (indexes) {
  for (let i = 1; i < indexes.length; i++) {
    if (indexes[i] !== indexes[i - 1] + 1) {
      return false
    }
  }
  return true
}

/**
 * Validates if paste data can be applied to the current selection state
 * @param {Array<Array<string>>} pasteData - 2D array of paste data
 * @param {Object} focusedCell - { colIndex: number, rowIndex: number }
 * @param {Array<number>} selectedColumnIndexes - Array of selected column indexes
 * @param {Array<number>} selectedRowIndexes - Array of selected row indexes
 * @param {Object} selectedCells - { rows: [startRow, endRow], cols: [startCol, endCol] }
 * @returns {Object} - { valid: boolean, message?: string, range?: { rows: [start, end], cols: [start, end] } }
 */
export function validatePasteSelection ({
  pasteData,
  focusedCell,
  selectedColumnIndexes = [],
  selectedRowIndexes = [],
  selectedCells = null
}) {
  // Validate paste data exists and has content
  if (!pasteData?.length || !pasteData[0]?.length) {
    return {
      valid: false,
      message: 'No paste data provided'
    }
  }
  
  const pasteRows = pasteData.length
  const pasteCols = pasteData[0].length
  
  if (selectedCells) {
    // For single cell target, allow paste expansion
    if (selectedCells.cols[0] === selectedCells.cols[1] &&
      selectedCells.rows[0] === selectedCells.rows[1]) {
      return {
        valid: true,
        range: {
          rows: [selectedCells.rows[0], selectedCells.rows[0] + pasteRows - 1],
          cols: [selectedCells.cols[0], selectedCells.cols[0] + pasteCols - 1]
        }
      }
    }
    
    // For range selection, limit paste to selection bounds
    const selectionRows = selectedCells.rows[1] - selectedCells.rows[0] + 1
    const selectionCols = selectedCells.cols[1] - selectedCells.cols[0] + 1
    
    return {
      valid: true,
      range: {
        rows: [selectedCells.rows[0], selectedCells.rows[0] + Math.min(pasteRows, selectionRows) - 1],
        cols: [selectedCells.cols[0], selectedCells.cols[0] + Math.min(pasteCols, selectionCols) - 1]
      }
    }
  } else if (selectedColumnIndexes.length) {
    // Only permit contiguous column selection
    if (!isContiguous(selectedColumnIndexes)) {
      return {
        valid: false,
        message: 'Selected columns must be contiguous for paste operation'
      }
    }
    
    // Limit paste to selected columns
    return {
      valid: true,
      range: {
        rows: [0, pasteRows - 1],
        cols: [
          Math.min(...selectedColumnIndexes),
          Math.min(...selectedColumnIndexes) + Math.min(pasteCols, selectedColumnIndexes.length) - 1
        ]
      }
    }
    
  } else if (selectedRowIndexes.length) {
    // Make sure rows are contiguous
    if (!isContiguous(selectedRowIndexes)) {
      return {
        valid: false,
        message: 'Selected rows must be contiguous for paste operation'
      }
    }
    
    // Limit paste to selected rows
    return {
      valid: true,
      range: {
        rows: [
          Math.min(...selectedRowIndexes),
          Math.min(...selectedRowIndexes) + Math.min(pasteRows, selectedRowIndexes.length) - 1
        ],
        cols: [0, pasteCols - 1]
      }
    }
    
  } else if (focusedCell) {
    return {
      valid: true,
      range: {
        rows: [focusedCell.rowIndex, focusedCell.rowIndex + pasteRows - 1],
        cols: [focusedCell.colIndex, focusedCell.colIndex + pasteCols - 1]
      }
    }
  }
  
  return {
    valid: false,
    message: 'No valid selection for paste operation'
  }
}

/**
 * Calculates if new rows/columns are needed for paste operation
 * @param {Object} range - { rows: [start, end], cols: [start, end] }
 * @param {number} totalRows - Total number of rows in grid
 * @param {number} totalColumns - Total number of columns in grid
 * @returns {Object} - { newRows: number, newColumns: number }
 */
export function calculatePasteRange (range, totalRows, totalColumns) {
  return {
    newRows: Math.max(0, range.rows[1] - totalRows + 1),
    newColumns: Math.max(0, range.cols[1] - totalColumns + 1)
  }
}
