import Papa from 'papaparse'

/*
Each output format expects either:
- data: an array of arrays, where each object represents a row in the table and each item a cell in the row

or:
- header: an array of strings, where each string represents a column name
- rows: an array of arrays, where each array represents a row in the table

 */
export const formats = {
  csv: {
    name: 'CSV',
    output: ({ data }) => {
      // https://www.papaparse.com/docs#json-to-csv
      return Papa.unparse(data)
    },
    extension: 'csv'
  },
  tsv: {
    name: 'TSV',
    output: ({ data }) => {
      return Papa.unparse(data, { delimiter: '\t' })
    },
    extension: 'tsv'
  },
  json: {
    name: 'JSON',
    output: ({ data }) => {
      return JSON.stringify(data, null, 2)
    },
    extension: 'json'
  },
  html: {
    name: 'HTML',
    extension: 'html',
    output: ({ header, rows }) => {
      const th = header.map(h => `\t\t<th>${h}</th>`).join('\n')
      const trs = rows.map(row => `\t<tr>\n${row.map(cell => `\t\t<td>${cell}</td>`).join('\n')}\n\t</tr>`).join('\n')
      return `<table>
  <thead>
${th}
  </thead>
  <tbody>
${trs}
  </tbody>
</table>`
    }
  },
  markdown: {
    name: 'Markdown',
    extension: 'md',
    output: ({ header, rows }) => {
      const th = header.map(h => ` ${h} `).join('|')
      const trs = rows.map(row => `| ${row.join(' | ')} |`).join('\n')
      return `| ${th} |
| ${header.map(() => '---').join(' | ')} |
${trs}`
    }
  },
  textile: {
    name: 'Textile',
    extension: 'txt',
    output: ({ header, rows }) => {
      const th = header.map(h => `_. ${h} `).join('|')
      const trs = rows.map(row => `| ${row.join(' | ')} |`).join('\n')
      return `|^.
|${th}|
|-.
${trs}`
    }
  }
}
