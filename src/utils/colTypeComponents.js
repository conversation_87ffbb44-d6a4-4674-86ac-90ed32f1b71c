/*
This works alongside formats.columnTypes, returning Vue components for each column type.

We split this out of formats.js so formats.js doesn't rely on any Vue code. That makes doing
embed.js easier, which relies on colTypes (but not on the Vue stuff).
*/

import CellEditor from '@/components/table/editors/TextCellEditor.vue'
import PercentCellEditor from '@/components/table/editors/PercentCellEditor.vue'
import NumberCellEditor from '@/components/table/editors/NumberCellEditor.vue'
import DateTimeCellEditor from '@/components/table/editors/DateTimeCellEditor.vue'
import BooleanCellEditor from '@/components/table/editors/BooleanCellEditor.vue'

import NumberCellRenderer from '@/components/table/renderers/NumberCellRenderer.vue'
import BooleanCellRenderer from '@/components/table/renderers/BooleanCellRenderer.vue'
import CurrencyCellRenderer from '@/components/table/renderers/CurrencyCellRenderer.vue'
import TextCellRenderer from '@/components/table/renderers/TextCellRenderer.vue'
import DateTimeCellRenderer from '@/components/table/renderers/DateTimeCellRenderer.vue'

import { Banknote, CalendarClock, Hash, LetterText, Percent, ToggleLeft } from 'lucide-vue-next'

export const getColumnIcon = (type) => {
  switch (type) {
    case 'currency':
      return Banknote
    case 'datetime':
      return CalendarClock
    case 'boolean':
      return ToggleLeft
    case 'number':
      return Hash
    case 'percent':
      return Percent
    case 'text':
    default:
      return LetterText
  }
}

export const getEditor = (type, props) => {
  switch (type) {
    case 'number':
      return NumberCellEditor
    
    case 'percent':
      return PercentCellEditor
    
    case 'currency':
      return NumberCellEditor
    
    case 'datetime':
      return DateTimeCellEditor
    
    case 'boolean':
      return BooleanCellEditor
    
    default:
      return CellEditor
  }
}

export const getRenderer = (type) => {
  switch (type) {
    case 'boolean':
      return BooleanCellRenderer
    
    case 'currency':
      return CurrencyCellRenderer
    
    case 'text':
      return TextCellRenderer
    
    case 'datetime':
      return DateTimeCellRenderer
    
    default:
      return NumberCellRenderer
  }
}
