import { ApiDebounceQueue } from '@/utils/api/apiQueue.js'
import { useProjectsStore } from '@/stores/projects.js'
import equal from 'fast-deep-equal'
import { twoLevelMerge } from '@/utils/helpers.js'

export class TableQueue extends ApiDebounceQueue {
  
  constructor (id, delay = 3000) {
    const api = {
      updateTable: {
        uri: `tables/${id}/`,
        after: async (data) => {
          if (data.title || data.project) {
            await useProjectsStore().reload()
          }
        }
      },
      updateRows: {
        uri: `tables/${id}/update-rows/`,
        before: (data) => {
          // wrap data in a { diff: data } object
          return { diff: data }
        }
      },
      insertRows: `tables/${id}/insert-rows/`,
      moveRows: `tables/${id}/move-rows/`,
      deleteRows: `tables/${id}/delete-rows/`,
      deleteColumns: `tables/${id}/delete-columns/`,
      updateColumn: {
        // note we use id here, not this.id, so it is not evaluated on the function call.
        // At that point, {this} might have changed to another table.
        uri: (kwargs) => `tables/${id}/update-column/${kwargs.id}/`
      },
      clearColumnAggregate: {
        uri: (kwargs) => `tables/${id}/clear-column-aggregate/${kwargs.id}/`
      },
      updateView: {
        uri: (kwargs) => `tableviews/${kwargs.viewId}/`
      },
      updateViewColumn: {
        uri: (kwargs) => `tableviews/${kwargs.viewId}/update-column/${kwargs.id}/`
      },
      updateViewGroup: {
        uri: (kwargs) => `tableviews/${kwargs.viewId}/update-group/${kwargs.id}/`
      }
    }
    super(api, delay, false)
  }
  
  _prepareQueue () {
    /* Specifically for 'patch' calls - save to columns, title or project - combine them
    together into a single request. */
    const newQueue = []
    let last = {}
    
    const squashables = ['updateTable', 'updateRows']
    
    while (this.queue.length > 0) {
      const current = this.queue.shift()
      if (squashables.includes(current.key) && last.key === current.key && equal(last.kwargs, current.kwargs)) {
        last.data = twoLevelMerge(last.data, current.data)
        
      } else {
        if (Object.keys(last).length > 0) {
          newQueue.push(last)
        }
        last = current
      }
    }
    newQueue.push(last)
    this.queue = newQueue
  }
}
