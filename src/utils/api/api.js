import axios from 'axios'

const apiUrl = import.meta.env.VITE_API_URL

export const api = {
  async get (endPoint, data) {
    return axios.get(apiUrl + endPoint, data)
  },
  
  async post (endPoint, data, headers) {
    return axios.post(apiUrl + endPoint, data, { headers })
  },
  
  async patch (endPoint, data, headers) {
    return axios.patch(apiUrl + endPoint, data, { headers })
  },
  
  async delete (endPoint, headers) {
    return axios.delete(apiUrl + endPoint, { headers })
  }
}
