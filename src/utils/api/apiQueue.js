import { useAuthStore } from '@/stores/auth.js'
import { useNotificationStore } from '@/stores/notifications.js'

export class DebounceQueue {
  constructor (delay = 3000, async = false) {
    this.queue = []
    this.delay = delay
    this.timeoutId = null
    this.async = async
  }
  
  add (operation) {
    this.queue.push(operation)
    this._process()
  }
  
  async flush () {
    return await this._flush()
  }
  
  async kill () {
    // kills the queue, ends the timer - prevents all execution
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }
    this.queue = []
  }
  
  _process () {
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId)
    }
    
    if (this.queue.length > 0) {
      this.timeoutId = setTimeout(() => {
        this._flush()
      }, this.delay)
    }
  }
  
  _prepareQueue () {
    // This function can be overwritten by subclasses to prepare the queue
    // before processing it
  }
  
  _getFunc (op) {
    // By default, operations are the functions themselves
    return op
  }
  
  async _flush () {
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }
    if (this.queue.length === 0) {
      return
    }
    
    const notificationStore = useNotificationStore()
    const notificationId = notificationStore.start('Saving...')
    this._prepareQueue()
    const promises = []
    while (this.queue.length > 0) {
      const func = this._getFunc(this.queue.shift())
      if (this.async) {
        promises.push(func())
      } else {
        await func()
      }
    }
    
    if (this.async) {
      await Promise.all(promises)
    }
    
    // Complete the notification with success
    notificationStore.complete(notificationId, 'Saved!', true)
  }
}

export class ApiDebounceQueue extends DebounceQueue {
  /*
  The ApiDebounceQueue is a subclass of DebounceQueue that is specifically
  designed to handle various API requests.
  */
  constructor (api, delay = 3000, async = false) {
    super(delay, async)
    this.api = api
  }
  
  add (key, data, kwargs = {}) {
    this.queue.push({ key, data, kwargs })
    this._process()
  }
  
  _getFunc (op) {
    return async () => {
      let { key, data, kwargs } = op
      let api = this.api[key]
      
      // Support bare strings; make that the api uri
      if (typeof api === 'string') {
        api = { uri: api }
      }
      
      if (api.before) {
        data = api.before(data)
      }
      await useAuthStore().patch(this._getUri(api.uri, kwargs), data)
      if (api.after) {
        await api.after(data)
      }
    }
  }
  
  _getUri (uri, kwargs) {
    // If uri is a function, call it with kwargs
    if (typeof uri === 'function') {
      return uri(kwargs)
    }
    return uri
  }
  
}
