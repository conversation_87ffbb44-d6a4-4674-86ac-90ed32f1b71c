import { describe, expect, it } from 'vitest'

import { detectColumn, detectColumns } from './detect'

describe('detectColumns', () => {
  it('Should detect Yen currency', () => {
    const colData = ['¥2,024,000', '¥800,000', '¥1,000,000', '¥1,500,000', '¥2,000,000', '¥1,000,000', '¥1,500,000', '¥5,000,000', '¥8,000,000', '¥1,500,000', '¥800,000', '¥1,000,000', '¥1,000,000', '¥1,000,000', '¥1,500,000', '¥2,000,000', '¥10,000,000', '¥1,000,000', '¥1,000,000', '¥2,000,000', '¥1,500,000', '¥2,000,000', '¥2,500,000', '¥3,500,000', '¥20,000,000', '¥60,000', '¥30,000', '¥88,000', '¥60,000', '¥88,000', '¥70,000', '¥50,000', '¥60,000', '¥35,000', '¥150,000', '¥150,000', '¥60,000', '¥200,000', '¥70,000', '¥200,000', '¥100,000 ']
    const colDef = detectColumn(colData)
    expect(colDef.type).toBe('currency')
    expect(colDef.props?.currency).toBe('JPY')
  })
  
  it('Should have no hidden columns', () => {
    const data = `Fruit\tCost\tStock\tMore like veg?\tLast time purchased
Apples\t£1.20\t5\t\t2024-02-16
Pear\t£1.25\t6\t\t2024-02-09
Orange\t£0.85\t12\t\t2024-02-10
Carrot\t£0.55\t5\tTrue\t2024-02-06
Satsuma\t£0.75\t13\t\t
Tomato\t£0.50\t5\tTrue\t2024-02-01`
    const arr = data.split('\n').map(row => row.split('\t'))
    const colDefs = detectColumns(arr)
    expect(colDefs.every(col => !col.hide)).toBe(true)
  })
  
  it('Should have two hidden cols', () => {
    const data = `Fruit\tEmpty\tStock\t\tLast time purchased
Apples\t\t5\t\t2024-02-16
Pear\t\t6\t\t2024-02-09
Orange\t\t12\t\t2024-02-10
Carrot\t\t5\t\t2024-02-06
Satsuma\t\t13
Tomato\t\t5\t\t2024-02-01`
    const arr = data.split('\n').map(row => row.split('\t'))
    const colDefs = detectColumns(arr)
    expect(colDefs.filter(col => col.hide).length).toBe(2)
    expect(colDefs[1].hide).toBe(true)
    expect(colDefs[3].hide).toBe(true)
  })
})
