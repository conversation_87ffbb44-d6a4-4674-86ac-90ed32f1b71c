// redo the above but as value/label objects
export const currencyDisplayOptions = [
  {
    value: 'symbol',
    label: 'Symbol'
  },
  {
    value: 'narrowSymbol',
    label: 'Narrow Symbol'
  },
  {
    value: 'code',
    label: 'Code'
  },
  {
    value: 'name',
    label: 'Name'
  }
]
export const currencies = [
  {
    locale: 'en-US',
    currency: 'USD',
    name: 'US Dollar',
    symbol: '$'
  },
  {
    locale: 'en-GB',
    currency: 'GBP',
    name: 'British Pound',
    symbol: '£'
  },
  {
    locale: 'en-IE',
    currency: 'EUR',
    name: 'Euro (International)',
    symbol: '€'
  },
  {
    locale: 'ja-JP',
    currency: 'JPY',
    name: 'Japanese Yen',
    symbol: '¥'
  },
  {
    locale: 'ko-KR',
    currency: 'KRW',
    name: 'South Korean Won',
    symbol: '₩'
  },
  {
    locale: 'zh-CN',
    currency: 'CNY',
    name: 'Chinese Yuan',
    symbol: '¥'
  },
  {
    locale: 'zh-TW',
    currency: 'TWD',
    name: 'New Taiwan Dollar',
    symbol: '$'
  },
  {
    locale: 'ru-RU',
    currency: 'RUB',
    name: 'Russian Ruble',
    symbol: '₽'
  },
  {
    locale: 'pt-BR',
    currency: 'BRL',
    name: 'Brazilian Real',
    symbol: 'R$'
  },
  {
    locale: 'hi-IN',
    currency: 'INR',
    name: 'Indian Rupee',
    symbol: '₹'
  },
  /*
  Can't just strip at start and end due to brackets, %s etc.
  Need to instead scape each one (and consider the $ sign!) and match exact strings.
  {
    locale: 'ar-SA',
    currency: 'SAR',
    name: 'Saudi Riyal',
    symbol: 'ر.س'
  }, */
  {
    locale: 'tr-TR',
    currency: 'TRY',
    name: 'Turkish Lira',
    symbol: '₺'
  },
  {
    locale: 'pl-PL',
    currency: 'PLN',
    name: 'Polish Zloty',
    symbol: 'zł'
  },
  {
    locale: 'vi-VN',
    currency: 'VND',
    name: 'Vietnamese Dong',
    symbol: '₫'
  },
  {
    locale: 'th-TH',
    currency: 'THB',
    name: 'Thai Baht',
    symbol: '฿'
  },
  {
    locale: 'id-ID',
    currency: 'IDR',
    name: 'Indonesian Rupiah',
    symbol: 'Rp'
  },
  {
    locale: 'ms-MY',
    currency: 'MYR',
    name: 'Malaysian Ringgit',
    symbol: 'RM'
  },
  {
    locale: 'fil-PH',
    currency: 'PHP',
    name: 'Philippine Peso',
    symbol: '₱'
  },
  {
    locale: 'hu-HU',
    currency: 'HUF',
    name: 'Hungarian Forint',
    symbol: 'Ft'
  },
  {
    locale: 'cs-CZ',
    currency: 'CZK',
    name: 'Czech Koruna',
    symbol: 'Kč'
  },
  {
    locale: 'da-DK',
    currency: 'DKK',
    name: 'Danish Krone',
    symbol: 'kr'
  },
  {
    locale: 'nb-NO',
    currency: 'NOK',
    name: 'Norwegian Krone',
    symbol: 'kr'
  },
  {
    locale: 'sv-SE',
    currency: 'SEK',
    name: 'Swedish Krona',
    symbol: 'kr'
  },
  {
    locale: 'hr-HR',
    currency: 'HRK',
    name: 'Croatian Kuna',
    symbol: 'kn'
  },
  {
    locale: 'ro-RO',
    currency: 'RON',
    name: 'Romanian Leu',
    symbol: 'lei'
  },
  {
    locale: 'uk-UA',
    currency: 'UAH',
    name: 'Ukrainian Hryvnia',
    symbol: '₴'
  },
  {
    locale: 'he-IL',
    currency: 'ILS',
    name: 'Israeli New Shekel',
    symbol: '₪'
  },
  {
    locale: 'bg-BG',
    currency: 'BGN',
    name: 'Bulgarian Lev',
    symbol: 'лв'
    
  },
  {
    locale: 'ar-EG',
    currency: 'EGP',
    name: 'Egyptian Pound',
    symbol: 'E£'
  },
  {
    locale: 'af-ZA',
    currency: 'ZAR',
    name: 'South African Rand',
    symbol: 'R'
  },
  {
    locale: 'en-AU',
    currency: 'AUD',
    name: 'Australian Dollar',
    symbol: '$'
  },
  {
    locale: 'en-NZ',
    currency: 'NZD',
    name: 'New Zealand Dollar',
    symbol: '$'
  },
  {
    locale: 'en-CA',
    currency: 'CAD',
    name: 'Canadian Dollar',
    symbol: '$'
  },
  {
    locale: 'es-MX',
    currency: 'MXN',
    name: 'Mexican Peso',
    symbol: '$'
  }
]

// Create set of all symbols from currencies above
export const currencySymbols = new Set(currencies.map(c => c.symbol))

export const currencyLongSymbols = new Set(currencies.map(c => c.currency))

