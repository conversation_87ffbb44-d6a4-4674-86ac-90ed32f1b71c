// function that takes a filename string and converts to a title, e.g. 'my-file-name' to 'My File Name' and 'myFileName' to 'My File Name'
// It should remove file extensions, convert pascalCase and CamelCase, underscores and dashes, all to spaces.
import { DEFAULT_CHUNK_SIZE } from '@/db/query/update.js'

export const convertFileNameToTitle = (fileName) => {
  const fileNameWithoutExtension = fileName.replace(/\.[^/.]+$/, '')
  const fileNameWithSpaces = fileNameWithoutExtension.replace(/[-_.]/g, ' ')
  const fileNameWithSpacesAndCamelCase = fileNameWithSpaces.replace(/([a-z])([A-Z])/g, '$1 $2')
  return fileNameWithSpacesAndCamelCase.replace(/\w\S*/g, (txt) => {
    return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase()
  })
}

export const capitalize = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export const getExcelColumnName = function (index) {
  // Maps an index to how Excel names columns - A-Z, then AA-AZ, then BA-BZ, etc
  const letter = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.charAt(index % 26)
  const concat = Math.floor(index / 26)
  return concat > 0 ? getExcelColumnName(concat - 1) + letter : letter
}

export const range = (start, end) => {
  return Array.from({ length: end - start + 1 }, (_, i) => start + i)
}

export const pluralize = (word, count) => {
  return count === 1 ? word : word + 's'
}

/**
 * Moves an item in an array from one index to another
 * @param {Array} arr - The array to modify
 * @param {number} oldIndex - Current position of the item
 * @param {number} newIndex - Desired position of the item
 * @returns {Array} The modified array
 */
export function moveInArray (arr, oldIndex, newIndex) {
  const item = arr.splice(oldIndex, 1)[0]
  arr.splice(newIndex, 0, item)
  return arr
}

/**
 * Merges two objects with nested structure, combining inner objects and preserving arrays
 * @param {Object} base - The base object to merge into
 * @param {Object} override - The object whose properties should override target
 * @returns {Object} A new object with merged properties
 */
export function twoLevelMerge (base = {}, override = {}) {
  const result = { ...base }
  Object.keys(override).forEach(key => {
    if (result[key]) {
      if (Array.isArray(override[key])) {
        result[key] = override[key]
      } else if (typeof override[key] === 'object' && override[key] !== null) {
        result[key] = { ...result[key], ...override[key] }
      } else {
        result[key] = override[key]
      }
    } else {
      result[key] = override[key]
    }
  })
  return result
}

/**
 * Process an array of items in chunks
 * @param {Array} items - Array of items to process
 * @param {Function} processFn - Function to process each chunk
 * @param {number} [chunkSize=DEFAULT_CHUNK_SIZE] - Size of each chunk
 * @returns {Array} Array of results from processing each chunk
 */
export const processInChunks = (items, processFn, chunkSize = DEFAULT_CHUNK_SIZE) => {
  const results = []
  
  for (let i = 0; i < items.length; i += chunkSize) {
    const chunk = items.slice(i, i + chunkSize)
    results.push(processFn(chunk))
  }
  
  return results
}
