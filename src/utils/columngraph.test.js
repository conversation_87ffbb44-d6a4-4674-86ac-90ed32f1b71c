import { buildDependencyGraph, getDependants, hasCircularDependencies, topologicalSort } from './columngraph'
import { describe, expect, test } from 'vitest'

describe('buildDependencyGraph', () => {
  test('creates empty graph for columns without dependencies', () => {
    const cols = [
      { id: 0, header: 'Col 0' },
      { id: 1, header: 'Col 1' }
    ]
    
    const { graph, indegree } = buildDependencyGraph(cols)
    
    expect(graph.size).toBe(2)
    expect(graph.get(0)).toEqual([])
    expect(graph.get(1)).toEqual([])
    expect(indegree.get(0)).toBe(0)
    expect(indegree.get(1)).toBe(0)
  })
  
  test('builds correct graph for columns with simple dependencies', () => {
    const cols = [
      { id: 0, header: 'Col 0' },
      { id: 1, header: 'Col 1' },
      { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } }
    ]
    
    const { graph, indegree } = buildDependencyGraph(cols)
    
    expect(graph.size).toBe(3)
    expect(graph.get(0)).toEqual([2])
    expect(graph.get(1)).toEqual([2])
    expect(graph.get(2)).toEqual([])
    expect(indegree.get(0)).toBe(0)
    expect(indegree.get(1)).toBe(0)
    expect(indegree.get(2)).toBe(2)
  })
  
  test('builds correct graph for complex dependencies', () => {
    const cols = [
      { id: 0, header: 'Col 0' },
      { id: 1, header: 'Col 1' },
      { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } },
      { id: 3, header: 'Avg', calc: { sourceCols: [1, 2] } }
    ]
    
    const { graph, indegree } = buildDependencyGraph(cols)
    
    expect(graph.size).toBe(4)
    expect(graph.get(0)).toEqual([2])
    expect(graph.get(1)).toContain(2)
    expect(graph.get(1)).toContain(3)
    expect(graph.get(2)).toEqual([3])
    expect(graph.get(3)).toEqual([])
    expect(indegree.get(0)).toBe(0)
    expect(indegree.get(1)).toBe(0)
    expect(indegree.get(2)).toBe(2)
    expect(indegree.get(3)).toBe(2)
  })
})

describe('getDependants', () => {
  const cols = [
    { id: 0, header: 'Col 0' },
    { id: 1, header: 'Col 1' },
    { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } },
    { id: 3, header: 'Avg', calc: { sourceCols: [1, 2] } }
  ]
  const { graph } = buildDependencyGraph(cols)
  
  test('returns empty array for columns with no dependants', () => {
    const dependants = getDependants(graph, 1)
    expect(dependants).toEqual([])
  })
  
  test('returns direct dependants', () => {
    const dependants = getDependants(graph, 2)
    expect(dependants).toContain(0)
    expect(dependants).toContain(1)
    expect(dependants).toHaveLength(2)
  })
  
  test('returns indirect dependants', () => {
    const dependants = getDependants(graph, 3)
    expect(dependants).toContain(0)
    expect(dependants).toContain(1)
    expect(dependants).toContain(2)
    expect(dependants).toHaveLength(3)
  })
  
})

describe('topologicalSort', () => {
  test('sorts simple dependency graph correctly', () => {
    const cols = [
      { id: 0, header: 'Col 0' },
      { id: 1, header: 'Col 1' },
      { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } }
    ]
    
    const { graph, indegree } = buildDependencyGraph(cols)
    const sorted = topologicalSort(graph, indegree)
    
    // Column 2 depends on 0 and 1, so it must come after them
    expect(sorted.indexOf(2)).toBeGreaterThan(sorted.indexOf(0))
    expect(sorted.indexOf(2)).toBeGreaterThan(sorted.indexOf(1))
  })
  
  test('sorts complex dependency graph correctly', () => {
    const cols = [
      { id: 0, header: 'Col 0' },
      { id: 1, header: 'Col 1' },
      { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } },
      { id: 3, header: 'Avg', calc: { sourceCols: [1, 2] } },
      { id: 4, header: 'Total', calc: { sourceCols: [2, 3] } }
    ]
    
    const { graph, indegree } = buildDependencyGraph(cols)
    const sorted = topologicalSort(graph, indegree)
    
    // Check that the topological ordering respects all dependencies
    expect(sorted.indexOf(2)).toBeGreaterThan(sorted.indexOf(0))
    expect(sorted.indexOf(2)).toBeGreaterThan(sorted.indexOf(1))
    expect(sorted.indexOf(3)).toBeGreaterThan(sorted.indexOf(1))
    expect(sorted.indexOf(3)).toBeGreaterThan(sorted.indexOf(2))
    expect(sorted.indexOf(4)).toBeGreaterThan(sorted.indexOf(2))
    expect(sorted.indexOf(4)).toBeGreaterThan(sorted.indexOf(3))
  })
  
  test('returns a subset of nodes if there is a cycle', () => {
    // Create columns with a circular dependency: 0 -> 2 -> 3 -> 0
    const cols = [
      { id: 0, header: 'Col 0', calc: { sourceCols: [3] } },
      { id: 1, header: 'Col 1' },
      { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } },
      { id: 3, header: 'Avg', calc: { sourceCols: [2] } }
    ]
    
    const { graph, indegree } = buildDependencyGraph(cols)
    const sorted = topologicalSort(graph, indegree)
    
    // The topological sort can't include all nodes because of the cycle
    expect(sorted.length).toBeLessThan(cols.length)
    expect(sorted).toContain(1) // Column 1 has no dependencies, should be included
  })
})

describe('hasCircularDependencies', () => {
  test('returns false for acyclic dependency graph', () => {
    const cols = [
      { id: 0, header: 'Col 0' },
      { id: 1, header: 'Col 1' },
      { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } },
      { id: 3, header: 'Avg', calc: { sourceCols: [1, 2] } },
      { id: 4, header: 'Total', calc: { sourceCols: [2, 3] } }
    ]
    
    const hasCycles = hasCircularDependencies(cols)
    expect(hasCycles).toBe(false)
  })
  
  test('returns true for direct self-reference', () => {
    const cols = [
      { id: 0, header: 'Col 0' },
      { id: 1, header: 'Col 1', calc: { sourceCols: [1] } } // Self-reference
    ]
    
    const hasCycles = hasCircularDependencies(cols)
    expect(hasCycles).toBe(true)
  })
  
  test('returns true for simple circular dependency', () => {
    const cols = [
      { id: 0, header: 'Col 0', calc: { sourceCols: [1] } },
      { id: 1, header: 'Col 1', calc: { sourceCols: [0] } }
    ]
    
    const hasCycles = hasCircularDependencies(cols)
    expect(hasCycles).toBe(true)
  })
  
  test('returns true for complex circular dependency', () => {
    // Create columns with a circular dependency: 0 -> 2 -> 3 -> 0
    const cols = [
      { id: 0, header: 'Col 0', calc: { sourceCols: [3] } },
      { id: 1, header: 'Col 1' },
      { id: 2, header: 'Sum', calc: { sourceCols: [0, 1] } },
      { id: 3, header: 'Avg', calc: { sourceCols: [2] } }
    ]
    
    const hasCycles = hasCircularDependencies(cols)
    expect(hasCycles).toBe(true)
  })
  
  test('handles multiple disconnected cycles', () => {
    // Two separate circular dependencies
    const cols = [
      { id: 0, header: 'Col 0', calc: { sourceCols: [1] } },
      { id: 1, header: 'Col 1', calc: { sourceCols: [0] } },
      { id: 2, header: 'Col 2', calc: { sourceCols: [3] } },
      { id: 3, header: 'Col 3', calc: { sourceCols: [2] } },
      { id: 4, header: 'Col 4' } // No circular dependency
    ]
    
    const hasCycles = hasCircularDependencies(cols)
    expect(hasCycles).toBe(true)
  })
})
