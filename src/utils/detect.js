import { isAccountancyFormat, parseNumeric } from './numbers'
import { currencies } from './currencies'
import { booleanStrings, getDefaultColProps } from './formats'
import { detectDateFormats, getDateType } from './dates'

function getDefaultColType (type) {
  return {
    type,
    props: getDefaultColProps(type)
  }
}

// Proportion of cells that must match the test type for it to be considered that type
const detectionThreshold = 0.8

const testArray = (array, test) => {
  // Returns the proportion of items in the array that pass the test
  return array.filter(test).length / array.length
}

const testArrayIncludes = (array, val) => {
  // Returns the proportion of items in the array that include the value
  return testArray(array, v => v.includes(val))
}

const testNumbers = (array) => {
  // Returns the proportion of items in the array that are numeric using parseNumeric
  return testArray(array, v => !isNaN(parseNumeric(v)))
}

/*
Detection functions.

Given an array of strings, these functions - one for each columnType - will do its best guess to return
a colType with a props object along with a score of 0 to 1 on the number of cells that match that detection.
*/
export const detect = {
  text: () => {
    return [getDefaultColType('text'), 1]
  },
  
  boolean: (stringData) => {
    return [
      getDefaultColType('boolean'),
      testArray(stringData, v => booleanStrings.includes(v.toLowerCase()))
    ]
  },
  
  datetime: (stringData) => {
    const dateFormats = stringData.map(detectDateFormats)
    
    // Go through and get a count of all the formats and pick the one with the most
    const formatCounts = {}
    for (const formats of dateFormats) {
      for (const format of formats) {
        formatCounts[format] = (formatCounts[format] || 0) + 1
      }
    }
    if (!Object.keys(formatCounts).length) {
      return [getDefaultColType('datetime'), 0]
    }
    const mostUsedFormat = Object.keys(formatCounts).reduce((a, b) => formatCounts[a] > formatCounts[b] ? a : b)
    const dateType = getDateType(mostUsedFormat)
    const colDef = {
      type: 'datetime',
      props: {
        dateType: dateType.key,
        format: mostUsedFormat
      }
    }
    return [colDef, formatCounts[mostUsedFormat] / stringData.length]
    
  },
  
  number: (stringData) => {
    return [
      getDefaultColType('number'),
      testNumbers(stringData)
    ]
  },
  
  percent: (stringData) => {
    return [
      getDefaultColType('percent'),
      testArrayIncludes(stringData, '%')
    ]
  },
  
  currency: (stringData) => {
    const symbolScores = currencies.map(currency => {
      return {
        currency,
        score: testArrayIncludes(stringData, currency.symbol)
      }
    })
    
    // Get the symbol and score of the highest scoring symbol. If matching values, this picks the first in the list
    const { currency, score } = symbolScores.reduce((a, b) => a.score < b.score ? b : a)
    if (score === 0) {
      return [getDefaultColType('currency'), 0]
    }
    
    // If any of the cells are in accountancy format, set accounting to true
    const accounting = stringData.some(v => isAccountancyFormat(v))
    const colDef = {
      type: 'currency',
      props: {
        ...getDefaultColProps('currency'),
        locale: currency.locale,
        currency: currency.currency,
        accounting,
        display: 'symbol'
      }
    }
    return [colDef, score]
  }
}

export function detectColumn (stringData) {
  /*
  Given an array of strings, detect the type of the column and return the type and props.
  */
  // Ensure everything is a trimmed string. Discount empty cells from impacting the decision
  stringData = stringData.map(v => String(v).trim()).filter(v => v)
  if (!stringData.length) {
    // if it's all blanks, set a 'hide' flag to recommend hidden by default
    return {
      type: 'text',
      hide: true
    }
  }
  
  for (const type of ['datetime', 'boolean']) {
    const [colDef, score] = detect[type](stringData)
    if (score > detectionThreshold) {
      return colDef
    }
  }
  if (testNumbers(stringData) > detectionThreshold) {
    for (const type of ['percent', 'currency', 'number']) {
      const [colDef, score] = detect[type](stringData)
      if (score > detectionThreshold) {
        return colDef
      }
    }
  }
  return getDefaultColType('text')
}

export function detectColumns (rowData) {
  /*
  Given a 2D array of strings, detect the types of the columns and return the types and props.
  */
  
  // Take the first row off as a potential header, and use the next 100 rows for detection
  const maxCols = Math.max(...rowData.map(row => row.length))
  const subset = rowData.slice(1, 101)
  
  // transpose our subset into columnal data, and pad with empty strings
  const colData = Array.from({ length: maxCols }, (_, i) => subset.map(row => row[i] || ''))
  return colData.map(detectColumn)
}
