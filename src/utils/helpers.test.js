// write vitest for helpers.js
// Path: src\utils\__tests__\helpers.test.js

import { describe, expect, it } from 'vitest'
import { convertFileNameToTitle, twoLevelMerge } from './helpers'

describe('convertFileNameToTitle', () => {
  it('should convert a filename to a title', () => {
    const fileName = 'my-file-name'
    const title = convertFileNameToTitle(fileName)
    expect(title).toEqual('My File Name')
  })
  it('should convert a filename with underscores to a title', () => {
    const fileName = 'my_file_name'
    const title = convertFileNameToTitle(fileName)
    expect(title).toEqual('My File Name')
  })
  it('should convert a filename with CamelCase to a title', () => {
    const fileName = 'myFileName'
    const title = convertFileNameToTitle(fileName)
    expect(title).toEqual('My File Name')
  })
  // test an extension in file name
  it('should convert a filename with an extension to a title', () => {
    const fileName = 'my-file-name.js'
    const title = convertFileNameToTitle(fileName)
    expect(title).toEqual('My File Name')
  })
  // test a filename with a pascal case
  it('should convert a filename with PascalCase to a title', () => {
    const fileName = 'MyFileName'
    const title = convertFileNameToTitle(fileName)
    expect(title).toEqual('My File Name')
  })
  // test file name with two periods in it
  it('should convert a filename with two periods in it to a title', () => {
    const fileName = 'my-file.name.js'
    const title = convertFileNameToTitle(fileName)
    expect(title).toEqual('My File Name')
  })
})

describe('twoLevelMerge', () => {
  it('should merge two objects with nested properties', () => {
    const target = {
      a: { x: 1, y: 2 },
      b: { z: 3 }
    }
    const source = {
      a: { y: 4, w: 5 },
      c: { v: 6 }
    }
    const expected = {
      a: { x: 1, y: 4, w: 5 },
      b: { z: 3 },
      c: { v: 6 }
    }
    expect(twoLevelMerge(target, source)).toEqual(expected)
  })
  
  it('should handle empty objects', () => {
    const target = {}
    const source = { a: { x: 1 } }
    expect(twoLevelMerge(target, source)).toEqual(source)
  })
  
  it('should not modify the original objects', () => {
    const target = { a: { x: 1 } }
    const source = { a: { y: 2 } }
    const originalTarget = { ...target }
    const originalSource = { ...source }
    
    twoLevelMerge(target, source)
    
    expect(target).toEqual(originalTarget)
    expect(source).toEqual(originalSource)
  })
  
  it('should properly handle null values in source object', () => {
    const target = {
      a: { x: 1, y: 2, z: 3 },
      b: { v: 4 }
    }
    const source = {
      a: { y: null, w: 5 }
    }
    const expected = {
      a: { x: 1, y: null, z: 3, w: 5 },
      b: { v: 4 }
    }
    expect(twoLevelMerge(target, source)).toEqual(expected)
  })
  
  it('should preserve null values in target when not mentioned in source', () => {
    const target = {
      a: { x: null, y: 2 },
      b: { z: null }
    }
    const source = {
      a: { y: 4, w: 5 }
    }
    const expected = {
      a: { x: null, y: 4, w: 5 },
      b: { z: null }
    }
    expect(twoLevelMerge(target, source)).toEqual(expected)
  })
  
  it('should preserve arrays in nested objects', () => {
    const target = {
      a: { items: [1, 2, 3] },
      b: { x: 1 }
    }
    const source = {
      a: { items: [4, 5] },
      b: { y: 2 }
    }
    const expected = {
      a: { items: [4, 5] },
      b: { x: 1, y: 2 }
    }
    expect(twoLevelMerge(target, source)).toEqual(expected)
  })
  
  it('should properly merge nested objects', () => {
    const target = {
      config: { x: 1 }
    }
    const source = {
      config: { y: 2 }
    }
    const expected = {
      config: { x: 1, y: 2 }
    }
    expect(twoLevelMerge(target, source)).toEqual(expected)
  })
})
