import { beforeEach, describe, expect, it, vi } from 'vitest'
import { parseData, processPasteData } from '@/utils/parse'
import { ggsched } from '@/_testfiles/ggsched'
import { libra } from '@/_testfiles/libra'
import { datahub } from '@/_testfiles/datahub'
import { fruits } from '@/_testfiles/fruits'
import { milestones } from '@/_testfiles/milestones'
import { paRevenue } from '@/_testfiles/pa-revenue'

describe('parseData', () => {
  it('Should import GGPoker HTML schedule correctly', () => {
    const {
      colDefs,
      hasHeader
    } = parseData(ggsched)
    expect(colDefs).toHaveLength(7)
    expect(colDefs[6].type).toBe('currency')
    expect(colDefs[6].props?.currency).toBe('JPY')
    expect(hasHeader).toBe(false)
  })
  it('Should import Libra correctly', () => {
    const {
      rowData,
      colDefs,
      hasHeader
    } = parseData(libra)
    expect(colDefs).toHaveLength(8)
    expect(rowData).toHaveLength(37)
    expect(rowData[1][0]).toBe('2018-01-02T09:00:00.000Z')
    expect(rowData[1][1]).toBe('88.1')
    expect(colDefs[0].type).toBe('datetime')
    expect(colDefs[1].type).toBe('number')
    expect(colDefs[2].type).toBe('number')
    expect(colDefs[7].type).toBe('text')
    expect(hasHeader).toBe(true)
  })
  
  it('Should handle fruits', () => {
    const {
      rowData,
      colDefs,
      hasHeader
    } = parseData(fruits)
    expect(colDefs).toHaveLength(5)
    expect(colDefs[1].type).toBe('currency')
    expect(colDefs[2].type).toBe('number')
    expect(colDefs[3].type).toBe('boolean')
    expect(rowData).toHaveLength(7)
    expect(hasHeader).toBe(true)
    const firstRow = rowData[1]
    expect(firstRow[0]).toBe('Apples')
  })
  
  it('Should recognize header of datahub', () => {
    const { hasHeader } = parseData(datahub)
    expect(hasHeader).toBe(true)
  })
  
  it('Should recognize Mon-YYYY formats', () => {
    const { colDefs } = parseData(milestones)
    expect(colDefs[1].type).toBe('datetime')
  })
  
  it('Should handle PA revenue auto headers even with a blank', () => {
    const { hasHeader } = parseData(paRevenue)
    expect(hasHeader).toBe(true)
  })
})

describe('processPasteData', () => {
  const createClipboardEvent = (data) => ({
    clipboardData: {
      getData: () => data
    }
  })
  
  it('handles empty/invalid clipboard data', () => {
    expect(processPasteData(null)).toBeNull()
    expect(processPasteData({})).toBeNull()
    expect(processPasteData(createClipboardEvent(''))).toBeNull()
    expect(processPasteData(createClipboardEvent('   '))).toBeNull()
  })
  
  it('processes single cell data', () => {
    const result = processPasteData(createClipboardEvent('test'))
    expect(result).toEqual([['test']])
  })
  
  it('processes row data with tabs', () => {
    const result = processPasteData(createClipboardEvent('a\tb\tc'))
    expect(result).toEqual([['a', 'b', 'c']])
  })
  
  it('processes single column with comma', () => {
    const result = processPasteData(createClipboardEvent('$2,120.23'))
    expect(result).toEqual([['$2,120.23']])
  })
  
  it('processes multiple rows', () => {
    const result = processPasteData(createClipboardEvent('a\tb\tc\n1\t2\t3'))
    expect(result).toEqual([
      ['a', 'b', 'c'],
      ['1', '2', '3']
    ])
  })
  
  it('trims whitespace from cells', () => {
    const result = processPasteData(createClipboardEvent('  a  \t  b  \n  c  \t  d  '))
    expect(result).toEqual([
      ['a', 'b'],
      ['c', 'd']
    ])
  })
  
  it('filters out completely empty rows', () => {
    const result = processPasteData(createClipboardEvent('a\tb\n\n\nc\td'))
    expect(result).toEqual([
      ['a', 'b'],
      ['c', 'd']
    ])
  })
  
  it('preserves empty cells within rows', () => {
    const result = processPasteData(createClipboardEvent('a\t\tc\n1\t2\t'))
    expect(result).toEqual([
      ['a', '', 'c'],
      ['1', '2', '']
    ])
  })
  
  it('handles mixed delimiters preferring tabs', () => {
    const result = processPasteData(createClipboardEvent('a\tb,c\n1\t2,3'))
    expect(result).toEqual([
      ['a', 'b,c'],
      ['1', '2,3']
    ])
  })
  
  it('handles currency values with commas correctly', () => {
    const result = processPasteData(createClipboardEvent('$1,234.56\t$2,345.67'))
    expect(result).toEqual([['$1,234.56', '$2,345.67']])
  })
  
  it('preserves comma-containing values in cells', () => {
    const testCases = [
      {
        input: '$1,234.56\t$2,345.67\n$3,456.78\t$4,567.89',
        expected: [
          ['$1,234.56', '$2,345.67'],
          ['$3,456.78', '$4,567.89']
        ]
      },
      {
        input: 'Smith, John\tDoe, Jane',
        expected: [['Smith, John', 'Doe, Jane']]
      },
      {
        input: '1,000\t2,000\n3,000\t4,000',
        expected: [
          ['1,000', '2,000'],
          ['3,000', '4,000']
        ]
      }
    ]
    
    testCases.forEach(({ input, expected }) => {
      const result = processPasteData(createClipboardEvent(input))
      expect(result).toEqual(expected)
    })
  })
  
  it('handles mixed content with commas correctly', () => {
    const input = 'Item Name\tPrice\nSmith, John\t$1,234.56\nDoe, Jane\t$2,345.67'
    const expected = [
      ['Item Name', 'Price'],
      ['Smith, John', '$1,234.56'],
      ['Doe, Jane', '$2,345.67']
    ]
    const result = processPasteData(createClipboardEvent(input))
    expect(result).toEqual(expected)
  })
  
})

describe('pasting empty cells', () => {
  let mockEvent
  
  beforeEach(() => {
    // Create a mock clipboard event
    mockEvent = {
      clipboardData: {
        getData: vi.fn()
      }
    }
  })
  
  it('should preserve empty cells when allowEmpty is true', () => {
    // Mock clipboard data with empty cells
    const clipboardText = 'A\t\nB\t'
    mockEvent.clipboardData.getData.mockReturnValue(clipboardText)
    
    // Process the paste data with allowEmpty=true
    const result = processPasteData(mockEvent, true)
    
    // Expected result should include empty cells
    const expected = [
      ['A', ''],  // Row with value and empty cell
      ['B', '']  // Row with value and empty cell
    ]
    
    expect(result).toEqual(expected)
  })
  
  it('should handle rows with only empty cells', () => {
    // Mock clipboard data with a completely empty row
    const clipboardText = 'A\n\n\nB\n'
    mockEvent.clipboardData.getData.mockReturnValue(clipboardText)
    
    // Process the paste data with allowEmpty=true
    const result = processPasteData(mockEvent, true)
    
    // Expected result should include the empty row
    const expected = [
      ['A'],
      [''],
      [''],
      ['B'],
      ['']
    ]
    
    expect(result).toEqual(expected)
  })
  
  it('should handle trailing empty cells and rows', () => {
    // Mock clipboard data with trailing empty cells and rows
    const clipboardText = 'A\t\t\nB\t\t'
    mockEvent.clipboardData.getData.mockReturnValue(clipboardText)
    
    // Process the paste data with allowEmpty=true
    const result = processPasteData(mockEvent, true)
    
    // Expected result should include all empty cells and rows
    const expected = [
      ['A', '', ''],
      ['B', '', '']
    ]
    
    expect(result).toEqual(expected)
  })
  
  it('should filter out empty cells when allowEmpty is false', () => {
    // Mock clipboard data with empty cells
    const clipboardText = 'A\t\n\n\nB\t'
    mockEvent.clipboardData.getData.mockReturnValue(clipboardText)
    
    // Process the paste data with allowEmpty=false
    const result = processPasteData(mockEvent, false)
    
    // Expected result should not include rows, but will include empty cells
    const expected = [
      ['A', ''],
      ['B', '']
    ]
    
    expect(result).toEqual(expected)
  })
})
