import { currencySymbols } from './currencies'
import { Big } from 'big.js'

/*
NO VUE COMPONENTS HERE

This is a utility file that contains functions for formatting and converting data types.
It is used by the Table and Chart components to format and convert data, but also by
embed.js to format data for the chart. Anything imported here goes into our
embeddable build.
 */

// Create regex to remove any currency symbols from anywhere in the string. Escape characters so e.g. $ matches $.
const allSymbols = Array.from(currencySymbols).map(s => `\\${s}`).join('|')
export const currencySymbolRegex = new RegExp(`(${allSymbols})`, 'g')

// Find 3 letter currency codes from start or end of a string. Match letters but not numbers.
export const currencyCodeRegex = /^([A-Z]{3})|([A-Z]{3})$/gi

/**
 * Converts a value to a Big number instance or NaN
 *
 * @param {*} value - The value to convert
 * @returns {Big|Number} A Big number instance or NaN if conversion fails
 */
export const toBig = (value) => {
  if (value === undefined || value === null || value === '') return NaN
  if (typeof value === 'boolean') return Big(Number(value))
  if (value instanceof Big) return value
  try {
    return Big(value)
  } catch (error) {
    return NaN
  }
}
export const parseNumeric = (value, commaDecimal = false, handleAccountancy = true) => {
  /*
  Parses a string to a number, handling currencies and %s but returning NaN if not recognized.

  Strips out currency symbols, from start and end, 3-letter currency codes, commas and percentages
  Handles accountancy negative numbers e.g. (1.99) to -1.99
  Handles commas as decimals if commaDecimal is true
  */
  
  if (value === undefined || value === null) return NaN
  
  // Try a direct conversion with Big.js; this does e.g. booleans to 1/0
  if (!isNaN(Number(value))) {
    return toBig(Number(value))
  }
  
  value = String(value)
  
  // If commas mean decimals, remove decimals, then switch comma to decimal
  if (commaDecimal) {
    value = value.replace(/\./g, '').replace(/,/g, '.')
  }
  
  // convert accountancy negative e.g. (1.99) to -1.99
  if (handleAccountancy) {
    value = value.replace(/\((.*)\)/, '-$1')
  }
  
  // Trim whitespace from value. remove currency symbols, commas and %
  value = value.trim()
    .replace(currencySymbolRegex, '')
    .replace(currencyCodeRegex, '')
    .replace(/[ ,%]/g, '')
  
  return toBig(value)
}

export const forceNumeric = (value) => {
  // Take value and remove all but the characters 0 to 9
  const num = String(value).replace(/[^0-9.-]/g, '')
    .replace(/^-+/, '-') // Replace multiple leading minuses with single minus
    .replace(/-+$/, '') // Remove trailing minuses
    .replace(/(?!^)-/g, '') // Remove any other minuses except leading
  return toBig(num)
}
export const interpretNumeric = (value) => {
  // A empty-like value will be charitably interpreted as 0; otherwise returns a NaN
  if (value === undefined || value === null || value === '-' || value === '') {
    return Big(0)
  }
  return toBig(value)
}

export const alwaysNumeric = (value) => {
  // interpretNumeric, but will return NaN as 0 so you always get a number
  return interpretNumeric(value) || Big(0)
}

export const isAccountancyFormat = (value, commaDecimal = false) => {
  // Return true if its negative parsing with accountancy, but NaN when not
  return parseNumeric(value, commaDecimal, true) < 0 &&
    isNaN(parseNumeric(value, commaDecimal, false))
}
