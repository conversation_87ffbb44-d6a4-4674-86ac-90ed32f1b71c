/**
 * Formula Service - Helper functions for working with formulas
 * This can be expanded with more comprehensive validation and parsing
 */

// List of supported SQL functions and operators
const SUPPORTED_FUNCTIONS = [
  'SUM', 'AVG', 'MIN', 'MAX', 'COUNT', 'COALESCE', 'ROUND',
  'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'CAST', 'AS'
]

const OPERATORS = ['+', '-', '*', '/', '%', '=', '<>', '>', '<', '>=', '<=', 'AND', 'OR', 'NOT']

// Simple tokenizer for highlighting and validating formulas
export function tokenizeFormula (formula) {
  if (!formula) return []
  
  const tokens = []
  let current = ''
  let inColumnRef = false
  let inString = false
  let stringDelimiter = ''
  
  const addToken = (type) => {
    if (current) {
      let tokenType = type
      
      if (!type) {
        // Try to determine token type
        if (/^\d+(\.\d+)?$/.test(current)) {
          tokenType = 'number'
        } else if (SUPPORTED_FUNCTIONS.includes(current.toUpperCase())) {
          tokenType = 'function'
        } else if (OPERATORS.includes(current.toUpperCase())) {
          tokenType = 'operator'
        } else {
          tokenType = 'identifier'
        }
      }
      
      tokens.push({
        value: current,
        type: tokenType
      })
      current = ''
    }
  }
  
  for (let i = 0; i < formula.length; i++) {
    const char = formula[i]
    
    // Handle column references [ColumnName]
    if (char === '[' && !inString) {
      addToken()
      inColumnRef = true
      current = char
    } else if (char === ']' && inColumnRef) {
      current += char
      addToken('column')
      inColumnRef = false
    }
    // Handle string literals
    else if ((char === '\'' || char === '"') && !inString) {
      addToken()
      inString = true
      stringDelimiter = char
      current = char
    } else if (char === stringDelimiter && inString) {
      current += char
      addToken('string')
      inString = false
    }
    // Handle operators and special characters
    else if (!inString && !inColumnRef && /[\s(),]/.test(char)) {
      addToken()
      if (char !== ' ') {
        tokens.push({
          value: char,
          type: char === '(' || char === ')' ? 'bracket' : 'separator'
        })
      }
    } else {
      current += char
    }
  }
  
  // Add any remaining token
  addToken()
  
  return tokens
}

// Basic formula validation
export function validateFormula (formula, availableColumns) {
  if (!formula || !formula.trim()) {
    return { valid: false, error: 'Formula cannot be empty' }
  }
  
  // Tokenize the formula
  const tokens = tokenizeFormula(formula)
  
  // Check for balanced brackets
  let bracketCount = 0
  tokens.forEach(token => {
    if (token.value === '(') bracketCount++
    if (token.value === ')') bracketCount--
    
    // Negative count means there's a closing bracket without an opening one
    if (bracketCount < 0) {
      return { valid: false, error: 'Unbalanced parentheses' }
    }
  })
  
  if (bracketCount !== 0) {
    return { valid: false, error: 'Unbalanced parentheses' }
  }
  
  // Check that column references are valid
  if (availableColumns) {
    const columnRefs = tokens.filter(t => t.type === 'column')
    for (const colRef of columnRefs) {
      // Extract column name from [ColumnName]
      const colName = colRef.value.substring(1, colRef.value.length - 1)
      
      // Check if column exists in available columns by matching header
      const colExists = availableColumns.some(col =>
        col.header && col.header.toLowerCase() === colName.toLowerCase()
      )
      
      if (!colExists) {
        return {
          valid: false,
          error: `Unknown column: ${colName}`
        }
      }
    }
  }
  
  // In a real app, you would perform more comprehensive validation here
  
  return { valid: true }
}

// Generate SQL from our formula syntax
export function generateSql (formula, columns) {
  // Replace column references [Column Name] with appropriate SQL reference
  let sql = formula
  
  columns.forEach(column => {
    if (column.header) {
      const pattern = new RegExp(`\\[${column.header}\\]`, 'gi')
      sql = sql.replace(pattern, `c${column.id}`)
    }
  })
  
  return sql
}

// Auto-complete suggestions based on current typing
export function getSuggestions (text, position, columns, functions) {
  // Check if we're in the middle of typing a column reference
  const isColumnRef = /\[[^\]]*$/.test(text.substring(0, position))
  
  // Get the current word being typed
  const upToPosition = text.substring(0, position)
  let lastWord
  
  if (isColumnRef) {
    // For column references, get text after the opening bracket
    const bracketPos = upToPosition.lastIndexOf('[')
    lastWord = bracketPos >= 0 ? upToPosition.substring(bracketPos + 1) : ''
  } else {
    // For functions, get the last word
    const match = upToPosition.match(/[a-zA-Z0-9_]+$/)
    lastWord = match ? match[0] : ''
  }
  
  if (!lastWord && !isColumnRef) return []
  
  const suggestions = []
  
  // Add column suggestions
  if (isColumnRef) {
    // For column references, match any part of the column name, not just the beginning
    const searchText = lastWord.toLowerCase()
    columns.forEach(column => {
      if (column.header && column.header.toLowerCase().includes(searchText)) {
        suggestions.push({
          label: column.header,
          type: 'column',
          insertText: `${column.header}]`
        })
      }
    })
  }
  // Add function suggestions
  else {
    const searchText = lastWord.toLowerCase()
    functions.forEach(func => {
      if (func.name.toLowerCase().includes(searchText)) {
        suggestions.push({
          label: func.name,
          type: 'function',
          insertText: func.template || `${func.name}()`
        })
      }
    })
  }
  
  return suggestions
}

export default {
  tokenizeFormula,
  validateFormula,
  generateSql,
  getSuggestions
}
