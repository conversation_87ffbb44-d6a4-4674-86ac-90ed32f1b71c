import { describe, expect, it } from 'vitest'
import { alwaysNumeric, forceNumeric, interpretNumeric, isAccountancyFormat, parseNumeric, toBig } from './numbers'
import { Big } from 'big.js'

describe('parseNumeric', () => {
  it('Should detect number', () => {
    expect(parseNumeric('1,000,000')).toStrictEqual(new Big(1000000))
    expect(parseNumeric('$1,000.50')).toStrictEqual(new Big(1000.5))
    expect(parseNumeric('¥800,000')).toStrictEqual(new Big(800000))
    expect(parseNumeric('¥1,000,000')).toStrictEqual(new Big(1000000))
    expect(parseNumeric('¥60,000')).toStrictEqual(new Big(60000))
  })
  it('Should detect all as numbers', () => {
    const stringData = [
      '¥2,024,000', '¥800,000', '¥1,000,000',
      '¥1,500,000', '¥2,000,000', '¥1,000,000',
      '¥1,500,000', '¥5,000,000', '¥8,000,000',
      '¥1,500,000', '¥800,000', '¥1,000,000'
    ]
    const numbers = stringData.map(v => parseNumeric(v))
    expect(numbers.every(v => !isNaN(v))).toBe(true)
  })
})

// Vitest unit tests based on the doc comments
describe('toNumber', () => {
  it('should convert 123 to Big(123)', () => {
    expect(toBig(123).toString()).toBe(new Big(123).toString())
  })
  
  it('should convert "456.789" to Big(456.789)', () => {
    expect(toBig('456.789').toString()).toBe(new Big(456.789).toString())
  })
  
  it('should return NaN for undefined', () => {
    expect(toBig(undefined)).toBeNaN()
  })
  
  it('should return NaN for invalid string', () => {
    expect(toBig('invalid')).toBeNaN()
  })
  
  it('should return NaN for empty string', () => {
    expect(toBig('')).toBeNaN()
  })
  
  it('should return NaN for NaN', () => {
    expect(toBig(NaN)).toBeNaN()
  })
  
  it('should return NaN for null', () => {
    expect(toBig(null)).toBeNaN()
  })
  
})

describe('isAccountancyFormat', () => {
  it('should return true for accountancy format negative numbers', () => {
    expect(isAccountancyFormat('(1,000)')).toBe(true)
    expect(isAccountancyFormat('(500.25)')).toBe(true)
    expect(isAccountancyFormat('(¥1,000)')).toBe(true)
  })
  
  it('should return false for regular negative numbers', () => {
    expect(isAccountancyFormat('-1000')).toBe(false)
    expect(isAccountancyFormat('-500.25')).toBe(false)
    expect(isAccountancyFormat('-¥1,000')).toBe(false)
  })
  
  it('should return false for positive numbers', () => {
    expect(isAccountancyFormat('1000')).toBe(false)
    expect(isAccountancyFormat('500.25')).toBe(false)
    expect(isAccountancyFormat('¥1,000')).toBe(false)
  })
  
  it('should handle comma decimal format correctly', () => {
    expect(isAccountancyFormat('(1.000,25)', true)).toBe(true)
    expect(isAccountancyFormat('(500,25)', true)).toBe(true)
    expect(isAccountancyFormat('1.000,25', true)).toBe(false)
  })
  
  it('should return false for invalid formats', () => {
    expect(isAccountancyFormat('')).toBe(false)
    expect(isAccountancyFormat('()')).toBe(false)
    expect(isAccountancyFormat('(invalid)')).toBe(false)
    expect(isAccountancyFormat('((100))')).toBe(false)
  })
})
describe('forceNumeric', () => {
  it('should strip non-numeric characters', () => {
    expect(forceNumeric('abc123def').toString()).toBe(new Big(123).toString())
    expect(forceNumeric('$45.67xyz').toString()).toBe(new Big(45.67).toString())
    expect(forceNumeric('--100.25--').toString()).toBe(new Big(-100.25).toString())
  })
  
  it('should handle special characters and spaces', () => {
    expect(forceNumeric('  123  ').toString()).toBe(new Big(123).toString())
    expect(forceNumeric('@#$%^&*90').toString()).toBe(new Big(90).toString())
    expect(forceNumeric('1,234,567').toString()).toBe(new Big(1234567).toString())
  })
  
  it('should handle edge cases', () => {
    expect(forceNumeric('')).toBeNaN()
    expect(forceNumeric('abc')).toBeNaN()
    expect(forceNumeric('.')).toBeNaN()
  })
})

describe('interpretNumeric', () => {
  it('should convert empty-like values to zero', () => {
    expect(interpretNumeric(undefined).toString()).toBe(new Big(0).toString())
    expect(interpretNumeric(null).toString()).toBe(new Big(0).toString())
    expect(interpretNumeric('-').toString()).toBe(new Big(0).toString())
    expect(interpretNumeric('').toString()).toBe(new Big(0).toString())
  })
  
  it('should handle valid numeric strings', () => {
    expect(interpretNumeric('123.45').toString()).toBe(new Big(123.45).toString())
    expect(interpretNumeric('-789').toString()).toBe(new Big(-789).toString())
  })
  
  it('should handle non-numeric strings', () => {
    expect(interpretNumeric('abc')).toBeNaN()
    expect(interpretNumeric('12.34.56')).toBeNaN()
  })
})

describe('alwaysNumeric', () => {
  it('should convert NaN to zero', () => {
    expect(alwaysNumeric('invalid').toString()).toBe(new Big(0).toString())
    expect(alwaysNumeric('abc123xyz').toString()).toBe(new Big(0).toString())
  })
  
  it('should preserve valid numbers', () => {
    expect(alwaysNumeric('45.67').toString()).toBe(new Big(45.67).toString())
    expect(alwaysNumeric(-123).toString()).toBe(new Big(-123).toString())
  })
  
  it('should handle empty-like values as zero', () => {
    expect(alwaysNumeric(undefined).toString()).toBe(new Big(0).toString())
    expect(alwaysNumeric(null).toString()).toBe(new Big(0).toString())
    expect(alwaysNumeric('').toString()).toBe(new Big(0).toString())
  })
})

