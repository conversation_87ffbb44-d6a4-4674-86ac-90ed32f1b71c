export function buildDependencyGraph (cols) {
  const graph = new Map()
  const indegree = new Map()
  
  cols.forEach(column => {
    graph.set(column.id, [])
    indegree.set(column.id, 0)
  })
  
  cols.forEach(column => {
    if (column.calc) {
      column.calc.sourceCols.forEach(dependencyId => {
        if (!graph.has(dependencyId)) {
          // Initialize the dependency in the graph if it doesn't exist
          graph.set(dependencyId, [])
          indegree.set(dependencyId, 0)
        }
        graph.get(dependencyId).push(column.id)
        indegree.set(column.id, indegree.get(column.id) + 1)
      })
    }
  })
  
  return {
    graph,
    indegree
  }
}

export function getDependants (graph, colId) {
  /*
  Finds all columns that are required to calculate the given column.
   */
  const dependants = new Set()
  const stack = [colId]
  
  while (stack.length > 0) {
    const current = stack.pop()
    
    graph.forEach((dependencies, column) => {
      if (dependencies.includes(current) && !dependants.has(column)) {
        dependants.add(column)
        stack.push(column)
      }
    })
  }
  
  return Array.from(dependants)
}

export function topologicalSort (graph, indegree) {
  const queue = []
  indegree.forEach((value, key) => {
    if (value === 0) queue.push(key)
  })
  
  const sortedColumns = []
  
  while (queue.length > 0) {
    const node = queue.shift()
    sortedColumns.push(node)
    graph.get(node).forEach(dependent => {
      indegree.set(dependent, indegree.get(dependent) - 1)
      if (indegree.get(dependent) === 0) {
        queue.push(dependent)
      }
    })
  }
  return sortedColumns
}

export function hasCircularDependencies (cols) {
  const { graph } = buildDependencyGraph(cols)
  
  // Three possible states for each node:
  // - not visited yet (not in visitedNodes)
  // - currently being visited (in currentPath)
  // - fully visited (in visitedNodes but not in currentPath)
  const visitedNodes = new Set()
  const currentPath = new Set()
  
  function dfs (nodeId) {
    // If we've already fully visited this node, it's safe
    if (visitedNodes.has(nodeId) && !currentPath.has(nodeId)) {
      return false
    }
    
    // If we encounter a node that's currently in our path, we have a cycle
    if (currentPath.has(nodeId)) {
      return true
    }
    
    // Add the current node to our path
    currentPath.add(nodeId)
    visitedNodes.add(nodeId)
    
    // Check all dependencies of this node
    const dependents = graph.get(nodeId) || []
    for (const dependent of dependents) {
      if (dfs(dependent)) {
        return true
      }
    }
    
    // Remove the current node from our path (backtracking)
    currentPath.delete(nodeId)
    
    return false
  }
  
  // Check all nodes
  for (const nodeId of graph.keys()) {
    if (!visitedNodes.has(nodeId)) {
      if (dfs(nodeId)) {
        return true
      }
    }
  }
  
  return false
}
