/***
 Aggregate methods.
 
 If type is given, then the return type is always in that format. Otherwise,
 it will be whatever the original column type is.
 */
import { typeMap } from '@/db/types.js'

export const aggregates = {
  sum: {
    label: 'Sum',
    desc: 'Total for all cells',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'Σ',
    common: true,
    query: col => `SUM(${col})`
  },
  
  max: {
    label: 'Max',
    desc: 'Maximum value',
    symbol: '↑',
    common: true,
    query: col => `MAX(${col})`
  },
  
  min: {
    label: 'Min',
    desc: 'Minimum value',
    symbol: '↓',
    common: true,
    query: col => `MIN(${col})`
  },
  
  avg: {
    label: 'Average',
    desc: 'Average of cells',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'x̄',
    common: true,
    // AVG casts to floating point - see https://github.com/duckdb/duckdb/issues/6829
    query: col => `CAST(AVG(${col}) AS ${typeMap.number})`
  },
  
  diff: {
    label: 'Difference',
    desc: 'Difference between the largest and smallest values',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'Δ',
    query: col => `MAX(${col}) - MIN(${col})`
  },
  
  count: {
    label: 'Count',
    desc: 'Number of all cells',
    symbol: '#',
    type: 'number',
    common: true,
    query: () => `CAST(COUNT(*) AS INT)`   // DuckDB returns COUNT as a BIGINT
  },
  
  distinct: {
    label: 'Count distinct',
    desc: 'Number of unique values',
    symbol: '∪',
    type: 'number',
    common: true,
    query: col => `CAST(COUNT(DISTINCT ${col}) AS INT)`   // DuckDB returns COUNT as a BIGINT
  },
  
  counta: {
    label: 'Count non-empty',
    desc: 'Number of non-empty cells',
    symbol: '#',
    type: 'number',
    common: true,
    query: col => `CAST(COUNT(${col}) AS INT)`   // DuckDB returns COUNT as a BIGINT
  },
  
  median: {
    label: 'Median',
    desc: 'Median value',
    limitTo: ['number', 'currency', 'percent'],
    symbol: '⟨x⟩',
    query: col => `median(${col})`
  },
  
  mode: {
    label: 'Mode',
    desc: 'Most common value',
    limitTo: ['text', 'number', 'currency', 'percent'],
    symbol: '⟨x⟩',
    query: col => `mode(${col})`
  },
  
  stddev: {
    label: 'St Dev',
    desc: 'Standard deviation',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'σ',
    query: col => `stddev_samp(${col})`
  },
  
  stddevp: {
    label: 'St Dev (P)',
    desc: 'Population standard deviation',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'σ',
    query: col => `stddev_pop(${col})`
  },
  
  var: {
    label: 'Variance',
    desc: 'Variance',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'σ²',
    query: col => `var_samp(${col})`
  },
  
  varp: {
    label: 'Variance (P)',
    desc: 'Population variance',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'σ²',
    query: col => `var_pop(${col})`
  },
  
  skew: {
    label: 'Skew',
    desc: 'Skewness',
    limitTo: ['number', 'currency', 'percent'],
    symbol: 'S',
    query: col => `skewness(${col})`
  }
}

// Return an aggregates object just like above, but only those valid for given column type
export const getAggregatesForColType = (colType, commonOnly = false) => {
  return Object.fromEntries(
    Object
      .entries(aggregates)
      .filter(([, agg]) => (!agg.limitTo || agg.limitTo.includes(colType)))
      .filter(([, agg]) => (!commonOnly || agg.common))
  )
}

// Simplified version of getAggregatesForCol, returning an array of { key, label } objects
export const getAggregateLabelsForColType = (colType, commonOnly = false) => {
  return Object.entries(getAggregatesForColType(colType, commonOnly)).map(([key, agg]) => {
    return { key, label: agg.label }
  }).sort((a, b) => a.label.localeCompare(b.label))
}
