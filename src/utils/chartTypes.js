import { markRaw } from 'vue'
import { ChartColumn, ChartLine, ChartNoAxesCombined, ChartPie, ChartScatter } from 'lucide-vue-next'

export const chartTypes = [
  {
    id: 'bar',
    text: 'Bar',
    title: 'Bar and Column Charts',
    desc: 'For all types of horizontal and vertical bar and column charts, including stacked charts.',
    canBeMixed: true,
    icon: markRaw(ChartColumn),
    
    presets: (numSeries) => {
      if (numSeries === 1) {
        return {
          col: {
            label: 'Columns'
          },
          bar: {
            label: 'Horizontal Bars',
            horizontal: true
          }
        }
        
      }
      return {
        col: {
          label: 'Columns',
          stacking: ''
        },
        colstack: {
          label: 'Stacked Columns',
          stacking: 'stacked'
        },
        col100: {
          label: '100% Stacked Columns',
          stacking: 'stacked100'
        },
        bar: {
          label: 'Bars',
          horizontal: true,
          stacking: 'normal'
        },
        barstack: {
          label: 'Stacked Bars',
          horizontal: true,
          stacking: 'stacked'
        },
        bar100: {
          label: '100% Stacked Bars',
          horizontal: true,
          stacking: 'stacked100'
        }
      }
    }
  },
  {
    id: 'line',
    text: 'Line',
    title: 'Line Charts',
    desc: 'Perfect for showing change over time.',
    canBeMixed: true,
    icon: markRaw(ChartLine),
    
    presets: (_) => {
      return {
        line: {
          label: 'Line',
          lineFill: false
        },
        area: {
          label: 'Area',
          lineFill: true
        }
      }
    }
  },
  {
    id: 'pie',
    text: 'Pie',
    title: 'Pie and Donut Charts',
    desc: 'For showing shares of a whole. Donut charts are just pies with the middles cut out, and are usually the best choice.',
    icon: markRaw(ChartPie),
    presets: (numSeries) => {
      if (numSeries !== 1) return []
      return {
        pie: {
          label: 'Pie',
          donutCutout: 0
        },
        donut: {
          label: 'Donut',
          donutCutout: 50
        },
        donutThin: {
          label: 'Donut (thin)',
          donutCutout: 80
        }
      }
    }
  },
  {
    id: 'scatter',
    text: 'Scatter',
    title: 'Scatter Plot',
    desc: 'For looking at trends between two variables. Best for a large number of data points.',
    icon: markRaw(ChartScatter),
    presets: (numSeries) => {
      if (numSeries !== 2) return []
      return {
        scatter: {
          label: 'Scatter'
        }
      }
    }
  },
  {
    id: 'mixed',
    text: 'Mixed',
    title: 'Mixed Chart Types',
    desc: 'For more advanced charts, combining both line and bars onto a single chart.',
    icon: markRaw(ChartNoAxesCombined),
    presets: (numSeries) => {
      if (numSeries === 1) return []
      if (numSeries === 2) {
        return {
          linebar: {
            label: 'Line chart with bar',
            chartTypes: ['line', 'bar']
          },
          barline: {
            label: 'Bar chart with line',
            chartTypes: ['bar', 'line']
          }
        }
        
      } else {
        return {
          linebar: {
            label: 'Line chart with bar',
            chartTypes: ['line', 'bar']
          },
          barline: {
            label: 'Bars with line',
            chartTypes: ['bar', 'line']
          },
          barstackline: {
            label: 'Stacked Bars with line',
            props: { stacking: 'stacked' },
            chartTypes: ['bar', 'line']
          },
          bar100line: {
            label: '100% Stacked Bars with line',
            props: { stacking: 'stacked100' },
            chartTypes: ['bar', 'line']
          }
        }
      }
    }
  }
]

export const getChartType = (id) => chartTypes.find((chartType) => chartType.id === id)

export const datasetsToChartType = (datasets) => {
  if (datasets.length === 2) {
    return getChartType('mixed')
  } else if (datasets.length === 1) {
    return getChartType(datasets[0].chartType)
  }
  return undefined
}
