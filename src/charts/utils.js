import { repr } from '@/utils/formats.js'

export const getFormatter = (col, compact = false) => {
  /*
  getFormatter

  Takes a column definition and returns a formatter function for numbers. Use in tooltips and in ticks
  on scales.

  It can be used for the callback on ticks. It takes three arguments
  (value, index, ticks) and should return a string. We only use the "value" portion of it.
  https://www.chartjs.org/docs/latest/axes/labelling.html#creating-custom-tick-formats
  */
  // Setting compact will force compact display regardless of the column - used for axes
  if (!col || !col.type) return
  return v => {
    const props = {
      ...col.props,
      compact
    }
    return repr(v, col.type, props)
  }
}
export const getCol = (cols, col) => {
  /*
  Return the column object in cols with the given col.
  
  If col is a number, then it's the id of the column. Otherwise, check against col.id.
   */
  if (col === undefined) return
  return cols.find(c => c.id === (typeof col === 'number' ? col : col.id))
}
export const getFirstCol = (cols, byRow, ds) => {
  /*
  Gets the first column by id in the dataset.
  
  This will get the first column ID in either values or series, depending on
  whether we're charting by row or by column.
  */
  const lookup = ds[byRow ? 'values' : 'series']
  if (!lookup) return
  return getCol(cols, lookup[0])
}
