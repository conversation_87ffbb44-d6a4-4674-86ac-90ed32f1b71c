import { getChartType } from '@/charts/chartConfig.js'
import { getExcelColumnName } from '@/utils/helpers.js'

export const colorSystems = {
  step: {
    label: 'Step through palette'
  },
  mono: {
    label: 'Single color for all series'
  }
}
export const colorStepOrders = {
  undefined: {
    label: 'Match order of appearance'
  },
  label: {
    label: 'Alphabetical'
  },
  table: {
    label: 'Table order'
  }
}
export const monoVariations = [
  // darken, lighten, saturate, desaturate, opaquer, clearer
  {
    label: 'No change',
    variation: undefined
  },
  {
    label: 'Lighten (5%)',
    variation: ['lighten', 0.05]
  },
  {
    label: 'Lighten (10%)',
    variation: ['lighten', 0.1]
  },
  {
    label: 'Lighten (20%)',
    variation: ['lighten', 0.2]
  },
  {
    label: 'Clearer (10%)',
    variation: ['clearer', 0.1]
  },
  {
    label: 'Clearer (20%)',
    variation: ['clearer', 0.2]
  },
  {
    label: 'Lighten and clearer (5%)',
    variation: [
      ['clearer', 0.05],
      ['lighten', 0.05]
    ]
  },
  {
    label: 'Desaturate and lighten (5%)',
    variation: [
      ['desaturate', 0.05],
      ['lighten', 0.05]
    ]
  }
]

const getLabel = (row, labelColumnId) => {
  // In TheChartEditor we might just have id/label pairs for row headers, so we handle both.
  if (!row) return ''
  return row.label ?? row[`c${labelColumnId}`]
}

const getHeader = (tableColumns, id) => {
  // Similarly, new implementation of getColumnHeader to support getting label first if it's there
  const pos = tableColumns.findIndex(c => c.id === id)
  if (pos === -1) return ''
  return tableColumns[pos].label ?? tableColumns[pos].header ?? getExcelColumnName(pos)
}

const getFlatSeries = (datasets, cols, rowHeaders, byRow, labelColumnId) => {
  /*
  Get a flat list of all series in this chart's datasets.
  
  Each series is an object with datasetIndex and seriesIndex (for quick lookup),
  a label, a tableIndex, and a color. It is sorted in color order based on the color system.
  */
  return datasets.map((ds, dsIndex) => {
    return ds.series.map((seriesId, sIndex) => {
      return {
        sIndex,
        dsIndex,
        label: byRow ? getLabel(rowHeaders.find(row => row.id === seriesId), labelColumnId) : getHeader(cols, seriesId),
        tableIndex: byRow ? rowHeaders.findIndex(row => row.id === seriesId) : cols.findIndex(col => col.id === seriesId)
      }
    })
  }).flat()
}

const getFlatValues = (datasets, cols, rowHeaders, byRow, labelColumnId) => {
  /*
  Does the same as coloredSeries, but for values.
  
  This is currently only for pie charts, but could be extended to single-series charts. However, note that unless
  byRow, values array is blank; it is interpreted as a range between rows from and to.
  */
  return datasets.map((ds, dsIndex) => {
    return ds.values?.map((valueId, vIndex) => {
      return {
        vIndex,
        dsIndex,
        label: byRow ? getHeader(cols, valueId) : getLabel(rowHeaders.find(row => row.id === valueId), labelColumnId),
        tableIndex: byRow ? cols.findIndex(col => col.id === valueId) : getLabel(rowHeaders.findIndex(row => row.id === valueId), labelColumnId)
      }
    })
  }).flat()
}

const colorize = (items, colorSystem) => {
  /*
  Takes a list of items, and adds a `.color` attribute to it based on the colorConfig.
  
  Items should have a `label` and `tableIndex` attribute for sorting.
   */
  let variation = undefined
  if (colorSystem.system === 'mono') {
    // no sort on mono. Set the vary for later - make sure it is an array of arrays
    variation = colorSystem.variation ? [...colorSystem.variation] : []
    if (!Array.isArray(variation[0])) {
      variation = [variation]
    }
  } else {
    // Sort the series if we're doing step
    if (colorSystem.order === 'label') {
      items.sort((a, b) => a.label?.toString().localeCompare(b.label?.toString()))
    } else if (colorSystem.order === 'table') {
      items.sort((a, b) => a.tableIndex - b.tableIndex)
    }
  }
  
  // Helper function to vary a value based on the iterator. Ensures no more than 2 decimal places
  // to avoid floating point shenanigans
  const vary = (v, i) => Number((v * i).toFixed(4))
  
  items.forEach((item, i) => {
    if (colorSystem.system === 'mono') {
      // Pass in a color modification based on the system vary, multiplied by the iterator
      
      if (!variation.length) {
        item.color = colorSystem.color || 0
      } else {
        item.color = {
          index: colorSystem.color || 0,
          modifiers: variation.map(([k, v]) => [k, vary(v, i)])
        }
      }
    } else {
      item.color = i + (colorSystem.startColorIndex || 0)
    }
  })
}

const isValueLookup = (datasets) => {
  return getChartType(datasets) === 'pie' && datasets.length === 1 && datasets[0].series.length === 1
}

export const getColorMap = (datasets, cols, rowHeaders, byRow, labelColumnId, colorSystem) => {
  /*
  Get a color map for the given datasets and chart system.
  
  rowHeaders just needs to contain the cells for labelColumnId, if byRow.
  
  Will return a flat list of either values or series, depending on whether this chart type
  is values-only (currently, just pies). Each item will have a `.color` attribute applied to it.
  */
  const chartType = getChartType(datasets)
  if (chartType === 'scatter' || datasets.length === 0) return []
  
  // We get a flat list of either values or series ready to colorize - contains information like label, table position, etc.
  // that could be useful for the colorize function
  const flattener = isValueLookup(datasets) ? getFlatValues : getFlatSeries
  const flatItems = flattener(datasets, cols, rowHeaders, byRow, labelColumnId).filter(item => item)
  if (flatItems.length) {
    colorize(flatItems, colorSystem)
    return flatItems
  }
}

export const colorDatasets = (datasets, cols, rows, byRow, labelColumnId, colorSystem) => {
  /*
  Applies color properties to the datasets object in-place.
  */
  if (datasets.length === 0) return
  if (getChartType(datasets) === 'scatter') {
    // Always mono for scatter. We don't store on the series (that doesn't make sense), but just on the ds1
    // Keep the series as an array of indexes
    datasets[0].color = colorSystem.color || 0
    return datasets
  }
  
  const colorMap = getColorMap(datasets, cols, rows, byRow, labelColumnId, colorSystem)
  if (!colorMap) return
  datasets.forEach((ds, dsIndex) => {
    ds.series = ds.series.map((id, sIndex) => {
      const color = isValueLookup(datasets)
                    ? ds.values.map((valueId, vIndex) => colorMap.find(v => v.dsIndex === dsIndex && v.vIndex === vIndex).color)
                    : colorMap.find(series => series.dsIndex === dsIndex && series.sIndex === sIndex).color
      return { id, color }
    })
  })
}

