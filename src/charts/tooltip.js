import { getCol, getFirst<PERSON>ol, getFormatter } from '@/charts/utils.js'

const getPieTooltip = ({ datasets, byRow, cols }) => {
  const d1 = datasets[0]
  return {
    callbacks: {
      title: () => '',
      label: (context) => {
        const label = context.label ?? ''
        const value = context.parsed
        
        // Calculate percentage for pie charts
        const total = context.dataset.data.reduce((sum, val) => sum + (val ?? 0), 0)
        const percentage = (value / total * 100) ?? 0
        
        // Get the column for the value. If byRow, then it will be based on
        // its position in the values array; but if byCol, then there is only one
        // column for all values because we only support 1 dataset
        const colId = byRow
                      ? d1.values[context.dataIndex]
                      : d1.series[0]
        
        const formatter = getFormatter(getCol(cols, colId))
        const formattedValue = formatter ? formatter(value) : value
        return `${label}: ${formattedValue} (${percentage.toFixed(1)}%)`
      }
    }
  }
}

const getTooltip = ({ datasets, horizontal, byRow, cols }) => {
  /*
  Get a tooltip object.
  
  Everything uses this except scatter.
   */
  const d1 = datasets[0]
  const ds = datasets
  
  return {
    // https://www.chartjs.org/docs/latest/configuration/tooltip.html#tooltip-callbacks
    
    callbacks: {
      title: (items) => {
        if (d1.chartType === 'pie') return ''
        let title = items[0].label
        if (ds.length === 1 && d1.chartType === 'bar' &&
          (d1.stacking === 'stacked' || d1.stacking === 'stacked100')) {
          const firstCol = getFirstCol(cols, byRow, d1)
          const formatter = getFormatter(firstCol)
          const sum = items.reduce((acc, item) => acc + item.raw, 0)
          title += `: ${formatter(sum)} total`
        }
        return title
      },
      label: (context) => {
        const label = context.dataset.label ?? ''
        let value = horizontal ? context.parsed.x : context.parsed.y
        if (value === null) return ''
        
        // Chart.js datasets "run through", whereas we have it split over
        // two distinct datasets when we have mixed charts. flatCols combines
        // either values or series into one flat array then performs the lookup
        // to get the right col for formatting.
        const col = byRow
                    ? ds.map(ds => ds.values).flat()[context.dataIndex]
                    : ds.map(ds => ds.series).flat()[context.datasetIndex]
        
        if (d1.stacking === 'stacked100') {
          // The plugin sets value to the % as a formatted string; we recalculate it ourselves
          // and get the original value.
          // https://github.com/y-takey/chartjs-plugin-stacked100
          const total = context.chart.data.datasets.reduce((sum, ds) => sum + ds.data[context.dataIndex] ?? 0, 0)
          value = context.chart.data.originalData[context.datasetIndex][context.dataIndex] ?? 0
          const percentage = (value / total * 100) ?? 0
          const formatter = getFormatter(getCol(cols, col))
          if (formatter) value = formatter(value)
          return `${label}: ${percentage.toFixed(1)}% (${value})`
        } else {
          const formatter = getFormatter(getCol(cols, col))
          if (formatter) value = formatter(value)
          return `${label}: ${value}`
        }
      }
    }
  }
}

const getScatterTooltip = ({ datasets, byRow, cols }) => {
  const d1 = datasets[0]
  return {
    callbacks: {
      label: (context) => {
        const values = [context.parsed.x, context.parsed.y]
        let formatted
        if (byRow) {
          // Only one formatted as both values come from the same column
          const formatter = getFormatter(getCol(cols, d1.values[context.dataIndex]))
          formatted = values.map(v => formatter(v))
        } else {
          // Format 2 values differently as they come from different columns
          const formatters = d1.series.map(id => getFormatter(getCol(cols, id)))
          formatted = values.map((v, i) => formatters[i](v))
        }
        if (context.dataset.labels) {
          return `${context.dataset.labels[context.dataIndex]} (${formatted[0]}, ${formatted[1]})`
        }
        return `${formatted[0]}, ${formatted[1]}`
      }
    }
  }
}

export const tooltips = {
  bar: getTooltip,
  line: getTooltip,
  pie: getPieTooltip,
  scatter: getScatterTooltip
}

