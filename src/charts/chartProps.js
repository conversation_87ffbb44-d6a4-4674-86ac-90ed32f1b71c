/*
Methods to prepare chart to a point that is read for chartdata.js.

This is the final step before we take a raw snapshot of data, so it must include everything needed for charting.

- strips down cols and rows to only those required for this specific chart
- takes only the important features from the database chart (title, subtitle, datasets, horizontal, byRow etc)
- returns a `chartProps` of this information
- this can be passed directly into chartdata.js, which preps the data for the chart.js library

 */
import { getChartType } from '@/charts/chartConfig.js'
import { colorDatasets } from '@/charts/chartColors.js'
import { getExcelColumnName } from '@/utils/helpers.js'
import { filterQuerySpec, getChartQuerySpec, tableToCols } from '@/db/query/utils.js'

const prepChartCols = (def, cols) => {
  /*
  Given a chart definition, and table cols, return only cols relevant to the chart.
  
  If byRow: Remove any cols not in the values
  If !byRow: Remove any cols not in the series
  
  We also then map the column to a smaller object which only includes what we need: id, header, props, type.
  This removes other data not needed for chart rendering, like field, isLabel, width.
  */
  const flatCols = def.datasets.map(ds => ds[def.byRow ? 'values' : 'series']).flat()
  const flatColIds = flatCols.map(col => typeof col === 'number' ? col : col.id)
  return cols.filter(col => col.id === def.axis.column || flatColIds.includes(col.id)).map(col => ({
      id: col.id,
      type: col.type,
      props: col.props,
      header: col.header || getExcelColumnName(cols.findIndex(c => c.id === col.id))  // use og position for excel naming
    })
  )
}

const getRowFilter = (type, def) => {
  /*
  Given chart definition and table rows, get only the relevant row Ids. If we're getting them all,
  then returns undefined.
   */
  
  if (def.byRow) {
    // Flatten the series and get all the row ids
    return {
      rowIds: def.datasets.map(ds => ds.series).flat().map(row => typeof row === 'number' ? row : row.id)
    }
    
  } else if (type === 'pie') {
    // Pie is only a single dataset; get its values
    return {
      rowIds: def.datasets[0].values
    }
    
  } else {
    // Return a positional range only if there are actual values defined
    // Check if rows has any defined properties (from or to)
    if (def.rows && (def.rows.from !== undefined || def.rows.to !== undefined)) {
      return {
        posRange: def.rows
      }
    }
  }
}

const prepDatasets = (datasets, cols, byRow) => {
  /*
  Takes a copy of datasets and removes superfluous data for a cleaner export.
  
  Also removes any columns that are not in cols; these have been deleted..
  */
  const data = JSON.parse(JSON.stringify(datasets))
  data.forEach(ds => {
    if (ds.values?.length === 0) delete ds['values']
    if (ds.stacking === '') delete ds['stacking']
    if (byRow && ds.values?.length > 0) {
      ds.values = ds.values.filter(col => cols.find(c => c.id === col))
    } else if (!byRow && ds.series?.length > 0) {
      ds.series = ds.series.filter(col => cols.find(c => c.id === col))
    }
  })
  return data
}

const optional = (obj) => {
  // Return a reduced version of obj, removing any keys that are empty strings, empty strings, empty objects or false
  obj = JSON.parse(JSON.stringify(obj))
  for (const key in obj) {
    // A simple label axis with show: true can be removed as it will be assumed
    if (key === 'labelAxis' && Object.keys(obj[key]).length === 1 && obj[key].show === true) {
      delete obj[key]
    }
    if (obj[key] === '' || obj[key] === null || obj[key] === undefined || obj[key] === false) {
      delete obj[key]
    }
  }
  return obj
}

export const getChartProps = (title, chart, tableCols, apiView) => {
  /*
  This gets an object ready for addChartData.
  
  This doesn't hit the database - it prepares for a database hit. We can construct this whenever the store changes
  and when we need the data, run it through getChartData to actually hit the database.
  
  It:
  - Strips down cols and rows to only those required for this specific chart
  - Takes only the important features from the database chart (title, subtitle, datasets, horizontal, byRow etc)
   */
  const viewColumns = tableToCols(tableCols, apiView)
  
  // This method removes any table-level column filters
  const querySpec = getChartQuerySpec(tableCols, apiView)
  const type = getChartType(chart.datasets)
  
  const colors = chart.design?.colors === undefined
                 ? { system: 'step' }
                 : { ...chart.design.colors }
  
  // Add on a specific row or pos range filter for this chart
  const spec = filterQuerySpec(querySpec, getRowFilter(type, chart))
  
  const cols = prepChartCols(chart, viewColumns)
  const datasets = prepDatasets(chart.datasets, cols, chart.byRow)
  
  return {
    type,
    cols,
    datasets,
    
    // Retain view-related information for the dbConn access
    querySpec: spec,
    
    // These are needed by getChartData; they'll be removed. We put them here so that a computed property of
    // props will update whenever these change
    colors,
    
    // these are passed right on through on getChartData
    ...optional({
      title: title,
      subtitle: chart.subtitle,
      horizontal: chart.horizontal,
      byRow: chart.byRow,
      labelAxis: chart.axis,
      theme: chart.theme
    })
  }
}

export const getChartData = async (chartProps, dbTable) => {
  /*
  Take a chartDef (defined above) and augments with actually row data from the dbConn database.
  
  Also requires for coloring:
    byRow: chartData.byRow,
    colors: chartData.design.colors,
    
  This in the final step before we take a "snapshot" of a chart. The output here can be saved in JSON
  and will rely on nothing else (except possibly a base theme) to render. Applies colorization to the
  datasets based on the 'colors' system definition in chart design.
  */
  const rows = await dbTable.chartData(chartProps.cols.map(col => col.id), chartProps.querySpec)
  const chartData = { ...chartProps, rows }
  
  // take a copy of datasets as it will be edited in-place
  chartData.datasets = JSON.parse(JSON.stringify(chartData.datasets))
  colorDatasets(chartData.datasets, chartData.cols, chartData.rows, chartData.byRow, chartData.labelAxis?.column, chartData.colors)
  
  delete chartData.colors
  delete chartData.groups
  delete chartData.rowFilter
  return chartData
}
