import { columnTypes, percFormatter, repr } from '@/utils/formats.js'
import { getExcelColumnName } from '@/utils/helpers.js'
import { Big } from 'big.js'
import { getCol, getFirstCol, getFormatter } from '@/charts/utils.js'
import { tooltips } from '@/charts/tooltip.js'

export class ChartError extends Error {
  constructor (message) {
    super(message) // Call the parent class constructor
    this.name = this.constructor.name // Set the error name to the class name
  }
}

const chartableValue = (val) => {
  // Converts various data types into values that can be used in charts
  // Returns: number, boolean, or null
  if (typeof val == 'boolean') return val          // Keep booleans as-is
  if (val instanceof Big) return val.toNumber()    // Convert Big.js numbers to regular numbers
  if (val === '') return null                      // Convert empty strings to null
  if (typeof val === 'number') return val          // Keep numbers as-is
  return null                                      // Convert all other types to null
}

const reprLabel = (label, labelCol, keepTime) => {
  /*
  Takes a value to use for a dataset label, and returns a representation for the scale.

  If it's a string, it just returns the string. For datetime values on a line,
  we keep it as a date object as the scales need it. Otherwise, we run it through
  repr.
  */
  if (labelCol === undefined) return String(label)
  if (labelCol.type === 'text') return String(label) || '(No label)'
  if (labelCol.type === 'datetime' && keepTime) return label
  const labelRepr = repr(label, labelCol.type, labelCol.props)
  if (labelRepr === null && !keepTime) return '(No label)'
  return labelRepr
}

// Simple function that returns a label for a row if we don't have one. Takes in an unused first
// attribute, so it can be used on map easily.
export const autoRowLabel = (_, index) => `Row ${index + 1}`

const getColIndexFromId = (cols, rows, id, isCol) => {
  /*
  Get either the positional index of either a column (if isCol) or row, given the id.
  */
  if (isCol) {
    return cols.findIndex(col => col.id === id)
  }
  return rows.findIndex(row => row.id === id)
}
export const getColumnHeaderById = (tableColumns, id) => {
  /*
  Given tableColumns and an id, returns its header or, if it doesn't have one, an Excel-style column name.
  */
  const pos = tableColumns.findIndex(c => c.id === id)
  return pos > -1 ? tableColumns[pos].header || getExcelColumnName(pos) : undefined
}

export const getRowHeader = (tableColumns, labelColumnId, row, rowIndex) => {
  /*
  Given table columns and a label column ID, return a rows "header"
  
  If there is no label column ID, we return a simple "Row X", where x=rowIndex.
  */
  const col = tableColumns.find(c => c.id === labelColumnId)
  if (col) {
    return repr(row[`c${labelColumnId}`], col.type, col.props)
  }
  return autoRowLabel(row, rowIndex)
}

export const getRowHeaderById = (cols, rows, rowId, labelColumnId) => {
  // labelColumnId should equal state.data.axis.column if not undefined, or tableStore.labelColumnId
  const rowIndex = getColIndexFromId(cols, rows, rowId, false)
  return getRowHeader(cols, labelColumnId, rows[rowIndex], rowIndex)
}

export const getChartType = (datasets) => {
  // If there's only one dataset, return its chart type; if there are none, return undefined; if there are two, return 'mixed'
  if (datasets.length === 0) return undefined
  if (datasets.length === 1) return datasets[0].chartType
  return 'mixed'
}

const timeXAxis = (datasets, labelColumn) => {
  // We show a time-style X axis if the label column is datetime and
  // every dataset is line
  return (labelColumn?.type === 'datetime') &&
    (datasets.every(ds => ds.chartType === 'line'))
}

const getChartSeries = (dataset, label, data, isHorizontal, isSecond, seriesObj) => {
  /*
  Get Chart Series

  Just takes a bunch of properties and turns it into a chart.js series.
  */
  const type = dataset.chartType
  const s = {
    data,
    label,
    type,
    refId: seriesObj.id, // these makes it easy to refer back to original data
    order: isSecond ? 1 : 0,
    fill: type === 'line' && dataset.lineFill ? 'origin' : false,
    tension: type === 'line' && dataset.tension || 0,
    stepped: type === 'line' && dataset.stepped,
    
    // used by the theme plugin; this is an index or definition into the theme colors
    color: seriesObj.color,
    
    // neccessary otherwise when switching config, chart.js will 'remember' the old setting
    xAxisID: undefined,
    yAxisID: undefined
  }
  if (isHorizontal) {
    s.xAxisID = 'x' + (isSecond && dataset.axis?.show ? '2' : '')
  } else {
    s.yAxisID = 'y' + (isSecond && dataset.axis?.show ? '2' : '')
  }
  return s
}

const parseSeriesObject = (obj) => {
  /*
  if obj is Number, then return { id: Number }.
  Otherwise, return the obj.
  */
  if (typeof obj === 'number') {
    return { id: obj }
  }
  return obj
}
const getBarLineData = ({ datasets, labelAxis, byRow, horizontal, rows, cols }) => {
  let labels
  let seriesConverter
  
  const labelCol = getCol(cols, labelAxis.column)
  
  if (byRow) {
    // Get labels - they're just the column headers
    const colIds = datasets[0].values || []
    const labelFormatter = getFormatter(labelCol)
    labels = colIds.map(id => getColumnHeaderById(cols, id)).filter(label => label !== undefined)
    
    seriesConverter = (dataset, dsIndex) => {
      return dataset.series.map((seriesObject, index) => {
        const seriesObj = parseSeriesObject(seriesObject)
        const row = rows.find(r => r.id === seriesObj.id)
        if (!row) return undefined
        const data = colIds.map(colId => chartableValue(row[`c${colId}`]))
        const label = labelFormatter ? labelFormatter(row[`c${labelCol.id}`]) : autoRowLabel(row, index)
        return getChartSeries(dataset, label, data, horizontal, dsIndex > 0, seriesObj)
      })
    }
  } else {
    // Get labels
    if (labelCol) {
      const keepTime = timeXAxis(datasets, labelCol)
      labels = rows.map(row => row[`c${labelAxis.column}`]).map(label => reprLabel(label, labelCol, keepTime))
    } else {
      labels = rows.map(autoRowLabel)
    }
    seriesConverter = (dataset, dsIndex) => {
      return dataset.series.map(seriesObject => {
        const seriesObj = parseSeriesObject(seriesObject)
        const col = getCol(cols, seriesObj)
        if (!col) return undefined
        const data = rows.map(row => chartableValue(row[`c${col.id}`]))
        return getChartSeries(dataset, col.header, data, horizontal, dsIndex > 0, seriesObj)
      })
    }
  }
  return {
    labels,
    datasets: datasets.map(seriesConverter).filter(cs => cs !== undefined).flat()
  }
}

const getScale = (axis, col, isLabelAxis) => {
  /*
  Get the chart.js scale definition.
  
  Requires our chart axisDef, the column used for formatting, and whether this is
  for labels or values.
  */
  if (!axis) return {}
  let scale = {
    display: axis.show,
    title: {
      display: Boolean(axis.label),
      text: axis.label
    },
    grid: {
      drawOnChartArea: true
    },
    ticks: {}  // Initialize empty ticks object
  }
  
  if (axis.maxTicks !== undefined) {
    scale.ticks.maxTicksLimit = axis.maxTicks
  }
  
  if (axis.range) {
    scale.beginAtZero = axis.range === 'zero'
    if (axis.range === 'custom') {
      scale.min = axis.min
      scale.max = axis.max
    }
  }
  if (col) {
    if (isLabelAxis) {
      scale.position = 'bottom'
      scale.type = 'category' // default to category; date lines will override this
    } else {
      // For values scales, we want to format the ticks
      scale.position = 'left'
      scale.ticks.callback = getFormatter(col, true)
    }
  }
  if (isLabelAxis) {
    scale.ticks.maxRotation = 90
  }
  return scale
}

const addScaleStacking = (options, scale, stackType) => {
  /*
  Augments scale with stacking information on the scale.
  
  stackType can be:
  stacked (normal stacking)
  stacked100 (stacking to 100%)
  anything else - no stack.
  */
  if (stackType === 'stacked' || stackType === 'stacked100') {
    scale.stacked = true
    options.scales.x.stacked = true
    if (stackType === 'stacked100') {
      scale.max = 100
      scale.ticks = {
        callback: v => percFormatter({}).format(v / 100)
      }
      options.plugins.stacked100 = {
        enable: true,
        
        // this doesn't work reliably so we create our own (switching from one
        // to another and tooltips don't update) so we always do our own - this
        // also gives us consistency with render rather than rely on the plugin
        replaceTooltipLabel: true
      }
    }
  }
}

const annotateTimeScale = (scale, props) => {
  /*
  Annotate a time-based scale with the display format.
  */
  scale.type = 'time'
  scale.time = columnTypes['datetime'].getScaleTime(props)
}

const horizontalScaleSwitch = (options) => {
  /*
  Switches the x and y scales for horizontal bar charts.
  
  This is because the scales are flipped for horizontal bar charts.
  */
  options.indexAxis = 'y'
  options.scales = {
    x: {
      ...options.scales.y,
      position: 'bottom'
    },
    y: {
      ...options.scales.x,
      position: 'left'
    },
    x2: (options.scales.y2) ? {
      ...options.scales.y2,
      position: 'top'
    } : undefined
  }
  // Must be deleted - undefined throws a chart.js error
  if (options.scales.x2 === undefined) {
    delete options.scales.x2
  }
  
}
const getBaseOptions = ({ type, title, subtitle, datasets, byRow, labelAxis, cols }) => {
  // count how many series we have in each dataset of chartStoreData
  const seriesCount = datasets.reduce((acc, ds) => acc + ds.series.length, 0)
  const showLegend = type === 'scatter' ? false : (type === 'pie' || seriesCount > 1)
  
  // Set interaction mode based on chart type; line and scatter is 'nearest' or we get issues
  // with high point density charts and it highlighting the left-most point; 'index' is for
  // other charts so that stacked charts can show multiple data points in one tooltip
  const interactionMode = (type === 'line' || type === 'scatter') ? 'nearest' : 'index'
  
  const options = {
    maintainAspectRatio: false,
    indexAxis: 'x',
    interaction: {
      mode: interactionMode
    },
    plugins: {
      title: {
        display: Boolean(title),
        text: title
      },
      subtitle: {
        display: Boolean(subtitle),
        text: subtitle
      },
      legend: {
        display: showLegend,
        position: 'top'
      }
    }
  }
  
  // Everything but pie has at least an X and Y scale
  if (type !== 'pie') {
    const d1 = datasets[0]
    const valueCol = getFirstCol(cols, byRow, d1)
    const labelCol = byRow ? undefined : getCol(cols, labelAxis.column)
    options.scales = {
      x: getScale(labelAxis, labelCol, true),
      y: getScale(d1.axis, valueCol)
    }
  }
  
  return options
}

/*
For each chart type it should have three methods to construct a chart:

- getData: Returns the data object for the chart.
- addOptions: Adds any additional options to the chart options object.
- getTooltip: Returns the tooltip object for the chart.
 */

const chartConstructors = {
  scatter: {
    getData: ({ datasets, labelAxis, byRow, rows, cols }) => {
      /*
      Scatters right now are just one dataset. The two series actually represent the X and Y values.
      
      In the future, multiple datasets could be represented by a third column whose property would group values
      into different datasets.
       */
      const ds = datasets[0]
      let labels
      let data
      
      if (byRow) {
        const rowsXY = ds.series.map(id => rows.find(r => r.id === id))
        labels = ds.values.map(id => getColumnHeaderById(cols, id))
        data = ds.values.map(id => rowsXY.map(row => chartableValue(row[`c${id}`])))
      } else {
        const labelCol = getCol(cols, labelAxis.column)
        if (labelCol) {
          labels = rows.map(row => row[`c${labelCol.id}`])
          labels = labels.map(label => reprLabel(label, labelCol))
        }
        data = rows.map(row => {
          return {
            x: chartableValue(row[`c${ds.series[0]}`]),
            y: chartableValue(row[`c${ds.series[1]}`])
          }
        })
      }
      return {
        datasets: [{
          data,
          labels,
          color: ds.color
        }]
      }
    },
    
    addOptions: (options, { datasets, labelAxis, byRow, cols }) => {
      const ds1 = datasets[0]
      const seriesCols = ds1.series.map(id => getCol(cols, id))
      
      // Removed this check - scatter 2 cols doesn't need values, not sure why we had this?!
      //if (ds1.values === undefined || ds1.values.length === 0) throw new ChartError('Scatter chart requires at least one value')
      if (seriesCols.length !== 2) throw new ChartError('Scatter chart requires exactly two series')
      if (byRow) {
        const col = getCol(cols, ds1.values[0])
        options.scales = {
          x: getScale(labelAxis, col),
          y: getScale(ds1.axis, col)
        }
      } else {
        options.scales = {
          x: getScale(labelAxis, seriesCols[0]),
          y: getScale(ds1.axis, seriesCols[1])
        }
      }
    },
    
    getTooltip: tooltips.scatter
  },
  
  pie: {
    addOptions: (options, { datasets }) => {
      options.cutout = `${datasets[0].donutCutout || 0}%`
    },
    
    getTooltip: tooltips.pie,
    
    getData: ({ datasets, byRow, labelAxis, rows, cols }) => {
      const ds = datasets[0]
      const series = parseSeriesObject(ds.series[0])
      const labelCol = getCol(cols, labelAxis.column)
      let labels = []
      let datapoints = []
      
      // Create dsValues that makes sure every value is an object. If it's a number, create an object { id: number }
      const dsValues = ds.values?.map(obj => typeof obj === 'number' ? { id: obj } : obj)
      if (dsValues) {
        if (byRow) {
          // If charting by row, then we work with one row, and values are the columns in that row. Labels are column headers
          const row = rows.find(r => r.id === series.id)
          if (row) {
            labels = dsValues.map(obj => getColumnHeaderById(cols, obj.id)).filter(label => label !== undefined)
            datapoints = dsValues.map(obj => chartableValue(row[`c${obj.id}`]))
          }
        } else {
          // If charting by column, then we work with one column, and values are the rows in that column. Labels are the
          // labelColumnId at each row
          const valueRows = dsValues.map(obj => rows.find(r => r.id === obj.id)).filter(row => row !== undefined)
          datapoints = valueRows.map(row => row[`c${series.id}`]).map(chartableValue)
          if (labelCol) {
            labels = valueRows.map(row => reprLabel(row[`c${labelCol.id}`], labelCol))
          } else {
            labels = valueRows.map(autoRowLabel)
          }
        }
      }
      // We add on our own special 'colors' value used by the theme to customize colors
      return {
        labels,
        datasets: datapoints ? [{
          data: datapoints,
          color: series.color
        }] : []
      }
    }
    
  },
  
  line: {
    getData: getBarLineData,
    getTooltip: tooltips.line,
    addOptions: (options, { datasets, byRow, horizontal, labelAxis, cols }) => {
      // Lines specifically can augment the X axis with date scale information
      addScaleStacking(options, options.scales.y, datasets[0].stacking)
      if (horizontal) {
        horizontalScaleSwitch(options)
      }
      if (!byRow) {
        const labelCol = getCol(cols, labelAxis.column)
        if (labelCol?.type === 'datetime') {
          annotateTimeScale(options.scales.x, labelCol.props)
        }
      }
    }
  },
  
  bar: {
    getData: getBarLineData,
    getTooltip: tooltips.bar,
    addOptions: (options, { datasets, horizontal }) => {
      const d1 = datasets[0]
      addScaleStacking(options, options.scales.y, d1.stacking)
      if (horizontal) {
        horizontalScaleSwitch(options)
      }
    }
  },
  
  mixed: {
    getData: getBarLineData,
    getTooltip: tooltips.bar,
    addOptions: (options, { datasets, labelAxis, horizontal, byRow, cols }) => {
      const d1 = datasets[0]
      const d2 = datasets.length === 2 ? datasets[1] : undefined
      
      addScaleStacking(options, options.scales.y, d1.stacking)
      if (d2?.axis?.show && d2.series.length) {
        const valueCol = getFirstCol(cols, byRow, d2)
        options.scales.y2 = getScale(d2.axis, valueCol)
        options.scales.y2.position = 'right'
        addScaleStacking(options, options.scales.y2, d2.stacking)
        options.scales.y2.grid.drawOnChartArea = !d1.axis?.show // only want the grid lines for one axis to show up
      }
      if (horizontal) {
        horizontalScaleSwitch(options)
      }
      
      // if every dataset chartType is line, annotate the x scale with time
      const labelColumn = getCol(cols, labelAxis.column)
      if (timeXAxis(datasets, labelColumn)) {
        annotateTimeScale(options.scales.x, labelColumn.props)
      }
    }
    
  }
}

export const getChartConfig = (chartData) => {
  /*
  Get a Chart.js config object.

  Suitable to pass directly as the second attribute in the Chart(canvas, config).
  */
  
  // Make sure chartProps has all neccessary attributes
  // eslint-disable-next-line no-prototype-builtins
  const missing = ['datasets', 'rows', 'cols'].filter(attr => !chartData.hasOwnProperty(attr))
  if (missing.length) {
    console.error(`Missing attributes in chartProps: ${missing.join(', ')}`)
    return undefined
  }
  
  if (chartData.labelAxis === undefined) {
    chartData.labelAxis = { show: true }
  }
  const props = { type: getChartType(chartData.datasets), ...chartData }
  props.horizontal = props.horizontal || false
  
  try {
    const options = getBaseOptions(props)
    const chartMethods = chartConstructors[props.type]
    chartMethods.addOptions(options, props)
    options.plugins.tooltip = chartMethods.getTooltip(props)
    options.plugins.themes = props.theme
    
    // filtered out any datasets that have empty series
    props.datasets = props.datasets.filter(ds => ds.series.length > 0)
    const data = (props.datasets.length > 0) ? chartMethods.getData(props) : undefined
    return { type: props.type, options, data }
    
  } catch (error) {
    if (error instanceof ChartError) {
      return {}
    } else {
      throw error
    }
  }
  
}
