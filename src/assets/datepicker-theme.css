/*
Datepicker theme
 */

.dp__main, .dp__menu {
  /* General */
  --dp-font-family: theme('fontFamily.sans');
  --dp-border-radius: theme('borderRadius.md');
  --dp-cell-border-radius: theme('borderRadius.md');
  --dp-common-transition: theme('transitionProperty.all') theme('transitionDuration.100') theme('transitionTimingFunction.in-out');

  /* Sizing */
  --dp-button-height: auto;
  --dp-month-year-row-height: theme('spacing.9');
  --dp-month-year-row-button-size: theme('spacing.9');
  --dp-button-icon-height: theme('spacing.5');
  --dp-cell-size: theme('spacing.9');
  --dp-cell-padding: theme('spacing[1.5]');
  --dp-common-padding: theme('spacing[2.5]');
  --dp-input-icon-padding: theme('spacing.9');
  --dp-input-padding: theme('spacing[1.5]') theme('spacing[7]') theme('spacing[1.5]') theme('spacing.3');
  --dp-menu-min-width: theme('width.64');
  --dp-action-buttons-padding: theme('spacing[0.5]') theme('spacing[1.5]');
  --dp-row-margin: theme('spacing[1.5]') 0;
  --dp-calendar-header-cell-padding: theme('spacing.2');
  --dp-two-calendars-spacing: theme('spacing[2.5]');
  --dp-overlay-col-padding: theme('spacing[0.5]');
  --dp-time-inc-dec-button-size: theme('spacing.8');
  --dp-menu-padding: theme('spacing[1.5]') theme('spacing.2');

  /* Font sizes */
  --dp-font-size: theme('fontSize.sm');
  --dp-preview-font-size: theme('fontSize.xs');
  --dp-time-font-size: theme('fontSize.4xl');

  /* Transitions */
  --dp-animation-duration: theme('transitionDuration.100');
  --dp-menu-appear-transition-timing: cubic-bezier(.4, 0, 1, 1);
  --dp-transition-timing: theme('transitionTimingFunction.out');
}

.dp__input {
  text-align: right;
  border-radius: 0;
}

.dp__action_row {
  background-color: theme('colors.zinc.100');
  border-top: 1px solid theme('colors.zinc.200');
}

.dp__action_button {
  font-weight: theme('fontWeight.medium');
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: theme('spacing.1') theme('spacing.3');
  font-size: theme('fontSize.sm');
}

/* Apply the primary button style to the select button */
.dp__action_select {
  border-width: 1px;
  border-radius: theme('borderRadius.md');
  box-shadow: theme('boxShadow.xs');
  border-color: transparent;
  color: theme('colors.white');
  background-color: theme('colors.fuchsia.600');
}

.dp__action_select:hover {
  background-color: theme('colors.fuchsia.700');
}

.dp__action_select:focus {
  outline: none;
}

/* Style the cancel button as text-only */
.dp__action_cancel {
  background-color: transparent;
  border: none;
  color: theme('colors.zinc.700');
  font-size: theme('fontSize.xs');
}

.dp__action_cancel:hover {
  color: theme('colors.zinc.900');
}

.dp__action_cancel:focus {
  outline: none;
}

.dp__theme_light {
  --dp-background-color: theme('colors.white');
  --dp-text-color: theme('colors.zinc.700');
  --dp-primary-color: theme('colors.fuchsia.500');
  --dp-secondary-color: theme('colors.fuchsia.200');
  --dp-hover-color: theme('colors.fuchsia.100');
  --dp-hover-text-color: theme('colors.zinc.900');
  --dp-hover-icon-color: theme('colors.fuchsia.500');
  --dp-disabled-color: theme('colors.zinc.200');
  --dp-disabled-text-color: theme('colors.zinc.400');
  --dp-border-color: theme('colors.zinc.200');
}
