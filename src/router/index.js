import { createRouter, createWebHistory } from 'vue-router'
import { useTableStore } from '@/stores/table.js'
import { useChartStore } from '@/stores/chart.js'
import { useAppStore } from '@/stores/app.js'
import appRoutes from '@/router/app.js'
import publicRoutes from '@/router/public.js'
import accountRoutes from '@/router/account.js'
import micrositesRoutes from '@/router/microsites.js'

const routes = [
  ...accountRoutes,
  ...appRoutes,
  ...publicRoutes,
  ...micrositesRoutes
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

router.beforeEach((to, from) => {
  // always push out the table queue when router is changing
  const tableStore = useTableStore()
  tableStore.flushQueue()
  
  // if the new path doesn't start /tables, then reset the tablestore
  if (!to.path.startsWith('/app/tables')) {
    tableStore.$reset()
    useChartStore().$reset()
  }
  
  // If table or view is changing, make sure to reset the selected columns and rows
  if (
    (from.params.tableId !== to.params.tableId) ||
    (from.params.chartId !== to.params.chartId) ||
    (from.params.viewId !== to.params.viewId)
  ) {
    useAppStore().clearSelection()
  }
  return true
})

export default router
