import AppDashboard from '@/views/app/AppDashboard.vue'
import DataEditor from '@/views/app/DataEditor.vue'
import App from '@/views/app/App.vue'
import AuthOnly from '@/views/AuthOnly.vue'

const getChartRoutes = (prefix) => {
  return [
    {
      path: '',
      name: `app-${prefix}-charts`,
      props: (route) => ({
        ...route.params,
        mode: 'charts'
      }),
      component: DataEditor
    },
    {
      path: ':chartId',
      name: `app-${prefix}-chart`,
      props: true,
      component: DataEditor
    },
    
    {
      path: ':chartId/export/:chartExportFormat?',
      name: `app-${prefix}-chart-export`,
      props: (route) => ({
        ...route.params,
        mode: 'export'
      }),
      component: DataEditor
    },
    {
      path: ':chartId/edit/:editStep?',
      name: `app-${prefix}-chart-edit`,
      props: (route) => ({
        ...route.params,
        editStep: Number(route.params.editStep) || 0,
        mode: 'edit'
      }),
      component: DataEditor
    }
  ]
}

export default [
  {
    path: '/app',
    component: AuthOnly,
    children: [
      {
        path: '',
        component: App,
        children: [
          {
            path: '',
            name: 'app',
            redirect: { name: 'app-dashboard' }
          },
          {
            path: 'dashboard',
            name: 'app-dashboard',
            component: AppDashboard
          },
          {
            path: 'projects/:projectId',
            name: 'app-projects',
            props: true,
            component: AppDashboard
          },
          
          // Table and view
          {
            path: 'tables/:tableId',
            name: 'app-table',
            props: true,
            component: DataEditor
          },
          {
            path: 'tables/:tableId/views/:viewId',
            name: 'app-view',
            props: true,
            component: DataEditor
          },
          
          // Add-chart routes for table and view - we want this outside /charts/...
          // for better router tab matching
          {
            path: 'tables/:tableId/add-chart',
            name: `app-table-chart-add`,
            props: (route) => ({
              ...route.params,
              mode: 'add'
            }),
            component: DataEditor
          },
          {
            path: 'tables/:tableId/views/:viewId/add-chart',
            name: `app-view-chart-add`,
            props: (route) => ({
              ...route.params,
              mode: 'add'
            }),
            component: DataEditor
          },
          
          // Viewing, editing and exporting charts on a table or a view
          {
            path: 'tables/:tableId/charts',
            children: getChartRoutes('table')
          },
          {
            path: 'tables/:tableId/views/:viewId/charts',
            children: getChartRoutes('view')
          }
        ]
      }
    ]
  }
]
