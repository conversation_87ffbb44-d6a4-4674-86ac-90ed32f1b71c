/*
Public routes are routes that are accessible without authentication. It includes a consisitent navbar and footer
It includes sign-up and login routes, as well as the homepage, marketing pages and the blog.
 */
import RTPView from '@/views/microsites/rtp/RTPView.vue'
import VenuesView from '@/views/microsites/rtp/VenuesView.vue'
import VenueView from '@/views/microsites/rtp/VenueView.vue'

export default [
  {
    path: '/microsites/toothsum',
    component: RTPView,
    children: [
      {
        path: '',
        name: 'rtp-home',
        redirect: '/microsites/toothsum/venues/map'
      },
      {
        path: 'venues',
        children: [
          {
            path: '',
            redirect: '/microsites/toothsum/venues/map'
          },
          {
            path: ':tab',
            name: 'rtp-venues',
            component: VenuesView,
            props: true
          }
        ]
      },
      {
        path: 'venue/:venue',
        children: [
          {
            path: '',
            name: 'rtp-venue-home',
            redirect: to => {
              return {
                name: 'rtp-venue', params: {
                  venue: to.params.venue,
                  tabId: 'points'
                }
              }
            }
          },
          {
            path: 'results/:week',
            name: 'rtp-venue-results',
            component: VenueView,
            props: route => ({
              venue: route.params.venue,
              tabId: 'results',
              week: Number(route.params.week)
            })
          },
          {
            path: ':tabId',
            name: 'rtp-venue',
            component: VenueView,
            props: true
          },
          {
            path: 'players/:playerId',
            name: 'rtp-venue-players',
            component: VenueView,
            props: route => ({
              venue: route.params.venue,
              tabId: 'players',
              playerId: Number(route.params.playerId)
            })
          }
        ]
      }
    ]
  }
]
