import LoginView from '@/views/public/accounts/LoginView.vue'
import HomepageView from '@/views/public/HomepageView.vue'
import NotFoundView from '@/views/public/NotFoundView.vue'
import BlogView from '@/views/public/blog/BlogView.vue'
import PostView from '@/views/public/blog/PostView.vue'
import PublicView from '@/views/public/PublicView.vue'
import ContactView from '@/views/public/ContactView.vue'
import PasswordResetView from '@/views/public/accounts/PasswordResetView.vue'
import PasswordResetConfirmView from '@/views/public/accounts/PasswordResetConfirmView.vue'
import SignUpView from '@/views/public/accounts/SignUpView.vue'
import ActivateAccountView from '@/views/public/accounts/ActivateAccountView.vue'

/*
Public routes are routes that are accessible without authentication. It includes a consisitent navbar and footer
It includes sign-up and login routes, as well as the homepage, marketing pages and the blog.
 */
export default [
  {
    path: '',
    component: PublicView,
    children: [
      {
        path: 'blog',
        children: [
          {
            path: '',
            name: 'blog',
            component: BlogView
          },
          {
            path: 'preview/:slug',
            name: 'preview',
            component: PostView,
            meta: {
              preview: true
            }
          },
          {
            path: ':slug',
            name: 'post',
            component: PostView,
            props: true
          }
        ]
      },
      {
        path: '',
        name: 'homepage',
        component: HomepageView
      },
      {
        path: 'sign-up',
        name: 'sign-up',
        component: HomepageView,
        props: { signup: true }
      },
      {
        path: 'contact-us',
        name: 'contact-us',
        component: ContactView
      }
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView
  },
  {
    path: '/alpha-sign-up',
    name: 'sign-up',
    component: SignUpView
  },
  {
    path: '/activate/:uid/:token',
    name: 'activateAccount',
    component: ActivateAccountView
  },
  {
    path: '/reset-password',
    name: 'password-reset',
    component: PasswordResetView
  },
  {
    path: '/reset-password/confirm/:uid/:token',
    name: 'password-reset-confirm',
    component: PasswordResetConfirmView
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: NotFoundView
  }
]
