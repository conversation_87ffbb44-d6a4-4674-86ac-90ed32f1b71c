import Chart from 'chart.js/auto'
import {getChartConfig} from '@/charts/chartConfig.js'
import {chartThemePlugin} from '@/chartjs-plugins/plugin.themes.js'
import 'chartjs-adapter-date-fns'
import ChartjsPluginStacked100 from 'chartjs-plugin-stacked100'

;(function () {
  if (window.datahero) return
  window.datahero = {
    charts: {},
    themes: {}
  }
  
  Chart.register(chartThemePlugin)
  Chart.register(ChartjsPluginStacked100)
  
  // enabled by default - see https://www.chartjs.org/docs/latest/general/colors.html "If you are using the UMD version of Chart.js, ..."
  Chart.defaults.plugins.colors.enabled = false
  
  const createChartConfig = function (id, element) {
    const themeId = element.getAttribute('data-datahero-theme')
    const themeMode = element.getAttribute('data-datahero-mode')
    const autoMode = element.getAttribute('data-datahero-automode') !== 'false'
    const json = JSON.parse(document.getElementById(`datahero-${id}`).textContent)
    if (!json) {
      return
    }
    const theme = window.datahero.themes[themeId] || window.datahero.themes.default
    json.theme = {
      theme: theme,
      mode: themeMode,
      allowAutoModes: autoMode
    }
    return getChartConfig(json)
  }
  
  const buildThemes = function () {
    document.querySelectorAll('.datahero-theme').forEach(function (element) {
      const id = element.getAttribute('id')
      if (!id || id === 'datahero-theme') {
        window.datahero.themes.default = JSON.parse(element.textContent)
      } else if (id.startsWith('datahero-theme-')) {
        window.datahero.themes[id.slice(15)] = JSON.parse(element.textContent)
      }
    })
  }
  
  const divWrapper = function () {
    const div = document.createElement('div')
    div.style.position = 'relative'
    div.style.width = '100%'
    div.style.height = '100%'
    return div
  }
  
  document.addEventListener('DOMContentLoaded', function () {
    /* Load in themes
    
    For every JSON <script> class name datahero-theme and id `datahero-theme-id`, load the JSON into an Object
    with keys for each id and values equal to the JSON. If there's no -id then load it as the default theme.
    */
    buildThemes()
    
    document.querySelectorAll('.datahero').forEach(function (element) {
      const id = element.getAttribute('data-datahero-id')
      if (element.children.length > 0 || element.textContent.trim().length > 0) {
        console.warn(`DataHero element with id ${id} is not empty. The chart will not be rendered.`)
        return
      }
      
      const config = createChartConfig(id, element)
      const canvas = document.createElement('canvas')
      element.appendChild(divWrapper().appendChild(canvas))
      window.datahero.charts[id] = new Chart(canvas, config)
    })
    
    // listen for any changes to the data attributes of .datahero objects
    // and update the chart options accordingly
    const observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        const id = mutation.target.getAttribute('data-datahero-id')
        const config = createChartConfig(id, mutation.target)
        if (config) {
          window.datahero.charts[id].options = config.options
          window.datahero.charts[id].update()
        }
      })
    })
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-datahero-id', 'data-datahero-theme', 'data-datahero-mode'],
      subtree: true
    })
    
    // listen for changes in the textContent in any script tags with id datahero-* and
    // reload the chart with the new data
    const dataObserver = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        const id = mutation.target.id.slice(9)
        const element = document.querySelector(`.datahero[data-datahero-id="${id}"]`)
        if (!element) {
          return
        }
        const config = createChartConfig(id, element)
        if (config) {
          window.datahero.charts[id].options = config.options
          window.datahero.charts[id].data = config.data
          window.datahero.charts[id].update()
        }
      })
    })
    
    /*
    loop through all the script tags and observe them individually
    
    Example in console of changing JSON dynamically:
    
    > const el = document.getElementById('datahero-mcHqwBSsgBLJYRJMm2qEWk')
    > const json = JSON.parse(el.textContent)
    > json.title = 'New Title'
    > el.textContent = JSON.stringify(json)
    */
    document.querySelectorAll('script[id^="datahero-"]').forEach(function (script) {
      dataObserver.observe(script, {
        childList: true
      })
    })
  })
})()

