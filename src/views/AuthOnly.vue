<template>
  <div>
    <router-view v-if="authStoreAppIsReady"/>

    <teleport to="body">
      <the-loading-profile-modal ref="loadingModal"/>
      <modal-error ref="modalError"/>
    </teleport>
  </div>
</template>


<script>
import ModalError from '@/components/modal/ModalError.vue'
import TheLoadingProfileModal from '@/components/ui/TheLoadingProfileModal.vue'
import { authStatusCodes, useAuthStore } from '@/stores/auth.js'
import { mapActions, mapState } from 'pinia'
import router from '@/router/index.js'

export default {
  name: 'AuthOnly',
  components: { TheLoadingProfileModal, ModalError },

  head: {
    title: 'DataHero'
  },

  data () {
    return {
      authStatusCodes
    }
  },
  computed: {
    ...mapState(useAuthStore, {
      authStoreStatus: 'authStatus',
      authStoreAppIsReady: 'appIsReady'
    })
  },

  watch: {
    authStoreStatus (val) {
      // This is a global check for when logged out - go to login page
      if (val === authStatusCodes.loggedOut) {
        router.replace({ name: 'login' })
      }
    }
  },

  async mounted () {
    this.$refs.loadingModal.open()
    await Promise.all([this.authStoreLoadProfile(), this.authStoreCreateDb()])

    // if we're debugging:
    if (process.env.NODE_ENV === 'development') {
      window.db = useAuthStore().db  // attach to window for console access
    }

    // Won't exist any more if we've redirected to the login page
    if (this.$refs.loadingModal) {
      this.$refs.loadingModal.close()
    }
  },

  async beforeUnmount () {
    await this.authStoreDestroyDb()
  },

  methods: {
    ...mapActions(useAuthStore, {
      authStoreLoadProfile: 'loadProfile',
      authStoreCreateDb: 'createDb',
      authStoreDestroyDb: 'destroyDb'
    })
  }

}
</script>

<style scoped>

</style>
