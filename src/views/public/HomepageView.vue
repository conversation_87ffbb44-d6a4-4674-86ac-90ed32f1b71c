<template>
  <div class="">
    <homepage-hero class=""/>
    <homepage-tables class="mt-16 lg:mt-24"/>
    <homepage-charts class="mt-16 lg:mt-24"/>
    <homepage-ai class="mt-16 lg:mt-24"/>
    <homepage-sign-up class=""/>
  </div>

  <teleport to="body">
    <the-invite-modal ref="inviteModal"/>
  </teleport>
</template>

<script>
import TheInviteModal from '@/views/public/components/TheInviteModal.vue'
import HomepageHero from '@/views/public/homepage/HomepageHero.vue'
import HomepageTables from '@/views/public/homepage/HomepageTables.vue'
import HomepageCharts from '@/views/public/homepage/HomepageCharts.vue'
import HomepageSignUp from '@/views/public/homepage/HomepageSignUp.vue'
import HomepageJobs from '@/views/public/homepage/HomepageJobs.vue'
import HomepageAi from '@/views/public/homepage/HomepageAI.vue'
import HomepageGuarantee from '@/views/public/homepage/HomepageGuarantee.vue'

export default {
  components: {
    HomepageGuarantee,
    HomepageAi,
    HomepageJobs,
    HomepageSignUp,
    HomepageTables,
    HomepageCharts,
    HomepageHero,
    TheInviteModal
  },

  head: {
    title: 'DataHero: Manage Your Data, Create Great Charts'
  },

  props: {
    signup: {
      type: Boolean,
      default: false
    }
  },
  mounted () {
    if (this.signup) {
      this.$refs.inviteModal.open()
    }
  },
  watch: {
    signup (value) {
      if (value) {
        this.$refs.inviteModal.open()
      } else {
        this.$refs.inviteModal.close()
      }
    }
  }
}

</script>
