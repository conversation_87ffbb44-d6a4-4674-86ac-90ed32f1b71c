<template>
  <div class="bg-white">
    <header class="absolute inset-x-0 top-0 z-50">
      <nav class="flex items-center justify-between p-6 lg:px-8" aria-label="Global">
        <div class="flex lg:flex-1">
          <router-link :to="{name: 'homepage' }" class="-m-1.5 p-1.5">
            <span class="sr-only">DataHero</span>
            <brand-image/>
          </router-link>
        </div>
        <div class="flex lg:hidden">
          <button
              type="button"
              class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
              @click="mobileMenuOpen = true">
            <span class="sr-only">Open main menu</span>
            <Menu class="h-6 w-6" aria-hidden="true"/>
          </button>
        </div>
        <div class="hidden lg:flex lg:gap-x-12 text-gray-900">
          <router-link
              v-for="(item, index) in navbar"
              :key="index"
              :to="item.route"
              class="text-sm font-semibold leading-6"
              exact-active-class="text-fuchsia-700">
            {{ item.name }}
          </router-link>
        </div>
        <div class="hidden lg:flex lg:flex-1 lg:justify-end">
          <router-link :to="{ name: 'app-dashboard' }" class="text-sm font-semibold leading-6 text-gray-900">Go to app
            <span aria-hidden="true">&rarr;</span></router-link>
        </div>
      </nav>
      <Dialog as="div" class="lg:hidden" @close="mobileMenuOpen = false" :open="mobileMenuOpen">
        <div class="fixed inset-0 z-50"/>
        <dialog-panel
            class="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
          <div class="flex items-center justify-between">
            <a href="/" class="-m-1.5 p-1.5">
              <span class="sr-only">DataHero</span>
              <brand-image/>
            </a>
            <button type="button" class="-m-2.5 rounded-md p-2.5 text-gray-700" @click="mobileMenuOpen = false">
              <span class="sr-only">Close menu</span>
              <X class="h-6 w-6" aria-hidden="true"/>
            </button>
          </div>
          <div class="mt-6 flow-root">
            <div class="-my-6 divide-y divide-gray-500/10">
              <div class="space-y-2 py-6">
                <router-link
                    v-for="(item, index) in navbar"
                    :key="index"
                    :to="item.route"
                    class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50">
                  {{ item.name }}
                </router-link>
              </div>
              <div class="py-6">
                <router-link :to="{ name: 'app-dashboard' }" class="text-sm font-semibold leading-6 text-gray-900">
                  Go to app
                </router-link>
              </div>
            </div>
          </div>
        </dialog-panel>
      </Dialog>
    </header>
    <router-view/>
    <footer class="bg-gray-900 text-gray-100">
      <div class="mx-auto max-w-7xl overflow-hidden px-6 py-20 sm:py-24 lg:px-8">
        <nav class="-mb-6 columns-2 sm:flex sm:justify-center sm:space-x-12" aria-label="Footer">
          <div v-for="item in navbar" :key="item.name" class="pb-6">
            <router-link :to="item.route" class="text-sm leading-6 text-gray-100 hover:text-gray-300">
              {{ item.name }}
            </router-link>
          </div>
        </nav>
        <div class="mt-10 flex justify-center space-x-10">
        </div>
        <p class="mt-10 text-center text-sm leading-5 text-gray-300">&copy; 2024 Chalkstream Development, Inc. All
          rights reserved.</p>
      </div>
    </footer>
  </div>
</template>


<script>
import BrandImage from '@/components/ui/BrandImage.vue'
import { Dialog, DialogPanel } from '@headlessui/vue'
import { Menu, X } from 'lucide-vue-next'

export default {
  components: { Dialog, DialogPanel, BrandImage, X, Menu },
  head: {
    title: 'DataHero'
  },
  data () {
    return {
      mobileMenuOpen: false,
      navbar: [
        { name: 'About', route: { name: 'homepage' } },
        { name: 'Blog', route: { name: 'blog' } },
        { name: 'Sign Up', route: { name: 'sign-up' } },
        { name: 'Contact Us', route: { name: 'contact-us' } }
      ]
    }
  }
}
</script>

<style>
</style>
