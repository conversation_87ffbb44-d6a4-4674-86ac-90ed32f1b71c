<template>
  <main class="bg-zinc-200 py-16 sm:py-24" v-if="post">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-4xl">

        <article class="mt-12">
          <!-- Header Section -->
          <header class="text-center">
            <div class="block text-sm text-zinc-500 sm:text-base mb-12">
              <time v-if="post.date_published" :datetime="post.date_published">{{
                  formatDate(post.date_published)
                }}
              </time>
              <time v-else>Unpublished</time>
              &bull;
              <span v-if="post.author">by {{ post.author.name }}, {{ post.author.position }}</span>
            </div>

            <h1
                v-html="post.title"
                class="text-4xl sm:text-7xl font-bold tracking-tight mb-8 bg-linear-to-r from-zinc-900 to-fuchsia-700 inline-block text-transparent bg-clip-text"/>
            <p
                v-html="post.subtitle"
                class="text-xl text-zinc-600 sm:text-2xl mb-8"/>


            <div v-if="post.tags.length" class="flex justify-center space-x-3 mb-8">
              <span class="px-3 py-1 inline-flex text-zinc-500">Filed under</span>
              <span
                  v-for="tag in post.tags"
                  :key="tag"
                  class="inline-flex items-center rounded-full bg-zinc-500 px-6 py-1 text-sm font-medium text-white"
              >{{ tag }}</span>
            </div>

          </header>
        </article>
      </div>
    </div>


    <!-- Image Section -->
    <div v-if="post.image" class="my-5 md:my-12">
      <div class="overflow-hidden bg-zinc-200 flex items-center justify-center max-h-[780px]">
        <img
            :src="image_root + post.image"
            alt="Blog Post Image"
            loading="lazy"
            class="w-full object-cover aspect-video max-h-[780px]"
        />
      </div>
    </div>


    <!-- text section from components -->
    <component
        v-for="(component, index) in components"
        :key="index"
        :is="component.component"
        v-bind="component.props"
    />


    <div>
      <div class="mx-auto max-w-7xl py-24 sm:px-6 sm:py-32 lg:px-8">
        <div
            class="relative isolate overflow-hidden bg-zinc-900 px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16">
          <h2 class="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">DataHero helps you
            manage, organize and chart your data.</h2>
          <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-zinc-300">Learn about a new way of organizing your
            personal and business data in one central location.</p>
          <div class="mt-10 flex items-center justify-center gap-x-6">
            <router-link
                :to="{ name: 'sign-up' }"
                class="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-zinc-900 shadow-xs hover:bg-zinc-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white">
              Get started
            </router-link>
            <router-link :to="{ name: 'homepage' }" class="text-sm font-semibold leading-6 text-white">Learn
              more <span aria-hidden="true">→</span></router-link>
          </div>
          <svg viewBox="0 0 1024 1024"
               class="absolute left-1/2 top-1/2 -z-10 h-[64rem] w-[64rem] -translate-x-1/2 [mask-image:radial-gradient(closest-side,white,transparent)]"
               aria-hidden="true">
            <circle cx="512" cy="512" r="512" fill="url(#827591b1-ce8c-4110-b064-7cb85a0b1217)" fill-opacity="0.7"/>
            <defs>
              <radialGradient id="827591b1-ce8c-4110-b064-7cb85a0b1217">
                <stop stop-color="#7775D6"/>
                <stop offset="1" stop-color="#E935C1"/>
              </radialGradient>
            </defs>
          </svg>
        </div>
      </div>
    </div>

  </main>


</template>


<script>
import { format, formatDistanceToNow } from 'date-fns'
import { api } from '@/utils/api/api.js'
import BlogText from '@/views/public/blog/BlogText.vue'
import BlogImage from '@/views/public/blog/BlogImage.vue'
import BlogIntro from '@/views/public/blog/BlogIntro.vue'
import { useAuthStore } from '@/stores/auth.js'

const api_root = import.meta.env.VITE_API_URL

const getPost = async (slug, preview) => {
  if (preview) {
    const authStore = useAuthStore()
    const headers = await authStore.getAuthorization()
    return api.get(`blog/preview/${slug}`, { headers })
  } else {
    return api.get(`blog/posts/${slug}`)
  }
}

export default {

  props: {
    slug: {
      type: String,
      required: true
    }
  },

  head () {
    if (this.post === null) return
    return {
      title: `${this.post.title}${this.preview ? ' (Preview)' : ''} - The DataHero Blog`
    }
  },

  data () {
    return {
      post: null,
      image_root: import.meta.env.VITE_IMAGE_ROOT
    }
  },

  async beforeRouteEnter (to, from, next) {
    await getPost(to.params.slug, to.meta.preview).then(response => {
      next(vm => {
        vm.post = response.data
      })
    }).catch(() => {
      next({ name: 'blog' })
    })
  },

  async beforeRouteUpdate (to) {
    this.post = null
    await getPost(to.params.slug, to.meta.preview).then(response => {
      this.post = response.data
    }).catch(() => {
      this.$router.push({ name: 'blog' })
    })
  },

  computed: {
    preview () {
      return this.$route.meta.preview
    },

    components () {

      // split up the blog post into sections before, inside, and after the regex
      const sections = this.post.body?.split(/(<p>{.*?:.*?}<\/p>)/g) || []

      // convert sections into components
      return sections.map((section, index) => {
        // get out the parts with in our special tag
        const re = /<p>{(.*?):(.*?)}<\/p>/g
        const submatch = re.exec(section)
        if (submatch) {
          const [_, keyword, props] = submatch
          if (keyword === 'image') {
            const [index, alt] = props.split(':')
            const src = this.post.images[index - 1]
            return {
              component: BlogImage,
              props: { src, alt }
            }
          }

        } else {
          return {
            component: this.post.image && index === 0 ? BlogIntro : BlogText,
            props: { text: section }
          }
        }
      }).filter(component => component)  // filter out non matches

    }
  },

  methods: {
    formatDate (dt) {
      let rel = formatDistanceToNow(dt)
      rel = rel.charAt(0).toUpperCase() + rel.slice(1)
      dt = format(dt, 'MMMM do, yyyy')
      return `${dt} (${rel} ago)`
    }
  }
}
</script>


<style scoped>

</style>
