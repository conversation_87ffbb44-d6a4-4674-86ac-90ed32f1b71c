<template>

  <div class="mx-auto max-w-7xl relative z-20 bg-zinc-400 shadow-xl shadow-zinc-500">
    <img
        :src="image_root + src"
        :alt="alt"
        loading="lazy"
        class="w-full h-auto"
    />
  </div>
</template>


<script>

export default {
  name: 'BlogImage',
  data: () => {
    return {
      image_root: import.meta.env.VITE_IMAGE_ROOT
    }
  },
  props: {
    src: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      required: true
    }
  }
}
</script>


<style scoped>

</style>
