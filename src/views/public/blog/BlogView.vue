<template>
  <div class="bg-white py-24 sm:py-32">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:max-w-4xl">
        <h1 class="text-4xl tracking-tight sm:text-5xl text-zinc-900">
          <strong class="text-fuchsia-500">By the Numbers</strong>
          &mdash; the DataHero blog
        </h1>
        <p class="mt-2 text-lg leading-8 text-zinc-600">Data insights, product updates and how-tos.</p>
        <div class="mt-16 space-y-20 lg:mt-20 lg:space-y-20">
          <article v-for="post in posts" :key="post.id" class="relative isolate flex flex-col gap-8 lg:flex-row">
            <div v-if="post.image" class="relative aspect-16/9 sm:aspect-2/1 lg:aspect-square lg:w-48 lg:shrink-0">
              <img class="absolute inset-0 h-full w-full rounded-2xl bg-zinc-50 object-cover" :src="post.image" alt=""/>
              <div class="absolute inset-0 rounded-2xl ring-1 ring-inset ring-zinc-900/10"/>
            </div>
            <div class="flex-1">
              <div class="flex items-center gap-x-4 text-xs">
                <time :datetime="post.date_published" class="text-zinc-500">
                  {{ formatDate(post.date_published) }} ago
                </time>
              </div>
              <div class="group relative">
                <h3 class="mt-3 text-2xl font-normal">
                  <router-link :to="{ name: 'post', params: { slug: post.slug } }">
                    <span v-html="post.title"/>
                  </router-link>
                </h3>
                <p class="mt-5 text-zinc-600" v-html="post.subtitle"/>
              </div>
            </div>
          </article>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import { formatDistanceToNow } from 'date-fns'
import { api } from '@/utils/api/api.js'

const image_root = import.meta.env.VITE_IMAGE_ROOT

export default {
  head: {
    title: 'Behind the Numbers: The DataHero Blog'
  },
  data () {
    return {
      image_root,
      posts: []
    }
  },

  async beforeRouteEnter (to, from, next) {
    const response = await api.get('blog/posts/')
    next(vm => {
      vm.posts = response.data
    })
  },

  async beforeRouteUpdate (to, from) {
    this.posts = []
    api.get('blog/posts/').then(response => {
      this.posts = response.data
    })
  },

  methods: {
    formatDate (dt) {
      const s = formatDistanceToNow(dt)
      return s.charAt(0).toUpperCase() + s.slice(1)
    }
  }
}
</script>


<style scoped>

</style>
