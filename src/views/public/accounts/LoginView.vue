<template>
  <div
      class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8"
  >
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Sign in to DataHero
      </h2>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
        <form
            @submit.prevent="processLogin"
            class="space-y-6"
            action="#"
            method="POST"
        >
          <p
              v-if="loginFailed"
              class="mb-6 text-sm text-center font-medium text-red-600"
          >
            That email and password did not match our records. Please try again.
          </p>
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <div class="mt-1">
              <input
                  v-model="email"
                  id="email"
                  name="email"
                  type="email"
                  autocomplete="email"
                  required=""
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
              />
            </div>
          </div>

          <div>
            <label
                for="password"
                class="block text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <div class="mt-1">
              <input
                  v-model="password"
                  id="password"
                  name="password"
                  type="password"
                  autocomplete="current-password"
                  required=""
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
              />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="text-sm">
              <router-link
                  :to="{ name: 'password-reset' }"
                  class="font-medium text-fuchsia-600 hover:text-fuchsia-500 cursor-pointer"
              >
                Forgot your password?
              </router-link>
            </div>
          </div>

          <div>
            <base-btn type="primary" :loading="loading" full-width is-submit>{{
                loading ? 'Signing in...' : 'Sign in'
              }}
            </base-btn>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { authStatusCodes, useAuthStore } from '@/stores/auth.js'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import router from '@/router/index.js'
import { mapActions, mapState } from 'pinia'

export default {
  name: 'LoginView',
  components: { BaseBtn },
  data () {
    return {
      email: '',
      password: '',
      loading: false,
      loginFailed: false
    }
  },
  computed: {
    ...mapState(useAuthStore, {
      authStoreStatus: 'authStatus'
    })
  },
  methods: {
    ...mapActions(useAuthStore, {
      authStoreLogin: 'login'
    }),
    async processLogin () {
      this.loading = true
      await this.authStoreLogin(this.email, this.password)
      if (this.authStoreStatus === authStatusCodes.loggedIn) {
        await router.push({ name: 'app' })
      } else {
        this.loginFailed = true
        this.password = ''
      }
      this.loading = false
    }
  }
}
</script>

<style scoped></style>
