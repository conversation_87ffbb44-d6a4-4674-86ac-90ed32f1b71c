<template>
  <div class="min-h-screen flex items-center justify-center">
    <div class="text-center">
      <h2 class="text-3xl font-extrabold text-gray-900 mb-4">
        {{ activationStatus === 'success' ? 'Account Activated!' : 'Activation Error' }}
      </h2>
      <p v-if="activationStatus === 'success'" class="text-green-600">
        Your account has been activated successfully. You can now
        <router-link to="/login" class="text-fuchsia-600">log in!</router-link>
      </p>
      <p v-else class="text-red-600">
        We couldn't activate your account. Please try again or contact support.
      </p>
    </div>
  </div>
</template>

<script>
import { api } from '@/utils/api/api.js'

export default {
  name: 'ActivateAccountView',
  data () {
    return {
      activationStatus: null // 'success' or 'error'
    }
  },
  async created () {
    const { uid, token } = this.$route.params
    try {
      await api.post('auth/activate/', { uid, token })
      this.activationStatus = 'success'
    } catch (error) {
      this.activationStatus = 'error'
    }
  }
}
</script>

<style scoped></style>
