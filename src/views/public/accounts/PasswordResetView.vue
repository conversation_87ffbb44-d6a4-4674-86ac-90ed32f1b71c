<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {{ resetSuccess ? 'Success!' : 'Reset your password' }}
      </h2>
      <p v-if="!resetSuccess" class="mt-2 text-center text-sm text-gray-600">
        Enter your email address, and we'll send you a link to reset your password.
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
        <div v-if="!resetSuccess">
          <form @submit.prevent="processResetPassword" class="space-y-6">
            <p v-if="resetFailed" class="mb-6 text-sm text-center font-medium text-red-600">
              Unable to send reset email. Please try again later.
            </p>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div class="mt-1">
                <input
                    v-model="email"
                    id="email"
                    name="email"
                    type="email"
                    autocomplete="email"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <base-btn type="primary" :loading="loading" full-width is-submit>
                {{ loading ? 'Sending...' : 'Reset Password' }}
              </base-btn>
            </div>
          </form>
        </div>

        <div v-else>
          <p class="text-center text-gray-600 mb-6">
            If an account exists with that email, we've sent password reset instructions to your email address.
            Please check your inbox and follow the instructions to reset your password.
          </p>
          <div>
            <base-btn type="primary" @click.prevent="$router.push({ name: 'homepage' })" full-width>Return to homepage
            </base-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { api } from '@/utils/api/api.js'

export default {
  name: 'ResetPasswordView',
  components: { BaseBtn },
  data () {
    return {
      email: '',
      loading: false,
      resetSuccess: false,
      resetFailed: false
    }
  },
  methods: {
    async processResetPassword () {
      this.loading = true
      this.resetSuccess = false
      this.resetFailed = false
      api.post('auth/reset-password/', { email: this.email }).then(() => {
        this.resetSuccess = true
      }).catch(() => {
        this.resetFailed = true
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped></style>
