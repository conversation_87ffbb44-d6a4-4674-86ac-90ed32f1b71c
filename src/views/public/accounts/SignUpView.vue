<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {{ registerSuccess ? 'Welcome!' : 'Create a New Account' }}
      </h2>
      <p v-if="!registerSuccess" class="mt-2 text-center text-sm text-gray-600">
        Please fill in the details to register.
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
        <div v-if="!registerSuccess">
          <form @submit.prevent="processRegister" class="space-y-6">
            <!-- Display error messages if registration failed -->
            <div v-if="registerFailed && error" class="mb-6">
              <ul class="text-sm text-red-600">
                {{ error }}
              </ul>
            </div>

            <!-- Rest of the form fields -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
              <div class="mt-1">
                <input
                    v-model="email"
                    id="email"
                    name="email"
                    type="email"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
              <div class="mt-1">
                <input
                    v-model="password"
                    id="password"
                    name="password"
                    type="password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label for="passwordConfirm" class="block text-sm font-medium text-gray-700">
                Confirm Password
              </label>
              <div class="mt-1">
                <input
                    v-model="passwordConfirm"
                    id="passwordConfirm"
                    name="passwordConfirm"
                    type="password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label for="companyName" class="block text-sm font-medium text-gray-700">
                Company Name (Optional)
              </label>
              <div class="mt-1">
                <input
                    v-model="companyName"
                    id="companyName"
                    name="companyName"
                    type="text"
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <base-btn type="primary" :loading="loading" full-width is-submit>
                {{ loading ? 'Registering...' : 'Register' }}
              </base-btn>
            </div>
          </form>
        </div>
        <div v-else>
          <p class="text-center text-gray-600">
            To complete your registration, please check your email for a verification link.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { api } from '@/utils/api/api.js'

export default {
  name: 'RegisterView',
  components: { BaseBtn },
  data () {
    return {
      email: '',
      password: '',
      passwordConfirm: '',
      companyName: '',
      loading: false,
      registerSuccess: false,
      registerFailed: false,
      error: null
    }
  },
  methods: {
    async processRegister () {
      if (this.password !== this.passwordConfirm) {
        this.registerFailed = true
        this.error = 'Passwords do not match.'
        return
      }

      this.loading = true
      this.registerFailed = false

      await api
          .post('auth/alpha-sign-up/', {
            email: this.email,
            password: this.password,
            company_name: this.companyName
          })
          .then(() => {
            this.registerSuccess = true
            this.companyName = ''
          })
          .catch((error) => {
            this.registerFailed = true
            if (error.response && error.response.data) {
              this.error = 'Sorry, there was an error with your registration. Please check your details and try again. Make sure your password is at least 8 characters and is not a common word.'
            } else {
              this.error = 'An unexpected error occurred. Please try again.'
            }
          })
          .finally(() => {
            this.loading = false
          })
    }
  }
}
</script>

<style scoped></style>
