<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {{ resetSuccess ? 'Success!' : 'Set a new password' }}
      </h2>
      <p v-if="!resetSuccess" class="mt-2 text-center text-sm text-gray-600">
        Enter your new password below.
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
        <div v-if="!resetSuccess">
          <form @submit.prevent="processResetPasswordConfirm" class="space-y-6">
            <p v-if="resetFailed" class="mb-6 text-sm text-center font-medium text-red-600">
              Failed to reset password. Please check the information and try again. Passwords
              less than 8 characters long and common passwords will be rejected.
            </p>
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700">
                New Password
              </label>
              <div class="mt-1">
                <input
                    v-model="password"
                    id="password"
                    name="password"
                    type="password"
                    autocomplete="new-password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label for="passwordConfirm" class="block text-sm font-medium text-gray-700">
                Confirm New Password
              </label>
              <div class="mt-1">
                <input
                    v-model="passwordConfirm"
                    id="passwordConfirm"
                    name="passwordConfirm"
                    type="password"
                    autocomplete="new-password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <base-btn type="primary" :loading="loading" full-width is-submit>
                {{ loading ? 'Resetting...' : 'Set Password' }}
              </base-btn>
            </div>
          </form>
        </div>

        <div v-else>
          <p class="text-center text-gray-600 mb-6">
            Your password has been reset successfully. You can now log in with your new password.
          </p>
          <div>
            <base-btn type="primary" @click.prevent="$router.push({ name: 'login' })" full-width>
              Log In
            </base-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { api } from '@/utils/api/api.js'

export default {
  name: 'ResetPasswordConfirmView',
  components: { BaseBtn },
  data () {
    return {
      password: '',
      passwordConfirm: '',
      loading: false,
      resetSuccess: false,
      resetFailed: false
    }
  },
  computed: {
    uid () {
      return this.$route.params.uid || ''
    },
    token () {
      return this.$route.params.token || ''
    }
  },
  methods: {
    async processResetPasswordConfirm () {
      if (this.password !== this.passwordConfirm) {
        this.resetFailed = true
        return
      }

      this.loading = true
      this.resetSuccess = false
      this.resetFailed = false
      api.post('auth/reset-password-confirm/', {
        uid: this.uid,
        token: this.token,
        new_password: this.password,
        re_new_password: this.passwordConfirm
      }).then(() => {
        this.resetSuccess = true
      }).catch(() => {
        this.resetFailed = true
      })
      this.loading = false
    }
  }
}
</script>

<style scoped></style>
