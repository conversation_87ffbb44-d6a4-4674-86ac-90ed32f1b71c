<template>
  <base-modal
      ref="base"
      small
      hide-cancel
      submit-text="Join the list"
      title="Join the invite list!"
      :submit-method="signupSubmit"
      :disable-submit="turnstileToken === ''"
      @shown="() => $refs.turnstile.reset()"
      @closing="$router.push({ name: 'homepage' })">
    <p>
      Thanks for your interest in DataHero! We are still in limited private testing
      to make sure the rollout goes smoothly for users.</p>
    <p>
      If you'd like to register your interest, enter in your email below.
      <strong>No spam, guaranteed</strong> &mdash; we'll just contact you as soon as we're ready to open
      up to more users.
    </p>
    <input
        v-model="signupEmail"
        required
        type="email"
        class="mt-4 w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
        placeholder="Your email address"/>

    <vue-turnstile ref="turnstile" :site-key="turnstileKey" v-model="turnstileToken"/>
    <p v-if="signupError" class="mt-4 text-sm text-red-600">{{ signupError }}</p>
  </base-modal>

  <base-modal small ref="success" title="You're in!" hide-cancel>
    <p>{{ signupSuccess }}</p>
  </base-modal>
</template>
<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import VueTurnstile from 'vue-turnstile'
import { api } from '@/utils/api/api.js'

export default {
  name: 'the-invite-modal',
  components: { BaseModal, VueTurnstile },
  data () {
    return {
      turnstileKey: import.meta.env.VITE_TURNSTILE_KEY,
      signupEmail: '',
      signupError: '',
      signupSuccess: '',
      turnstileToken: ''
    }
  },
  methods: {
    open () {
      this.signupError = ''
      this.signupEmail = ''
      this.turnstileToken = ''
      this.$refs.base.open()
    },

    close () {
      this.$refs.base.close()
    },

    async signupSubmit () {
      this.signupEmail = this.signupEmail.trim()
      if (this.signupEmail === '') {
        this.signupError = 'Please enter your email address to join the list!'
        return false
      }

      // noinspection RegExpRedundantEscape
      const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      if (!re.test(this.signupEmail)) {
        this.signupError = 'Please enter a valid email address!'
        return false
      }

      // do an axios POST to /api/invite-list/ with the email address and proceed if we got a 201
      await api.post('invite-list/', {
        email: this.signupEmail,
        token: this.turnstileToken
      }).then(response => {
        // if status code is 202, then change message to say already signed up
        if (response.status === 202) {
          this.signupSuccess = `You're already on the list! We'll be in touch soon.`
        } else {
          this.signupSuccess = `You're in! Thanks, your email has been added to the list! We'll contact you as soon as more spaces are available.`
        }
        this.$refs.base.close()
        this.$refs.success.open()
      }).catch(() => {
        this.signupError = 'Sorry, there was an error adding your email to the list. Please check your email address and try again.'
      })

    }

  }
}
</script>
