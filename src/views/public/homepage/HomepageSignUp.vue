<template>
  <div class="bg-zinc-100">
    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:flex lg:items-center lg:justify-between lg:px-8">
      <div>
        <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Give DataHero a Spin</h2>
        <div class="text-xl mt-6">
          DataHero is currently in private testing. Join the invite list to be among the first to try it out.
        </div>
      </div>
      <div class="mt-10 flex items-center gap-x-6 lg:mt-0 lg:shrink-0">
        <router-link :to="{ name: 'sign-up' }">
          <base-btn xl type="primary">Join the List <span aria-hidden="true">→</span></base-btn>
        </router-link>
      </div>
    </div>
  </div>

</template>

<script>
import BaseBtn from '@/components/buttons/BaseBtn.vue'

export default {
  components: { BaseBtn }
}
</script>


<style scoped>

</style>
