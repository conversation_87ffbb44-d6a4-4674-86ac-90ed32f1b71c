<template>
  <div class="relative isolate pt-20">
    <div class="px-6 py-12 sm:py-16 md:px-12 xl:px-32 xl:flex xl:items-center xl:gap-x-10 bg-gray-900"
         style="background-color: #0b0130">
      <div class="mx-auto xl:mx-0 xl:flex-1 text-lg lg:text-xl leading-8 text-gray-300">
        <div class="text-2xl lg:text-3xl tracking-tight text-gray-100 mt-12">
          Numbed by numbers? Grappling with graphs?
        </div>
        <h1 class="mt-8 font-bold text text-5xl lg:text-7xl text-white">
          <strong class="text-fuchsia-600">DataHero</strong> to the
          <span class="underline decoration-fuchsia-500">rescue</span>.
        </h1>
        <div class="intro mt-12 xl:mr-20 space-y-6">
          <p class="text-xl lg:text-2xl">
            Use DataHero to manage your data and create beautiful
            charts <strong>in seconds.</strong>
          </p>
          <ul class="lg:ml-8 space-y-2 text-white">
            <li>
              <CheckCircle class="size-7 text-fuchsia-500 inline-block mr-1"/>
              No more messing around with spreadsheets.
            </li>
            <li>
              <CheckCircle class="size-7 text-fuchsia-500 inline-block mr-1"/>
              No more fussing with bad charts.
            </li>
            <li>
              <CheckCircle class="size-7 text-fuchsia-500 inline-block mr-1"/>
              Use DataHero and get on with your work.
            </li>
          </ul>
        </div>
        <div class="mt-12">
          <router-link :to="{ name: 'sign-up' }" class="mt-12 mx-auto">
            <base-btn type="primary" xl>
              <strong>Get started for free <span aria-hidden="true">→</span></strong>
            </base-btn>
          </router-link>
        </div>
      </div>
      <div class="hidden xl:block mt-16 sm:mt-24 xl:mt-0 shrink-0 w-2/5 max-w-lg">
        <img :src="heroImage" alt="DataHero tables"/>
      </div>
    </div>
  </div>
</template>
<script>

import { Check, CheckCircle } from 'lucide-vue-next'
import BaseBtn from '@/components/buttons/BaseBtn.vue'

import heroImage from '@/assets/homepage/hero.png'

export default {
  name: 'homepage-hero',
  components: {
    BaseBtn,
    Check,
    CheckCircle
  },
  data () {
    return {
      heroImage
    }
  }
}
</script>
