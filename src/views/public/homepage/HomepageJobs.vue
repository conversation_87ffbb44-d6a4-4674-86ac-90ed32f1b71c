<template>
  <section class="relative isolate overflow-hidden bg-white px-6 lg:px-8">
    <div class="text-center">
      <h2 class="text-xl mb-12 font-bold text-fuchsia-500 mt-8">Find out how DataHero helps&mldr;</h2>
      <nav class="flex text-2xl space-x-4 justify-center w-full" aria-label="Tabs">
        <a v-for="job in jobs" :key="job.name" :href="`#${job.name}`"
           :class="[selectedJob === job.name ? 'bg-fuchsia-100 text-fuchsia-700' : 'text-gray-500 hover:text-gray-700', 'rounded-md px-3 py-2']"
           :aria-current="selectedJob === job.name ? 'page' : undefined">{{ job.name }}</a>
      </nav>
    </div>

    <div
        class="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,var(--color-fuchsia-300),white)] opacity-20"/>
    <div
        class="absolute inset-y-0 right-1/2 -z-10 mr-16 w-[200%] origin-bottom-left skew-x-[-30deg] bg-white shadow-xl shadow-fuchsia-600/10 ring-1 ring-fuchsia-50 sm:mr-28 lg:mr-0 xl:mr-16 xl:origin-center"/>
    <div class="mx-auto max-w-2xl lg:max-w-4xl">
      <img class="mx-auto h-12" src="https://tailwindui.com/img/logos/workcation-logo-fuchsia-600.svg" alt=""/>
      <figure class="mt-10">
        <q class="text-center text-xl font-semibold leading-8 text-gray-900 sm:text-2xl sm:leading-9">
          {{ jobs.find(job => job.name === selectedJob).description }}
        </q>
        <figcaption class="mt-10">

        </figcaption>
      </figure>
    </div>
  </section>
</template>

<script>
import { Banknote, BookOpen, PenSquare } from 'lucide-vue-next'

export default {
  data () {
    return {
      selectedJob: 'Teachers',
      jobs: [
        {
          name: 'Teachers',
          description: `With DataHero I can keep track of student attendance and grades. Thanks to the school's
theme, I can put together visualizations in seconds and they look great when i share findings with the team.
Best of all, it gets out of my way so I can focus on teaching.`,
          icon: BookOpen
        },
        {
          name: 'Journalists',
          description: 'In our newsroom, whenever we have a data-driven story, we use DataHero to create charts and graphs. It\'s so easy to use and the results are always beautiful.',
          icon: PenSquare
        },
        {
          name: 'Small business owners',
          description: 'I use DataHero to keep track of my sales and expenses. It\'s so easy to keep up to date and I can put together quick reports for everyone on the team.',
          icon: Banknote
        }
      ]
    }
  }
}
</script>

<style scoped>

</style>
