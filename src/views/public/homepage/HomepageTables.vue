<template>
  <div class="overflow-hidden bg-white text-lg leading-8 text-gray-600">
    <div class="mx-auto max-w-7xl md:px-6 lg:px-8">
      <div class="grid grid-cols-1 gap-x-8 gap-y-16 sm:gap-y-20 lg:grid-cols-2 lg:items-start">
        <div class="px-6 lg:px-0 lg:pr-4 lg:pt-4">
          <div class="mx-auto max-w-2xl lg:mx-0 lg:max-w-lg">
            <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Tables, not spreadsheets
            </p>
            <p class="mt-6">
              Spreadsheets are great &mdash; if you're a data scientist. But for
              everyone else, you probably spend more time tidying, formatting,
              and fixing your data than you do analyzing it or understanding
              it.
            </p>
            <p class="mt-4">
              <strong class="font-bold text-fuchsia-600">That's where <span class="underline decoration-fuchsia-500">DataHero</span>
                comes in.</strong>
              It keeps data in structured tables so everything remains structured and consistent.
            </p>
            <dl class="mt-10 max-w-xl space-y-8 text-base leading-7 text-gray-600 lg:max-w-none">
              <div
                  v-for="feature in features"
                  :key="feature.name"
                  class="relative pl-9">
                <dt class="inline font-semibold text-gray-900">
                  <component
                      :is="feature.icon"
                      class="absolute left-1 top-1 h-5 w-5 text-fuchsia-600"
                      aria-hidden="true"
                  />
                  {{ feature.name }}
                </dt>
                {{ ' ' }}
                <dd class="inline">{{ feature.description }}</dd>
              </div>
            </dl>
          </div>
        </div>
        <div class="sm:px-6 lg:px-0">
          <div
              class="relative isolate overflow-hidden bg-fuchsia-500 px-6 pt-8 sm:mx-auto sm:max-w-2xl sm:rounded-3xl sm:pl-16 sm:pr-0 sm:pt-16 lg:mx-0 lg:max-w-none">
            <div
                class="absolute -inset-y-px -left-3 -z-10 w-full origin-bottom-left skew-x-[-30deg] bg-fuchsia-100 opacity-20 ring-1 ring-inset ring-white"
                aria-hidden="true"/>
            <div class="mx-auto max-w-2xl sm:mx-0 sm:max-w-none">
              <img
                  :src="tablesImage"
                  alt="DataHero tables"
                  width="1809"
                  height="1193"
                  class="-mb-12 w-[48rem] max-w-none rounded-tl-xl bg-gray-800 ring-1 ring-white/10"
              />
            </div>
            <div
                class="pointer-events-none absolute inset-0 ring-1 ring-inset ring-black/10 sm:rounded-3xl"
                aria-hidden="true"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { ArrowDownToLine, Calculator, Table2 } from 'lucide-vue-next'

import tablesImage from '@/assets/homepage/tables.png'

export default {
  data () {
    return {
      tablesImage,
      features: [
        {
          name: 'Fuss-free formats.',
          description: 'Dates, numbers, currencies, percentages and more just work as you\'d expect.',
          icon: Table2
        },
        {
          name: 'Impressive imports.',
          description: 'Paste in your data from anywhere and DataHero will work out what it is and how to use it.',
          icon: ArrowDownToLine
        },
        {
          name: 'Clear calculations.',
          description: 'No more formulas. Create calculated columns in your data in a couple of clicks.',
          icon: Calculator
        }
      ]
    }
  }
}
</script>
