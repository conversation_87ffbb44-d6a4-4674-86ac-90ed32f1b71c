<template>

  <div class="bg-gray-900 py-24 sm:py-32">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-3xl lg:text-center">
        <h2 class="text-xl font-semibold leading-7 text-white">Get Your Work Done Faster and Smarter with</h2>
        <p class="mt-4 text-4xl font-bold tracking-tight text-fuchsia-400 sm:text-4xl">
          DataHero AI
        </p>
        <p class="mt-6 text-lg leading-8 text-gray-300">
          When AI works, it feels like magic. When it doesn't, it's just gets in your way. DataHero uses large language
          models throughout the product to help you get your job done quicker and gain instant insights.
          But we keep the integration seamless so it won't get in your way, and doesn't outstay its welcome.
        </p>
      </div>
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
          <div v-for="feature in features" :key="feature.name" class="flex flex-col">
            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-white">
              <component :is="feature.icon" class="h-5 w-5 flex-none text-fuchsia-400" aria-hidden="true"/>
              {{ feature.name }}
            </dt>
            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-300">
              <ul class="flex-auto">
                <li v-for="point in feature.points" :key="point" class="flex items-start gap-x-2">
                  <span class="flex-none text-fuchsia-400">•</span>
                  <span>{{ point }}</span>
                </li>
              </ul>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</template>

<script>
import { Clock, Cloud, Lightbulb } from 'lucide-vue-next'

export default {
  components: {
    Clock,
    Cloud,
    Lightbulb
  },
  data () {
    return {
      features: [
        {
          name: 'Importing Ease',
          points: [
            'Extract tables of data from paragraphs of text',
            'Find text and tables from PDFs, Word documents, screenshots and other rich media'
          ],
          icon: Cloud
        },
        {
          name: 'Cut Down on Keystrokes',
          points: [
            'Get smart recommendations for table and chart titles, axis, and descriptions',
            'Smart fill data on entry to cut down on keystrokes'
          ],
          icon: Clock
        },
        {
          name: 'AI Insights',
          points: [
            'Get recommendations for charts and calculations',
            'Summarize trends in natural language'
          ],
          icon: Lightbulb
        }
      ]
    }
  }
}
</script>


<style scoped>

</style>
