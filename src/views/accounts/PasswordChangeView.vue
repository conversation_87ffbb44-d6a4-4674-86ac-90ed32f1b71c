<template>
  <div
      class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8"
  >
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {{ changeSuccess ? 'Success!' : 'Change Password' }}
      </h2>
      <p v-if="!changeSuccess" class="mt-2 text-center text-sm text-gray-600">
        Enter your current password and a new password.
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow-sm sm:rounded-lg sm:px-10">
        <div v-if="!changeSuccess">
          <form @submit.prevent="processChangePassword" class="space-y-6">
            <p
                v-if="changeFailed"
                class="mb-6 text-sm text-center font-medium text-red-600"
            >
              Failed to change password. Please check your current password matches and that the new password is at
              least 8 characters long.
              Common passwords will also be rejected.
            </p>

            <div>
              <label for="currentPassword" class="block text-sm font-medium text-gray-700">
                Current Password
              </label>
              <div class="mt-1">
                <input
                    v-model="currentPassword"
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    autocomplete="current-password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label for="newPassword" class="block text-sm font-medium text-gray-700">
                New Password
              </label>
              <div class="mt-1">
                <input
                    v-model="newPassword"
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    autocomplete="new-password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label for="reNewPassword" class="block text-sm font-medium text-gray-700">
                Confirm New Password
              </label>
              <div class="mt-1">
                <input
                    v-model="reNewPassword"
                    id="reNewPassword"
                    name="reNewPassword"
                    type="password"
                    autocomplete="new-password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <base-btn type="primary" :loading="loading" full-width is-submit>
                {{ loading ? 'Changing...' : 'Change Password' }}
              </base-btn>
            </div>
          </form>
        </div>

        <div v-else>
          <p class="text-center text-sm mb-6 text-gray-600">
            Your password has been changed successfully.
          </p>

          <div>
            <base-btn type="primary" @click.prevent="$router.push({ name: 'app' })" full-width>
              Return to the App
            </base-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { useAuthStore } from '@/stores/auth.js'
import { api } from '@/utils/api/api.js'

export default {
  name: 'ChangePasswordView',
  components: { BaseBtn },
  data () {
    return {
      currentPassword: '',
      newPassword: '',
      reNewPassword: '',
      loading: false,
      changeSuccess: false,
      changeFailed: false
    }
  },
  methods: {
    async processChangePassword () {
      if (this.newPassword !== this.reNewPassword) {
        this.changeFailed = true
        return
      }
      this.loading = true
      this.changeSuccess = false
      this.changeFailed = false
      const data = {
        current_password: this.currentPassword,
        new_password: this.newPassword,
        re_new_password: this.reNewPassword
      }
      const headers = await useAuthStore().getAuthorization()
      api.post('auth/set-password/', data, headers).then(() => {
        this.changeSuccess = true
      }).catch(() => {
        this.changeFailed = true
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped></style>
