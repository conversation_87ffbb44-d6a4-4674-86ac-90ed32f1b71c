<template>
  <div class="bg-zinc-100 min-h-screen w-full overflow-y-auto">
    <div class="py-6 px-6">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center space-x-4">
          <h1 class="text-2xl font-bold text-zinc-800">Welcome to <strong class="text-fuchsia-700">DataHero</strong>
          </h1>
          <div
              class="flex items-center bg-white px-3 py-1.5 rounded-md shadow-xs border border-zinc-200 relative cursor-pointer workspace-selector"
              @click="showWorkspaceMenu">
            <span class="text-sm font-medium text-zinc-500 mr-2">Workspace:</span>
            <span v-if="currentWorkspace" class="flex items-center text-sm font-semibold text-zinc-800">
              <Building2 class="h-4 w-4 text-fuchsia-600 mr-1.5"/>
              {{ currentWorkspace.name }}
            </span>
            <span v-else class="flex items-center text-sm font-semibold text-zinc-800">
              <User class="h-4 w-4 text-fuchsia-600 mr-1.5"/>
              Personal
            </span>
            <ChevronDown class="h-4 w-4 text-zinc-400 ml-2"/>
          </div>

          <!-- Context Menu for Workspace Selection -->
          <context-menu ref="workspaceMenu" :width="240">
            <menu-item-group title="Select Workspace">
              <!-- Personal Workspace Option -->
              <menu-item-button
                  @click="switchWorkspace(null)"
                  :selected="isPersonalWorkspace">
                <div class="flex items-center">
                  <User class="h-4 w-4 text-fuchsia-600 mr-2"/>
                  <span>Personal</span>
                </div>
              </menu-item-button>

              <!-- Organization Options -->
              <menu-item-button
                  v-for="org in organizations"
                  :key="org.id"
                  @click="switchWorkspace(org.id)"
                  :selected="workspaceId === org.id">
                <div class="flex items-center">
                  <Building2 class="h-4 w-4 text-fuchsia-600 mr-2"/>
                  <span>{{ org.name }}</span>
                </div>
              </menu-item-button>
            </menu-item-group>
          </context-menu>
        </div>
        <button
            @click="$emit('add-project')"
            class="cursor-pointer bg-fuchsia-600 hover:bg-fuchsia-700 text-white px-4 py-2 rounded-md flex items-center shadow-xs">
          <Plus class="h-5 w-5 mr-2"/>
          New Project
        </button>
      </div>

      <!-- Empty state -->
      <div v-if="!projectsStoreProjects.length && !projectsStoreOrphanTables.length"
           class="bg-white rounded-lg p-8 text-center shadow-md border-2 border-zinc-200">
        <FilePlus class="h-12 w-12 mx-auto text-fuchsia-500 mb-4"/>
        <h2 class="text-xl font-medium text-zinc-900 mb-2">Get started with DataHero</h2>
        <p class="text-zinc-600 mb-6 max-w-lg mx-auto">Create your first project or import data to start analyzing and
          visualizing your information.</p>
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <button
              @click="$emit('add-project')"
              class="cursor-pointer bg-fuchsia-600 hover:bg-fuchsia-700 text-white px-4 py-2 rounded-md flex items-center justify-center shadow-xs">
            <FolderPlus class="h-5 w-5 mr-2"/>
            Create a Project
          </button>
          <button
              @click="$emit('import')"
              class="cursor-pointer bg-white border border-zinc-300 hover:bg-zinc-50 text-zinc-700 px-4 py-2 rounded-md flex items-center justify-center shadow-xs">
            <HardDriveUpload class="h-5 w-5 mr-2"/>
            Import Data
          </button>
        </div>
      </div>

      <!-- Projects list -->
      <div v-else class="space-y-6">
        <!-- Projects section -->
        <div v-if="projectsStoreProjects.length">
          <div class="flex items-center mb-3">
            <Folder class="h-5 w-5 text-fuchsia-600 mr-2"/>
            <h2 class="text-lg font-bold text-zinc-800">Projects</h2>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="project in projectsStoreProjects" :key="project.id"
                 class="bg-white rounded-lg shadow-md border-l-4 border-t border-r border-b border-zinc-200">
              <div class="flex justify-between items-center p-3 border-b border-zinc-300 bg-zinc-50">
                <div class="flex items-center">
                  <h3 class="font-bold text-zinc-800">{{ project.title }}</h3>
                  <span class="ml-2 text-xs font-medium px-2 py-0.5 bg-zinc-200 text-zinc-700 rounded-full">
                    {{ project.tables.length }} {{ pluralize('table', project.tables.length) }}
                  </span>
                </div>
                <div class="flex space-x-1">
                  <button
                      @click="$emit('add-table', project)"
                      class="cursor-pointer text-zinc-600 hover:bg-zinc-200 p-1.5 rounded-md"
                      title="Add table">
                    <Grid2x2Plus class="h-4 w-4"/>
                  </button>
                  <button
                      @click="$emit('import', project)"
                      class="cursor-pointer text-zinc-600 hover:bg-zinc-200 p-1.5 rounded-md"
                      title="Import data">
                    <HardDriveUpload class="h-4 w-4"/>
                  </button>
                  <button
                      @click="$emit('edit-project', project)"
                      class="cursor-pointer text-zinc-600 hover:bg-zinc-200 p-1.5 rounded-md"
                      title="Edit project">
                    <Pencil class="h-4 w-4"/>
                  </button>
                </div>
              </div>

              <div v-if="project.tables.length">
                <div class="grid grid-cols-1 gap-0 divide-y divide-zinc-100">
                  <div
                      v-for="table in project.tables.slice(0, showAllTables[project.id] ? undefined : 5)"
                      :key="table.id"
                      class="p-2.5 hover:bg-zinc-50 cursor-pointer group"
                      @click="navigateToTable(table.id)">
                    <div class="flex justify-between items-center">
                      <div class="flex items-center">
                        <Table2 class="h-4 w-4 text-zinc-500 mr-2"/>
                        <span class="text-sm font-medium text-zinc-700">{{ table.title }}</span>
                      </div>
                      <ChevronRight class="h-4 w-4 text-zinc-400 opacity-0 group-hover:opacity-100"/>
                    </div>
                  </div>
                </div>
                <div v-if="project.tables.length > 5" class="p-2 border-t border-zinc-100">
                  <button
                      @click="toggleShowAllTables(project.id)"
                      class="cursor-pointer w-full text-xs text-zinc-500 hover:text-zinc-700 flex items-center justify-center">
                    <span v-if="showAllTables[project.id]">
                      <ChevronUp class="h-3.5 w-3.5 mr-1"/>
                    </span>
                    <span v-else>
                      <ChevronDown class="h-3.5 w-3.5 mr-1"/>
                    </span>
                  </button>
                </div>
              </div>
              <div v-else class="p-4 text-center text-zinc-500 text-sm italic">
                No tables in this project yet
              </div>
            </div>
          </div>
        </div>

        <!-- Orphan tables section -->
        <div v-if="projectsStoreOrphanTables.length">
          <div class="flex items-center mb-3">
            <Table2 class="h-5 w-5 text-zinc-600 mr-2"/>
            <h2 class="text-lg font-bold text-zinc-800">Standalone Tables</h2>
          </div>

          <div class="bg-white rounded-lg shadow-md border border-zinc-200">
            <div class="grid grid-cols-1 gap-0 divide-y divide-zinc-100">
              <div
                  v-for="table in projectsStoreOrphanTables.slice(0, showAllOrphanTables ? undefined : 10)"
                  :key="table.id"
                  class="p-3 hover:bg-zinc-50 cursor-pointer group"
                  @click="navigateToTable(table.id)">
                <div class="flex justify-between items-center">
                  <div class="flex items-center">
                    <Table2 class="h-4 w-4 text-zinc-500 mr-2"/>
                    <span class="text-sm font-medium text-zinc-700">{{ table.title }}</span>
                  </div>
                  <ChevronRight class="h-4 w-4 text-zinc-400 opacity-0 group-hover:opacity-100"/>
                </div>
              </div>
            </div>
            <div v-if="projectsStoreOrphanTables.length > 10" class="p-2 border-t border-zinc-100">
              <button
                  @click="toggleShowAllOrphanTables"
                  class="cursor-pointer w-full text-xs text-zinc-500 hover:text-zinc-700 flex items-center justify-center">
                <span v-if="showAllOrphanTables">
                  <ChevronUp class="h-3.5 w-3.5 mr-1"/>
                </span>
                <span v-else><ChevronDown class="h-3.5 w-3.5 mr-1"/></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'pinia'
import { useProjectsStore } from '@/stores/projects'
import { usePrefsStore } from '@/stores/prefs'
import { pluralize } from '@/utils/helpers.js'
import {
  Building2,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  FilePlus,
  Folder,
  FolderPlus,
  Grid2x2Plus,
  HardDriveUpload,
  Pencil,
  Plus,
  Table2,
  User
} from 'lucide-vue-next'
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import { useAuthStore } from '@/stores/auth.js'

export default {
  name: 'AppDashboard',
  components: {
    Building2,
    ChevronDown,
    ChevronUp,
    ChevronRight,
    ContextMenu,
    Table2,
    Folder,
    FolderPlus,
    FilePlus,
    MenuItemButton,
    MenuItemGroup,
    Pencil,
    Plus,
    Grid2x2Plus,
    HardDriveUpload,
    User
  },
  emits: ['add-project', 'edit-project', 'add-table', 'import'],
  data () {
    return {
      showAllTables: {},
      showAllOrphanTables: false
    }
  },
  computed: {
    ...mapState(useProjectsStore, {
      projectsStoreProjects: 'projects',
      projectsStoreOrphanTables: 'orphanTables'
    }),
    ...mapState(usePrefsStore, {
      workspaceId: 'workspaceId'
    }),
    ...mapState(useAuthStore, {
      authStoreOrganizations: 'organizations'
    }),
    organizations () {
      return this.authStoreOrganizations || []
    },
    currentWorkspace () {
      if (!this.workspaceId) return null
      return this.organizations.find(org => org.id === this.workspaceId)
    },
    isPersonalWorkspace () {
      return this.workspaceId === null
    }
  },
  methods: {
    pluralize,
    navigateToTable (tableId) {
      this.$router.push({
        name: 'app-table',
        params: { tableId }
      })
    },
    toggleShowAllTables (projectId) {
      this.showAllTables[projectId] = !this.showAllTables[projectId]
      this.$forceUpdate()
    },
    toggleShowAllOrphanTables () {
      this.showAllOrphanTables = !this.showAllOrphanTables
    },
    showWorkspaceMenu (event) {
      this.$refs.workspaceMenu.showMenu(event)
    },
    switchWorkspace (orgId) {
      usePrefsStore().workspaceId = orgId
      this.$refs.workspaceMenu.closeMenu()
      useProjectsStore().reload()
    }
  }
}
</script>
