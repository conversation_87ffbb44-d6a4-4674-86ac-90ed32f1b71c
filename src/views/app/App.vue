<template>
  <div class="select-none">
    <div id="app">
      <div class="h-screen flex overflow-hidden bg-white">
        <the-site-sidebar
            @file-drop="$refs.modalImportData.open(undefined, $event)"
            @add-table="$refs.modalAddNewTable.open($event)"
            @edit-table="$refs.modalEditTable.open($event)"
            @add-project="$refs.modalAddEditProject.open(null)"
            @edit-project="$refs.modalAddEditProject.open($event)"
            @import="$refs.modalImportData.open($event)"
        />
        <router-view
            @add-table="$refs.modalAddNewTable.open($event)"
            @edit-table="$refs.modalEditTable.open($event)"
            @add-project="$refs.modalAddEditProject.open(null)"
            @edit-project="$refs.modalAddEditProject.open($event)"
            @import="$refs.modalImportData.open($event)"
        />
      </div>

      <teleport to="body">
        <modal-import-data ref="modalImportData"/>
        <modal-add-new-table ref="modalAddNewTable"/>
        <modal-edit-table ref="modalEditTable" @delete-table="handleDeleteTable"/>
        <modal-add-edit-project ref="modalAddEditProject"/>
        <toast-notifications/>
      </teleport>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'pinia'
import { useProjectsStore } from '@/stores/projects.js'
import TheSiteSidebar from '@/components/ui/sidebar/TheSiteSidebar.vue'
import ModalImportData from '@/components/modal/ModalImportData.vue'
import TheLoadingProfileModal from '@/components/ui/TheLoadingProfileModal.vue'
import ModalError from '@/components/modal/ModalError.vue'
import { useTableStore } from '@/stores/table.js'
import ModalEditTable from '@/components/modal/ModalEditTable.vue'
import ToastNotifications from '@/components/ui/ToastNotifications.vue'
import ModalAddNewTable from '@/components/modal/ModalAddNewTable.vue'
import ModalAddEditProject from '@/components/modal/ModalAddEditProject.vue'

export default {
  components: {
    ModalAddEditProject,
    ModalAddNewTable,
    ToastNotifications,
    ModalEditTable,
    ModalError,
    TheLoadingProfileModal,
    ModalImportData,
    TheSiteSidebar
  },

  head: {
    title: 'DataHero App'
  },

  async beforeRouteLeave () {
    await useTableStore().flushQueue()
  },

  created () {
    window.addEventListener('beforeunload', this.beforeWindowUnload)
    window.addEventListener('visibilitychange', this.visibilityChange)
  },

  beforeUnmount () {
    window.removeEventListener('beforeunload', this.beforeWindowUnload)
    window.removeEventListener('visibilitychange', this.visibilityChange)
  },

  computed: {
    appHasQueue () {
      return useTableStore().hasQueue
    }
  },

  async mounted () {
    await this.reloadProjects()
  },

  methods: {
    ...mapActions(useProjectsStore, {
      reloadProjects: 'reload',
      projectsStoreDeleteTable: 'deleteTable'
    }),

    beforeWindowUnload (e) {
      const tableStore = useTableStore()
      if (tableStore.hasQueue) {
        if (!window.confirm('Do you really want to leave? You have unsaved changes, and you will lose them if you shut now.')) {
          e.preventDefault()
          e.returnValue = ''
        }
      }
    },

    visibilityChange () {
      if (document.visibilityState === 'hidden') {
        useTableStore().flushQueue()
      }
    },

    async handleDeleteTable () {
      await this.reloadProjects()
      await this.$router.push({ name: 'app-dashboard' })
    }
  }
}
</script>

<style>
</style>
