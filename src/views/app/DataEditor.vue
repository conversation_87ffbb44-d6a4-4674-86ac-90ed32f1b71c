<template>
  <div class="flex flex-col w-0 flex-1 overflow-hidden" v-bind="$attrs">
    <template v-if="loadingTable">
      <div class="flex flex-col items-center justify-center h-full">
        <p class="text-lg text-zinc-600 mb-4">Loading table...</p>
        <div class="block">
          <loading-spinner class="w-6 h-6"/>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="mt-1 z-10">
        <table-tabs
            :key="tableStoreId"
            :view-id="viewId"
            @edit-table="$emit('edit-table', projectsStoreFindTable(tableStoreId))"
            @refresh-view="$refs.tableEditor.setGridColumns()"
        />
      </div>
      <div class="z-10  border-t ">
        <table-actions
            :chart-pane-open="chartViewerStatus !== 'closed'"
            :table-id="tableId"
            :view-id="viewId"
            @formula-editor="$refs.formulaEditor.open()"
            @refresh-columns="$refs.tableEditor.setGridColumns()"
            @close-chart-pane="closeViewer"
            @set-first-row-to-header="setFirstRowToHeader"
            @export-table="$refs.exportTableModal.open(viewId)"
        />
      </div>
      <div class="flex-1 overflow-y-hidden flex flex-col">
        <main class="flex-1 overflow-y-auto flex focus:outline-hidden">
          <table-editor
              v-if="tableStoreId"
              :key="viewId || tableStoreId"
              :view-id="viewId"
              :viewing-chart="mode === 'add' || mode === 'edit' || Boolean(chartId)"
              ref="tableEditor"
              @save-chart="saveChart"
          ></table-editor>
        </main>
        <div
            v-if="chartViewerStatus !== 'closed'" class="splitter shrink-0 h-1">
          <splitter-horizontal @move="splitterMove" @up="splitterUp"></splitter-horizontal>
        </div>
        <the-status-bar-closed v-if="chartViewerStatus === 'closed'"/>
        <div :style="bottomPanelStyle" ref="bottomPanel">
          <transition-fixed-slide v-model="chartViewerStatus" slide-from="bottom" class="h-full">
            <div v-if="chartViewerStatus !== 'closed'"
                 class="flex-1 flex flex-col overflow-hidden focus:outline-hidden bg-white h-full">
              <the-status-bar @close="closeViewer"/>
              <the-chart-panel
                  :add-mode="mode === 'add'"
                  :show-left-side="chartEditorStatus === 'closed' || chartEditorStatus === 'closing'"
                  @chart-context-menu="$refs.editDialogs.openContextMenu($event)"
                  @box-click="chartBoxClick"
                  @cell-click="jumpToCell"
              />
            </div>
          </transition-fixed-slide>
        </div>
      </div>
    </template>

    <teleport to="body">
      <transition-fixed-slide
          v-model="chartEditorStatus"
          slide-from="right"
          class="fixed inset-y-0 right-0 w-112 border-l-2 border-gray-300"
      >
        <the-chart-editor
            v-if="tableStoreId && editStep !== undefined"
            :step="editStep"
            :view-id="viewId"
            @save-chart="saveChart"
            @cancel="routeToTable"
            @close="routeToChart"
        />
      </transition-fixed-slide>

      <modal-export-table ref="exportTableModal"/>
      <modal-export-chart
          ref="modalExportChart"
          :format="chartExportFormat"
          @closed="routeToChart"
      />
      <modal-formula-editor
          ref="formulaEditor"
          :columns="tableStoreColumns"
      />

      <template v-if="chartViewerStatus !== 'closed'">
        <chart-edit-dialogs ref="editDialogs"/>
      </template>


    </teleport>
  </div>
  <div
      v-if="chartEditorStatus === 'opened'"
      class="w-112 shrink-0"
  ></div>
</template>

<script>

import { mapActions, mapState, mapWritableState } from 'pinia'
import { capitalize } from '@/utils/helpers.js'

import { useTableStore } from '@/stores/table.js'
import { useChartStore } from '@/stores/chart.js'

import { usePrefsStore } from '@/stores/prefs.js'
import TransitionFixedSlide from '@/components/transitions/TransitionFixedSlide.vue'
import TheChartEditor from '@/components/chart/TheChartEditor.vue'
import ModalExportTable from '@/components/modal/ModalExportTable.vue'
import TheStatusBar from '@/components/ui/statusbar/TheStatusBar.vue'
import TableActions from '@/components/table/actionbar/TableActions.vue'
import SplitterHorizontal from '@/components/ui/SplitterHorizontal.vue'
import TableEditor from '@/components/table/TableEditor.vue'
import TheChartPanel from '@/components/chart/TheChartPanel.vue'
import TheStatusBarClosed from '@/components/ui/statusbar/TheStatusBarClosed.vue'
import BaseDialog from '@/components/modal/BaseDialog.vue'
import ChartEditDialogs from '@/components/chart/ChartEditDialogs.vue'
import ModalExportChart from '@/components/modal/ModalExportChart.vue'
import { useProjectsStore } from '@/stores/projects.js'
import TableTabs from '@/components/table/TableTabs.vue'
import ModalAddEditProject from '@/components/modal/ModalAddEditProject.vue'
import ModalEditTable from '@/components/modal/ModalEditTable.vue'
import ModalFormulaEditor from '@/components/modal/ModalFormulaEditor.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import { filterQuerySpec } from '@/db/query/utils.js'

export default {
  name: 'DataEditor',
  emits: ['edit-table'],
  components: {
    ModalFormulaEditor,
    LoadingSpinner,
    ModalEditTable,
    ModalAddEditProject,
    TableTabs,
    ModalExportChart,
    ChartEditDialogs,
    BaseDialog,
    TheStatusBar,
    TheStatusBarClosed,
    TheChartPanel,
    TransitionFixedSlide,
    ModalExportTable,
    TableActions,
    TheChartEditor,
    SplitterHorizontal,
    TableEditor
  },

  data () {
    return {
      loadingTable: false,
      chartEditorStatus: 'closed',
      chartViewerStatus: 'closed',
      chartSplitMover: undefined
    }
  },

  props: {
    // These come in from the route props
    mode: {
      type: String,
      required: false
    },
    tableId: {
      type: String,
      required: true
    },
    viewId: {
      type: String,
      required: false
    },
    chartId: {
      type: String,
      required: false
    },
    editStep: {
      type: Number,
      required: false
    },
    chartExportFormat: {
      type: String,
      required: false
    }
  },

  computed: {
    ...mapState(useChartStore, {
      chartStoreId: 'id',
      chartStoreData: 'data'
    }),
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreLabelColumnId: 'labelColumnId',
      tableStoreDbTable: 'dbTable',
      tableStoreColumns: 'columns'
    }),
    ...mapWritableState(usePrefsStore, {
      prefsStoreChartSplit: 'chartSplit'
    }),
    bottomPanelStyle () {
      return {
        height: this.chartViewerStatus === 'closed'
                ? '0px'
                : (this.chartSplitMover === undefined
                   ? (this.prefsStoreChartSplit * 100) + '%'
                   : this.chartSplitMover + 'px'
                )
      }
    }
  },

  watch: {
    $route: {
      handler: 'loadFromRoute',
      immediate: true
    }
  },

  methods: {
    ...mapActions(useProjectsStore, {
      projectsStoreFindTable: 'findTable'
    }),
    ...mapActions(useTableStore, {
      tableStoreSaveColumns: 'saveColumns',
      tableStoreReloadCharts: 'reloadCharts',
      tableStoreSetColumnHeader: 'setColumnHeader',
      tableStoreSetFirstRowToHeader: 'setFirstRowToHeader',
      tableStoreSetLabelColumnId: 'setLabelColumnId',
      tableStoreLoad: 'load'
    }),
    ...mapActions(useChartStore, {
      chartStoreSave: 'save',
      chartStoreReset: '$reset',
      chartStoreLoadFromTable: 'loadFromTable'
    }),

    closePane (viewer) {
      const attr = `chart${capitalize(viewer)}Status`
      if (this[attr] === 'opened' || this[attr] === 'opening') {
        this[attr] = 'closing'
      }
    },

    openPane (viewer) {
      const attr = `chart${capitalize(viewer)}Status`
      if (this[attr] === 'closed' || this[attr] === 'closing') {
        this[attr] = 'opening'
      }
    },

    closeViewer () {
      this.routeToTable()
      this.closePane('viewer')
    },

    openViewer () {
      this.routeToTable('chart')
      this.openPane('viewer')
    },

    togglePane (viewer, open) {
      if (open) {
        this.openPane(viewer)
      } else {
        this.closePane(viewer)
      }
    },

    async loadFromRoute () {
      // If the table ID has changed, load it from the store
      if (this.tableId !== this.tableStoreId) {
        this.loadingTable = true
        await this.tableStoreLoad(this.tableId)
        this.loadingTable = false
      }

      // If chart ID has changed, load it from the store; if nothing, shut it.
      if (this.chartId !== this.chartStoreId) {
        if (this.chartId) {
          this.chartStoreLoadFromTable(this.chartId)
        } else {
          this.chartStoreReset()
        }
      }

      // If requested by route, open the bottom pane; but keep it open
      if (this.chartId || this.mode) {
        this.openPane('viewer')
      }

      // Use the route as the source of truth for the editor on teh side
      this.togglePane('editor', this.mode === 'edit')

      if (this.chartId && this.mode === 'export') {
        await this.$refs.modalExportChart.open()
      }

    },

    routeToTable (mode) {
      // update the router to be the given table ID (or the one in table store)
      // mode can optionally be either add (to add new chart) or charts (to show chart list)
      this.$router.push({
        name: `app-${this.viewId ? 'view' : 'table'}${mode ? '-' + mode : ''}`,
        params: {
          tableId: this.tableStoreId,
          viewId: this.viewId
        }
      })
    },

    routeToChart ({ chartId, tableId, mode } = {}) {
      // mode can optionally be `edit` (to show chart editor) or `export` (show export modal)
      this.$router.push({
        name: `app-${this.viewId ? 'view' : 'table'}-chart${mode ? '-' + mode : ''}`,
        params: {
          chartId: chartId || this.chartStoreId,
          tableId: tableId || this.tableStoreId
        }
      })
    },

    async setFirstRowToHeader () {
      const querySpec = useTableStore().getQuerySpec(this.viewId)
      const topRowSpec = filterQuerySpec(querySpec, { output: { limit: 1 } })

      // Get top row id and a repr array of values in that row
      const colIds = this.tableStoreColumns.map(col => col.id)
      const [rowId, firstRow] = await Promise.all([
        this.tableStoreDbTable.rowIds({}, topRowSpec).then(ids => ids[0]),
        this.tableStoreDbTable.rowArray(colIds, topRowSpec, true).then(rows => rows[0])
      ])

      // Set the column headers to the values in the first row
      colIds.forEach((colId, index) => {
        this.tableStoreColumns.find(c => c.id === colId).header = firstRow[index] ?? ''
      })
      this.tableStoreSaveColumns()

      await Promise.all([
        this.$refs.tableEditor.setGridColumns(),
        this.tableStoreDbTable.deleteRows([rowId]),
        this.$refs.tableEditor.deleteRows([rowId])
      ])
    },

    splitterMove (val) {
      if (this.chartSplitMover === undefined) {
        this.chartSplitMover = this.$refs.bottomPanel.clientHeight
      }
      this.chartSplitMover -= val
    },

    splitterUp () {
      // Get the height of refs bottom panel as a proportion of the size of its parent's total height
      let bottomPanelHeight = Math.max(this.$refs.bottomPanel.clientHeight, 300)
      const parentHeight = this.$refs.bottomPanel.parentElement.clientHeight
      this.prefsStoreChartSplit = bottomPanelHeight / parentHeight
      this.chartSplitMover = undefined

    },

    async saveChart () {
      /*
      Save the chart already created in the chartStore, and navigate to it.

      This assumes that chartStore holds the data for a new chart - this handles the save, reloading the table
      and updating the route to make it visible. But it deals with updating the tableStore and routing to it.
      */
      if (this.chartStoreId) {
        await this.chartStoreSave()
        await this.tableStoreReloadCharts()

      } else {
        const newId = await this.chartStoreSave()
        await this.tableStoreReloadCharts()
        this.routeToChart({ chartId: newId })
      }
    },

    chartBoxClick (box) {
      // Box can be one of: title, subtitle, legend, labelAxis, labelAxis2, dataAxis
      if (box && box !== 'legend') {
        this.$refs.editDialogs.openDialog(box)
      } else {
        // If the box is a legend, open the general editor - but only if we have a chartId. Otherwise, just ignore.
        if (this.chartId) {
          this.routeToChart({ mode: 'edit' })
        }
      }
    },

    jumpToCell ({ column, row }) {
      this.$refs.tableEditor.jumpToCell(column, row)
    }
  }
}
</script>
