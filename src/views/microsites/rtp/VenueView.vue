<template>
  <div v-if="data">
    <div class="mb-4 py-2 px-4 bg-zinc-200 rounded-b flex">
      <h2 class="flex-1">
        <span class="text-base font-bold">{{ data.venue.name }}</span>
        <span class="hidden sm:inline text-sm text-zinc-700 ml-2" v-if="shortAddress">{{ shortAddress }}</span>
      </h2>
      <div class="flex-none shrink-0 text-right text-sm">
        <button @click="$refs.changeSeasonModal.open()" class="text-pink-500 hover:text-pink-700 cursor-pointer">
          Season {{ data.season }}, Year {{ data.year }}
        </button>
      </div>
    </div>

    <div v-if="rows.length === 0">
      <div class="p-4 bg-zinc-100">
        <p class="font-bold">There's no data available at this venue for this season.</p>
        <p class="mt-4">Either the venue didn't run during this season, the data is
          not yet available, or the venue is set to private. You can try:</p>

        <ul class="list-inside list-disc mt-4">
          <li>
            <button @click="$refs.changeSeasonModal.open()" class="text-pink-500 hover:text-pink-700">Picking another
              season for this venue,
            </button>
          </li>
          <li>
            <router-link :to="{ name: 'rtp-venue-home', params: { venue: venueId }}"
                         class="text-pink-500 hover:text-pink-700"
            >Viewing the latest season,
            </router-link>
            or
          </li>
          <li>
            <router-link :to="{ name: 'rtp-home' }" class="text-pink-500 hover:text-pink-700">Returning to the
              homepage
            </router-link>
            to pick another venue.
          </li>
        </ul>

      </div>
    </div>

    <template v-else>
      <div>
        <div class="border-b border-zinc-200">
          <nav class="-mb-px flex space-x-2 sm:space-x-8 mx-2 sm:px-4 pb-2 overflow-x-auto"
               style="scrollbar-width: thin"
               aria-label="Tabs">
            <base-tab
                v-for="tab in tabs"
                :key="tab.id"
                :id="tab.id"
                :label="tab.name"
                :selected-id="tabId"
                @click.prevent="changeTab(tab.id)"
            />
          </nav>
        </div>
      </div>
      <div v-if="chartConfig" class="h-[40vh] min-h-72">
        <data-hero-chart
            type="line"
            no-events
            :data="chartConfig.data"
            :options="chartConfig.options"/>
      </div>

      <template v-if="tabId === 'players'">
        <template v-if="playerId">
          <nav class="flex-none flex space-x-2 overflow-x-auto p-2" style="scrollbar-width: thin" aria-label="Tabs">
            <base-tab
                v-for="player in data.players"
                :key="player.id"
                :id="player.id"
                :label="player.name"
                :selected-id="playerId"
                @click="$router.push({ name: 'rtp-venue-players', params: { venue, playerId: player.id }})"
            />
          </nav>
          <div class="flex-none shrink-0 bg-white mt-2">
            <dl class="grid gap-0.5 overflow-hidden rounded-sm text-center grid-cols-3 sm:grid-cols-6">
              <div v-for="stat in playerStats" :key="stat.id" class="flex flex-col bg-zinc-400/5 p-2 sm:p-4">
                <dt class="text-xs leading-6 text-zinc-600">{{ stat.name }}</dt>
                <dd class="text-sm sm:text-lg font-semibold tracking-tight text-zinc-900">{{ stat.value }}</dd>
              </div>
            </dl>
          </div>
          <div class="h-[30vh] min-h-32 sm:min-h-48">
            <data-hero-chart
                no-events
                type="mixed"
                :data="playerConfig.data"
                :options="playerConfig.options"/>
          </div>
          <div class="flex-1 h-full">
            <data-hero-grid
                :rows="playerRows"
                :columns="playerColumns"
                :show-row-position-column="false"
                @cell-mouse-down="({rowIndex}) => this.$router.push({ name: 'rtp-venue-results', params: { venue, week: rowIndex + 1 }})"
            />

          </div>
        </template>
        <template v-else>
          <div class="p-4 bg-zinc-100">
            <ul role="list" class="mt-3 grid grid-cols-1 gap-5 sm:gap-6">
              <li v-for="(player, index) in data.players" :key="player.id" class="col-span-1 flex rounded-md shadow-xs">
                <div
                    class="flex-none flex w-16 shrink-0 items-center justify-center rounded-l-md font-light text-xl text-white"
                    :class="{
                  'bg-green-600': player.league_pos === 'winner',
                  'bg-blue-500': player.league_pos === 'qualify',
                  'bg-yellow-500': player.league_pos === 'playoff',
                  'bg-zinc-400': player.league_pos === 'below'
                }">{{ index + 1 }}
                </div>
                <div
                    class="flex flex-1 items-center justify-between truncate rounded-r-md border-b border-r border-t border-gray-200 bg-white">
                  <a
                      @click.prevent="$router.push({ name: 'rtp-venue-players', params: { venue, playerId: player.id }})"
                      class="flex-1 px-4 py-2 after:absolute after:insert-0 font-medium text-lg text-gray-900 hover:text-gray-600 cursor-pointer">
                    <div class="truncate text-md">{{ player.name }}</div>
                    <p class="text-gray-500 text-sm">{{ player.points }} points over {{ player.games }} weeks</p>
                  </a>
                </div>

              </li>
            </ul>
          </div>
        </template>
      </template>

      <template v-else-if="tabId === 'results'">
        <nav class="flex-none flex space-x-2 overflow-x-auto p-2" style="scrollbar-width: thin" aria-label="Tabs">
          <base-tab
              v-for="(week, index) in weekTabs"
              :key="index"
              :id="index"
              :label="week"
              :selected-id="weekIndex"
              @click="$router.push({ name: 'rtp-venue-results', params: { venue, week: index + 1 }})"
          />
        </nav>
        <div class="flex-1 h-full">
          <data-hero-grid
              :rows="weekRows"
              :columns="weekColumns"
              :selectedRowIds="selectedRowIds"
              @cell-mouse-down="({rowIndex}) => this.$router.push({ name: 'rtp-venue-players', params: { venue, playerId: weekRows[rowIndex].id }})"
              ref="grid"/>
        </div>
      </template>
      <div v-else class="flex-1 h-full">
        <data-hero-grid
            :rows="tableRows"
            :columns="tableColumns"
            :show-row-position-column="false"
            :row-selection="true"
            :toggle-rows="true"
            v-model:selectedRowIds="selectedRowIds"
            ref="grid"/>
      </div>
    </template>

    <teleport to="body">
      <base-modal
          small
          ref="changeSeasonModal"
          title="Select a season"
          @opened="scrollToSeason"
          @submit="$router.push({ name: 'rtp-venue', params: { venue: `${venueId}-${selectedSeason}`, tabId }})">
        <div class="flex flex-col space-y-4 max-h-96 sm:max-h-128">
          <fieldset aria-label="Privacy setting" class="overflow-y-auto">
            <radio-group v-model="selectedSeason" class="-space-y-px rounded-md bg-white">
              <radio-group-option
                  as="template"
                  v-for="(season, value) in seasons" :key="value"
                  :value="value"
                  :aria-label="season"
                  :id="`season-${value}`"
                  v-slot="{ active, checked }">
                <div
                    :class="[value === 0 ? 'rounded-tl-md rounded-tr-md' : '', value === seasons.length - 1 ? 'rounded-bl-md rounded-br-md' : '', checked ? 'z-10 border-pink-200 bg-pink-50' : 'border-gray-200', 'relative flex cursor-pointer border p-4 focus:outline-hidden']">
                  <span
                      :class="[checked ? 'border-transparent bg-pink-600' : 'border-gray-300 bg-white', active ? 'ring-2 ring-pink-600 ring-offset-2' : '', 'mt-0.5 flex h-4 w-4 shrink-0 cursor-pointer items-center justify-center rounded-full border']"
                      aria-hidden="true">
                    <span class="h-1.5 w-1.5 rounded-full bg-white"/>
                  </span>
                  <span class="ml-3 flex flex-col">
                    <span :class="[checked ? 'text-pink-900' : 'text-gray-900', 'block text-sm font-medium']">{{
                        season
                      }}</span>
                  </span>
                </div>
              </radio-group-option>
            </radio-group>
          </fieldset>
        </div>
      </base-modal>
    </teleport>
  </div>
</template>

<script>

import DataHeroGrid from '@/components/datahero/DataHeroGrid.vue'
import DataHeroChart from '@/components/datahero/DataHeroChart.vue'
import { getChartConfig } from '@/charts/chartConfig.js'
import BaseTab from '@/views/microsites/rtp/BaseTab.vue'
import BaseModal from '@/components/modal/BaseModal.vue'
import { RadioGroup, RadioGroupOption } from '@headlessui/vue'

const api_url = import.meta.env.VITE_MICROSITES_API_URL

const rtp_start_year = 2007

const theme = {
  dataColors: {
    borders: ['rgb(219 39 119)', '#36a2eb', '#ff9f40', '#ffcd56', '#4bc0c0', '#9966ff', '#c9cbcf']
  },
  padding: {
    base: 0,  // legendTitle scaleTicks
    layout: { left: 0, right: 0, top: 20, bottom: 20 }
  }
}

export default {
  name: 'VenueView',
  components: { RadioGroupOption, RadioGroup, BaseModal, BaseTab, DataHeroChart, DataHeroGrid },

  head () {
    if (!this.data) {
      return {
        title: 'Loading...'
      }
    }
    if (this.tabId === 'players' && this.playerId) {
      const player = this.data.players.find(player => player.id === this.playerId)
      return {
        title: `${player.name} - ${this.data.venue.name}`
      }
    } else if (this.tabId === 'results') {
      return {
        title: `Week ${this.week} results - ${this.data.venue.name}`
      }
    }
    const tab = this.tabs.find(tab => tab.id === this.tabId)
    return {
      title: `${tab.title} - ${this.data.venue.name}`
    }
  },

  props: {
    venue: {
      type: String,
      required: true
    },
    tabId: {
      type: String,
      required: false,
      default: 'points'
    },
    week: {
      type: Number,
      required: false,
      default: 0
    },
    playerId: {
      type: Number,
      required: false,
      default: undefined
    }
  },
  data () {
    return {
      data: undefined,
      rows: undefined,
      selectedRowIds: undefined,
      selectedSeason: undefined,
      venueId: undefined,

      tabs: [
        {
          name: 'League',
          title: 'League Standings',
          id: 'points'
        },
        {
          name: 'Total Points',
          title: 'Total Points',
          id: 'total'
        },
        {
          name: 'Avg Points',
          title: 'Average Points',
          id: 'avg',
          sort: (a, b) => b.c3 - a.c3
        },
        {
          name: 'Results',
          title: 'Week by Week Results',
          id: 'results'
        },
        {
          name: 'Players',
          title: 'Player Stats',
          id: 'players'
        }
      ],

      tableColumns: [
        {
          id: 0,
          header: 'Player',
          type: 'text',
          flex: 1,
          field: 'c0'
        },
        {
          id: 1,
          header: '#',
          type: 'number',
          field: 'c1',
          width: 60
        },
        {
          id: 2,
          header: 'Pnts',
          type: 'number',
          field: 'c2',
          width: 80
        },
        {
          id: 3,
          header: 'Avg',
          type: 'number',
          field: 'c3',
          props: {
            decimals: 0
          },
          width: 80
        }
      ]
    }
  },

  watch: {
    venue: 'fetchVenue'
  },

  computed: {

    weekIndex () {
      if (this.week > 0 && this.week < this.totalWeeks) {
        return this.week - 1
      } else {
        return this.totalWeeks - 1
      }
    },

    shortAddress () {
      // split data.venue.address into \n. Return the last two lines, comma-separated
      const address = this.data.venue.address
      const lines = address.split('\n')
      return lines.slice(-2).join(', ')
    },

    totalWeeks () {
      // Get the latest index in any player in this.data.results which isn't a 0
      const results = this.data.results
      const lastWeek = Object.values(results).reduce((acc, playerResults) => {
        const last = playerResults.reduce((acc, result, i) => {
          return result === 0 ? acc : i
        }, 0)
        return Math.max(acc, last)
      }, 0)
      return lastWeek + 1
    },

    weekTabs () {
      return Array.from({ length: this.totalWeeks }, (_, i) => `Week ${i + 1}`)
    },

    tableRows () {
      if (!this.rows) return []
      const tab = this.tabs.find(tab => tab.id === this.tabId)
      if (tab.sort) {
        return this.rows.slice().sort(tab.sort)
      }
      return this.rows.slice()
    },

    chartConfig () {
      if (!this.tableRows) return
      let chartProps

      const playerIds = (this.selectedRowIds.length)
                        ? this.selectedRowIds.map(index => this.tableRows[index].id)
                        : this.data.players.slice(0, 5).map(player => player.id)

      const players = this.data.players.filter(player => playerIds.includes(player.id))

      if (this.tabId === 'total') {
        chartProps = this.chartDataPoints({ playerIds, players })

      } else if (this.tabId === 'avg') {
        chartProps = this.chartDataAvg({ playerIds, players })

      } else if (this.tabId === 'points') {
        chartProps = this.chartDataCumulative({ playerIds, players })
      }

      if (chartProps) {
        chartProps.theme = {
          theme
        }
        return getChartConfig(chartProps)
      } else {
        return null
      }
    },

    playerConfig () {
      return getChartConfig({
        datasets: [{
          chartType: 'bar',
          series: [{
            id: 2,
            color: 0
          }],
          axis: {
            show: true
          }
        }, {
          chartType: 'line',
          series: [{
            id: 3,
            color: 1
          }],
          axis: {
            show: true
          }
        }],
        rows: this.playerRows,
        cols: [
          { id: 0, header: 'Week', type: 'text' },
          { id: 2, header: 'Points', type: 'number' },
          { id: 3, header: 'Cumulative', type: 'number' }
        ],
        labelAxis: {
          'show': true,
          'column': 0
        },
        theme: { theme }
      })
    },

    playerRows () {
      const player = this.data.players.find(player => player.id === this.playerId)
      const results = this.data.results[player.id]
      return results.filter((result, i) => i < this.totalWeeks).map((result, i) => {
        return {
          id: i,
          c0: `Wk ${i + 1}`,
          c1: this.finishPosition(result),
          c2: result,
          c3: results.slice(0, i + 1).reduce((acc, result) => acc + result, 0)
        }
      })
    },

    playerStats () {
      const player = this.data.players.find(player => player.id === this.playerId)

      const playerPos = this.rows.findIndex(row => row.id === player.id) + 1
      const wins = this.data.results[player.id].filter(result => result === 100).length
      const finalTables = this.data.results[player.id].filter(result => result >= 20).length

      return [
        {
          id: 1,
          name: 'League Position',
          value: `${playerPos}`
        },
        {
          id: 2,
          name: 'Total Points',
          value: player.points
        },
        {
          id: 3,
          name: 'Average Points',
          value: Math.round(player.points / player.games)
        },
        {
          id: 3,
          name: 'Total Games',
          value: player.games
        },

        {
          id: 4,
          name: 'Wins',
          value: `${wins} (${(wins / player.games * 100).toFixed(0)}%)`
        },
        {
          id: 5,
          name: 'Final Tables',
          value: `${finalTables} (${(finalTables / player.games * 100).toFixed(0)}%)`
        }

      ]
    },

    playerColumns () {
      return [
        { id: 0, header: 'Week', type: 'text', flex: 1, field: 'c0' },
        { id: 1, header: 'Pos.', type: 'text', width: 80, field: 'c1' },
        { id: 2, header: 'Points', type: 'number', width: 80, field: 'c2' },
        { id: 3, header: 'Cumul.', type: 'number', width: 90, field: 'c3' }
      ]
    },

    weekRows () {
      const players = this.data.players.map(player => {
        const points = this.data.results[player.id][this.weekIndex]
        if (points > 0) {
          return {
            id: player.id,
            c0: player.name,
            c1: this.finishPosition(points),
            c2: points
          }
        }
      }).filter(Boolean)

      // sort by points, then be player name
      let sortedPlayers = players.sort((a, b) => {
        if (a.c2 === b.c2) {
          return a.c0.localeCompare(b.c0)
        }
        return b.c2 - a.c2
      })

      // go thru sorted and if we're missing anyone in positions 1st to 8th, insert into the array an unknown player
      const positions = sortedPlayers.map(player => player.c1)
      const missingPositions = ['1st', '2nd', '3rd', '4th', '5th', '6th', '7th', '8th'].filter(pos => !positions.includes(pos))
      missingPositions.forEach(pos => {
        sortedPlayers.splice(parseInt(pos) - 1, 0, {
          id: 0,
          c0: '(Unknown)',
          c1: pos,
          c2: '-'
        })
      })
      return sortedPlayers
    },

    weekColumns () {
      return [
        { id: 0, header: 'Player', type: 'text', flex: 1, field: 'c0' },
        { id: 1, header: 'Pos', type: 'text', width: 80, field: 'c1' },
        { id: 2, header: 'Points', type: 'number', width: 80, field: 'c2' }
      ]
    },

    years () {
      return Array.from({ length: this.data.latest.year - rtp_start_year + 1 }, (_, i) => rtp_start_year + i)
    },

    seasons () {
      const seasons = {}
      for (let year = rtp_start_year; year <= this.data.latest.year; year++) {
        const lastSeason = (year === this.data.latest.year) ? this.data.latest.season : 4
        for (let season = 1; season <= lastSeason; season++) {
          seasons[`${year}-${season}`] = `${year} Season ${season}`
        }
      }
      return seasons
    }
  },

  mounted () {
    const tab = this.tabs.find(tab => tab.id === this.tabId)
    if (!tab) {
      this.$router.push({ name: 'rtp-venue-home', params: { venue: this.venue } })
    }
    this.fetchVenue()
  },

  methods: {
    scrollToSeason () {
      document.querySelector(`#season-${this.selectedSeason}`).scrollIntoView()
    },

    changeTab (tabId) {
      this.$router.push({ name: 'rtp-venue', params: { venue: this.venue, tabId } })
    },

    async fetchVenue () {
      this.selectedRowIds = []

      // Do we have full season info in this.venue?
      const match = this.venue.match(/^(\d+)-(\d{4})-(\d)$/)
      let uri
      this.selectedSeason = undefined
      if (match) {
        this.venueId = parseInt(match[1])
        const year = parseInt(match[2])
        const season = parseInt(match[3])
        this.selectedSeason = `${year}-${season}`
        uri = `${api_url}rtp/venue/${this.venueId}/${year - rtp_start_year + 1}/${season}/`

      } else {
        this.venueId = this.venue
        uri = `${api_url}rtp/venue/${this.venueId}/`
      }

      const response = await fetch(uri)

      // If the response is a 404 then the season isn't valid and we need to redirect
      if (response.status === 404) {
        this.$router.push({ name: 'rtp-venue-home', params: { venue: this.venueId } })
        return
      }
      this.data = await response.json()

      this.rows = this.data.players.map(player => {
        return {
          id: player.id,
          c0: player.name,
          c1: player.games,
          c2: player.points,
          c3: Math.round(player.points / player.games),
          c4: player.short_name
        }
      })
      if (!this.selectedSeason) {
        this.selectedSeason = `${this.data.latest.year}-${this.data.latest.season}`
      }
    },

    finishPosition (points) {
      return points === 100 ? '1st' : points === 80 ? '2nd' : points === 70 ? '3rd' : points >= 20 && points <= 60 && points % 10 === 0 ? `${10 - points / 10}th` : points === 10 ? '9+' : 'DNS'
    },

    chartDataPoints ({ playerIds }) {
      const rows = this.tableRows.filter(row => playerIds.includes(row.id))
      return {
        datasets: [{
          chartType: 'bar',
          series: [{
            id: 2,
            color: 0
          }]
        }],
        rows: rows,
        cols: [{
          id: 4,
          header: 'Player',
          type: 'text'
        }, {
          id: 2,
          header: 'Total Points',
          type: 'number'
        }],
        labelAxis: {
          'show': true,
          'column': 4
        }
      }
    },

    chartDataAvg ({ playerIds }) {
      // created a sorted copy of this.tableRows
      const rows = this.tableRows.filter(row => playerIds.includes(row.id))

      return {
        datasets: [{
          chartType: 'bar',
          series: [{
            id: 3,
            color: 0
          }]
        }],
        rows: rows,
        cols: [{
          id: 4,
          header: 'Player',
          type: 'text'
        }, {
          id: 3,
          header: 'Avg Points',
          type: 'number'
        }],
        labelAxis: {
          'show': true,
          'column': 4
        }
      }
    },

    chartDataCumulative ({ players }) {
      return {
        byRow: true,
        datasets: [{
          chartType: 'line',
          series: players.map((player, index) => {
            return {
              id: player.id,
              color: index
            }
          }),
          values: Array.from({ length: this.totalWeeks + 1 }, (_, i) => i + 1)
        }],
        rows: players.map(player => {
          const results = this.data.results[player.id]
          const data = {
            id: player.id,
            c0: player.short_name,
            c1: 0
          }
          let cum = 0
          results.forEach((result, i) => {
            cum += result
            data[`c${i + 2}`] = cum
          })
          return data
        }),
        cols: [
          { id: 0, header: 'Player', type: 'text' },
          { id: 1, header: 'Start', type: 'number' },
          ...Array.from({ length: this.totalWeeks }, (_, i) => ({
            id: i + 2,
            header: `Wk ${i + 1}`,
            type: 'number'
          }))
        ],
        labelAxis: {
          'show': true,
          'column': 0
        }
      }
    }
  }
}
</script>


<style scoped>

</style>
