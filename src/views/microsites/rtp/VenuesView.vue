<template>
  <div>
    <div class="flex-none my-4 flex flex-col sm:flex-row items-center sm:space-x-4 space-y-2 sm:space-y-0">
      <div class="w-full md:w-auto md:flex-none p-2 flex">
        <button
            @click="getUserLocation"
            class="flex-none flex items-center px-3 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 focus:outline-hidden mr-2 cursor-pointer">
          <MapPin class="w-5 h-5"/>
        </button>
        <input
            type="text"
            v-model="searchQuery"
            placeholder="Search venues..."
            class="flex-1 w-full md:w-64 px-4 py-2 border rounded-md"
        />
      </div>

      <div class="w-full p-2 md:flex-1 flex items-center space-x-2">
        <div class="flex-1 flex items-center">
          <input
              type="checkbox"
              id="showInactiveAndPrivate"
              v-model="showInactiveAndPrivate"
              class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded-sm"
          />
          <label for="showInactiveAndPrivate" class="text-sm ml-2">
            Show Inactive/Private
          </label>
        </div>
        <div class="flex-1 text-sm text-right">
          Showing {{ filteredVenues.length }} of {{ venues.length }} venues
        </div>

      </div>

    </div>
    <tab-group
        :selected-index="tabIndex"
        @change="$router.push({name: 'rtp-venues', params: { tab: tabs[$event] }})">
      <tab-list class="flex-none flex space-x-1 rounded-t bg-pink-500 p-1">
        <Tab
            v-for="tab in tabs"
            :key="tab"
            class="w-full py-2.5 text-sm leading-5 font-medium rounded-sm focus:outline-hidden cursor-pointer"
        >
          <template v-slot="{ selected }">
            <span
                :class="[
                selected
                  ? 'bg-white shadow-sm'
                  : 'text-pink-200 hover:bg-pink-700 hover:text-white',
                'px-4 py-2 rounded-sm capitalize',
              ]"
            >
              {{ tab }}
            </span>
          </template>
        </Tab>
      </tab-list>

      <tab-panels class="flex-1 h-full overflow-auto">
        <!-- Map View -->
        <tab-panel class="h-full w-full">
          <l-map
              :zoom="zoom"
              :center="center"
              class="h-full w-full"
              @update:zoom="onZoomEnd"
              @update:center="onMoveEnd"
          >
            <l-tile-layer :url="url" :attribution="attribution"></l-tile-layer>
            <l-marker
                v-for="venue in filteredVenues"
                :key="venue.id"
                :lat-lng="[venue.lat, venue.lon]"
                @click="onMarkerClick(venue.id, $event.target)"
            >
              <l-popup class="w-64" :class="[ openPopupId === venue.id ? '' : 'min-h-96' ]">
                <div v-if="openPopupId === venue.id" class="space-y-4">
                  <h2 class="text-lg font-bold">
                    <router-link :to="{ name: 'rtp-venue-home', params: { venue: venue.id } }">{{
                        venue.name
                      }}
                    </router-link>
                  </h2>
                  <p v-if="venue.night">Game night: <strong>{{ weekDayToText(venue.night) }}</strong></p>

                  <div v-if="venue.image">
                    <router-link :to="{ name: 'rtp-venue-home', params: { venue: venue.id } }">
                      <img
                          :src="getVenueImageUrl(venue.image)"
                          :alt="`${venue.name} Image`"
                          class="w-full h-auto max-h-60 object-cover rounded-sm"
                      />
                    </router-link>
                  </div>
                  <ul class="space-y-2">
                    <li v-if="formatAddress(venue.address)" class="flex items-start">
                      <MapPin class="size-5 text-pink-500 mr-2 flex-none shrink-0"/>
                      <span>{{ formatAddress(venue.address) }}</span>
                    </li>
                    <li v-if="venue.postcode" class="flex items-start">
                      <Mail class="size-5 text-pink-500 mr-2"/>
                      <span>{{ venue.postcode }}</span>
                    </li>
                    <li v-if="venue.phone" class="flex items-start">
                      <Phone class="size-5 text-pink-500 mr-2"/>
                      <span>{{ venue.phone }}</span>
                    </li>
                  </ul>


                  <p>
                    <btn-group>
                      <router-link :to="{ name: 'rtp-venue-home', params: { venue: venue.id } }">
                        <button
                            type="button"
                            class="rounded-md bg-pink-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-pink-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-600">
                          View Results &raquo;
                        </button>
                      </router-link>

                      <button
                          type="button"
                          class="ml-2 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                        <a :href="`https://www.redtoothpoker.com/league/${venue.id}`" target="_blank"
                           rel="noopener noreferrer">Official page</a>
                      </button>
                    </btn-group>
                  </p>
                </div>
              </l-popup>
            </l-marker>
          </l-map>
        </tab-panel>

        <tab-panel class="h-full">
          <data-hero-grid
              @cell-mouse-down="$router.push({name: 'rtp-venue-home', params: { venue: this.tableRows[$event.rowIndex].id }})"
              :rows="tableRows"
              :columns="columns"
              :show-row-position-column="false"
          />
        </tab-panel>

        <tab-panel class="h-full prose p-4">
          <h2>About Toothsum</h2>
          <!-- write a boilerplate intro about the site. Include a disclosure that this site is not affiliated with Red Tooth Poker and all data is copyright of them. say made with love by datahero -->
          <p>Toothsum is a fan-made site that provides player and venue stats for Red Tooth Poker, a popular pub poker
            tour in the UK.</p>
          <p>It is not affiliated with <a href="https://www.redtoothpoker.com" target="_blank">Red Tooth Poker</a> and
            all data is copyright of them.</p>
          <p>Made with love by <a href="/">DataHero</a>, a data visualization and analytics platform.</p>
        </tab-panel>
      </tab-panels>
    </tab-group>
  </div>
</template>

<script>
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/vue'
import { LMap, LMarker, LPopup, LTileLayer } from '@vue-leaflet/vue-leaflet'
import 'leaflet/dist/leaflet.css'
import L from 'leaflet'
import DataHeroGrid from '@/components/datahero/DataHeroGrid.vue'

import { Mail, MapPin, Phone } from 'lucide-vue-next'
import BtnGroup from '@/components/buttons/BtnGroup.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'

const api_url = import.meta.env.VITE_MICROSITES_API_URL

// Fix Leaflet's default icon paths
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: new URL(
      'leaflet/dist/images/marker-icon-2x.png',
      import.meta.url
  ).href,
  iconUrl: new URL('leaflet/dist/images/marker-icon.png', import.meta.url).href,
  shadowUrl: new URL('leaflet/dist/images/marker-shadow.png', import.meta.url)
      .href
})

export default {
  name: 'VenuesView',
  components: {
    BaseBtn,
    BtnGroup,
    TabGroup,
    DataHeroGrid,
    TabPanel,
    TabPanels,
    TabList,
    LMap,
    LTileLayer,
    LMarker,
    LPopup,
    Tab,
    MapPin,
    Mail,
    Phone
  },
  props: {
    tab: {
      type: String
    }
  },
  head: {
    title: 'Toothsum - Red Tooth Poker Stats'
  },
  data () {
    return {
      venues: [],
      searchQuery: '',
      showInactiveAndPrivate: true,
      tabs: ['map', 'details', 'about'],
      openPopupId: null,
      zoom: 6,
      center: [54.5, -2.5], // Center over the UK
      url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      attribution:
          '&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors',
      columns: [
        {
          id: 0,
          header: 'Venue Name',
          flex: 1,
          type: 'text',
          field: 'name'
        },
        {
          id: 1,
          header: 'Night',
          width: 150,
          type: 'text',
          field: 'night'
        },
        {
          id: 2,
          header: 'Postcode',
          width: 150,
          type: 'text',
          field: 'postcode'
        }
      ]
    }
  },
  computed: {
    filteredVenues () {
      return this.venues.filter((venue) => {
        const matchesSearch =
            venue.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
            venue.address.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
            venue.postcode.toLowerCase().includes(this.searchQuery.toLowerCase())

        const matchesToggle =
            this.showInactiveAndPrivate || (venue.active && !venue.private)
        return matchesSearch && matchesToggle
      })
    },
    tabIndex () {
      return this.tabs.indexOf(this.tab) || 0
    },

    tableRows () {
      return this.filteredVenues.map((venue) => ({
        id: venue.id,
        name: venue.name,
        night: this.weekDayToText(venue.night),
        postcode: venue.postcode
      }))
    }
  },
  mounted () {
    this.fetchVenues()
  },
  methods: {
    onMarkerClick (id) {
      this.openPopupId = id
      return false
    },
    onPopupClose () {
      this.openPopupId = null
    },

    weekDayToText (weekday) {
      // Convert 0 = Monday, 6 = Sunday
      const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
      return days[weekday]
    },

    async fetchVenues () {
      try {
        const response = await fetch(api_url + 'rtp/venues') // Replace with your actual API endpoint
        this.venues = await response.json()
      } catch (error) {
        console.error('Error fetching venues:', error)
      }
    },
    formatAddress (address) {
      // Replace \n with <br/> for line breaks
      return address.replace(/\n/g, ', ')
    },
    getVenueImageUrl (imageName) {
      return `https://www.redtoothpoker.com/images/venues/${imageName}`
    },
    getUserLocation () {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
              const { latitude, longitude } = position.coords
              this.center = [latitude, longitude]
              this.zoom = 11
            },
            (error) => {
              console.error('Error getting location:', error)
            },
            {
              enableHighAccuracy: true,
              timeout: 5000,
              maximumAge: 0
            }
        )
      } else {
        console.warn('Geolocation is not supported by this browser.')
      }
    },
    onZoomEnd (zoom) {
      this.zoom = zoom
    },
    onMoveEnd (center) {
      this.center = center
    }
  }
}
</script>

<style scoped>

@reference "tailwindcss"

.leaflet-container a {
  @apply text-pink-600;
}


/* general typography */
.leaflet-container {
  @apply font-sans;
  @apply text-sm;
}

</style>
