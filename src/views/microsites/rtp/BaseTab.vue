<template>
  <a
      ref="anchor"
      :class="[selectedId === id
      ? 'border-pink-500 text-pink-600 font-bold'
      : 'border-transparent text-zinc-500 hover:border-zinc-300 hover:text-zinc-700 font-medium',
      'whitespace-nowrap border-b-2 px-1 pb-2 text-sm cursor-pointer']"
      :aria-current="selectedId === id ? 'page' : undefined">{{ label }}
  </a>
</template>

<script>
export default {
  name: 'BaseTab',
  props: {
    id: {
      required: true
    },
    label: {
      type: String,
      required: true
    },
    selectedId: {
      required: true
    }
  },
  watch: {
    selectedId (newValue) {
      this.checkInView()
    }
  },
  mounted () {
    this.checkInView()
  },

  methods: {
    checkInView () {
      if (this.selectedId === this.id) {
        this.$refs.anchor.scrollIntoView({
          inline: 'center',
          behavior: 'smooth'
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
