import { createApp } from 'vue'
import { createHead, VueHeadMixin } from '@unhead/vue'

import App from './DataHero.vue'
import router from './router'
import './assets/tailwind.css'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import Big from 'big.js'

// Issues with Pinia showing in devtools - Before this had to go after the router; now it has to go first!
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
const head = createHead()

const app = createApp(App)

app.use(router).use(head).mixin(VueHeadMixin).use(pinia).mount('#datahero')

window.Big = Big
