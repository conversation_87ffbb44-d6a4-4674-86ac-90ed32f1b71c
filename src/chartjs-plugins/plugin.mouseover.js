import { modifyColor } from '@/chartjs-plugins/plugin.themes.js'

export const mouseoverPlugin = {
  id: 'mouseover',
  defaults: {
    box: undefined,
    modifiers: [
      ['clearer', 0.3],
      ['saturate', 0.2]
    ]
  },
  beforeLayout: (chart, event, options) => {
    if (options.box === 'title' || options.box === 'subtitle') {
      const obj = chart.options.plugins[options.box]
      obj.color = modifyColor(obj.color, options.modifiers)
    }
  }
}
