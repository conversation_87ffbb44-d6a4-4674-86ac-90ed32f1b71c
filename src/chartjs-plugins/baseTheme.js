// // Color modifiers: darken lighten saturate desaturate clearer opaquer
export const baseTheme = {
  
  dataColors: {
    borders: ['#36a2eb', '#ff6384', '#ff9f40', '#ffcd56', '#4bc0c0', '#9966ff', '#c9cbcf'],
    backgrounds: [
      ['clearer', 0.4]
    ],
    hovers: [
      ['saturate', 0.5],
      ['darken', 0.2]
    ]
  },
  
  interactions: {
    // mode is determined by chart type
    intersect: true,
    animationDuration: 500,
    legendToggle: true
  },
  
  backgroundColors: {
    colors: ['#ffffff', '#000000cc', '#ffffffbf', '#000'],  // default: pie, scales, tooltipMultikey
    canvas: 0,  // special case - canvas background color. If undefined, transparent
    tooltip: 1,
    scalesTicks: 2  // Only displays if showLabelBackdrop - which is only on for radial scale
  },
  
  borderColors: {
    colors: ['#0000001a', '#00000000'],  // default: scales scalesGrid scalesGridTick:
    tooltip: 1  // black but 100% transparent is the default
  },
  
  borderWidths: {
    base: 1,  // bar point scales scalesGrid scalesGridTick
    bar: 2,
    arc: 2,
    point: [1, 1],
    line: 3,
    tooltip: 0
  },
  
  radius: {
    base: 0,  // bar
    bar: 0,
    point: 4,
    pointHover: 6,
    pointHit: 20,
    tooltip: 6
  },
  
  padding: {
    base: 0,  // legendTitle scaleTicks
    layout: { left: 30, right: 50, top: 20, bottom: 20 },
    tooltip: 6,
    tooltipBox: 1,
    tooltipCaret: 2,
    title: { top: 0, bottom: 15 },
    subtitle: { top: 0, bottom: 15 },
    legendBottom: 15,
    legendLabels: 10,
    scalesTicksBackdrop: 2,
    scalesAutoSkip: 3,
    scalesTitle: 4
  },
  
  spacing: {
    base: 2, // tooltipBody tooltipTitle tooltipFooter
    arc: 0,
    tooltipTitleBottom: 6,
    tooltipFooterTop: 6
  },
  
  align: {
    base: 'start',  // start, center, end
    legend: 'end'
  },
  
  fonts: {
    colors: ['#606060', '#36a2eb', '#f0f0f0'],
    
    // Can be an array of arrays, and each custom def can refer to a 'stack' index
    stacks: ['Helvetica Neue', 'Helvetica', 'Arial', 'sans-serif'],
    base: {
      size: 14
    },
    title: {
      size: 30,
      color: 1,
      weight: 'bold'
    },
    subtitle: {
      size: 16
    },
    tooltip: {
      color: 2
    },
    tooltipTitle: {
      weight: 'bold',
      color: 2
    },
    tooltipFooter: {
      color: 2
    },
    scalesTitle: {
      weight: 'bold'
    }
  },
  
  modes: {
    pie: {
      condition: {
        type: 'pie'
      },
      align: {
        base: 'center',  // start, center, end
        legend: 'center'
      }
    },
    
    mobile: {
      condition: {
        width: {
          max: 800
        }
      },
      fonts: {
        base: {
          size: 12
        },
        title: {
          size: 20
        },
        subtitle: {
          size: 16
        },
        scalesTitle: {
          size: 14
        }
      },
      padding: {
        legendBottom: 10
      }
    },
    dark: {
      condition: {
        media: '(prefers-color-scheme: dark)'
      }
      /*
      backgroundColors: {
        colors: ['#000000', '#ffffffcc', '#000000bf']  // default: pie, scales, tooltipMultikey
      } */
    },
    print: {
      condition: {
        media: 'print'
      },
      dataColors: {
        borders: ['#000000', '#222222', '#444444', '#666666', '#888888', '#aaaaaa', '#cccccc']
      },
      
      fonts: {
        scalesTitle: {
          size: 50
        },
        colors: ['#000000', '#000000', '#ffffff']
      }
    }
  }
}
