import { merge } from 'chart.js/helpers'

import { baseTheme } from '@/chartjs-plugins/baseTheme.js'
import colorLib from '@kurkle/color'

function getBorderColor (theme, i) {
  return theme.dataColors.borders[i % theme.dataColors.borders.length]
}

export function modifyColor (colorString, modifiers) {
  /*
  Takes a color string, applies modifiers, and returns a hexString.
  
  Coverts string into a Color and, given an array of [modifierMethod, value] pairs,
  calls each method with the given numeric value, in order.
  
  Modifier should be one of: darken, lighten, saturate, desaturate, opaquer, clearer
  */
  let color = colorLib(colorString)
  
  // only proceed if we have modifiers, and it is an iterable (i.e. an array)
  if (modifiers && typeof modifiers[Symbol.iterator] === 'function') {
    // If modifiers is _not_ and array of arrays, then turn it into one
    if (!Array.isArray(modifiers[0])) {
      modifiers = [modifiers]
    }
    for (const [key, value] of modifiers) {
      if (typeof color[key] === 'function') {
        color = color[key](value)
      }
    }
  }
  return color.hexString()
  
}

function getBackgroundColor (theme, i) {
  if (Array.isArray(theme.dataColors.backgrounds)) {
    // if the first item in the array is itself an array, assume we have modifiers
    if (Array.isArray(theme.dataColors.backgrounds[0])) {
      return modifyColor(getBorderColor(theme, i), theme.dataColors.backgrounds)
    } else {
      return theme.dataColors.backgrounds[i % theme.dataColors.backgrounds.length]
    }
  } else {
    // If it's not an array, we don't have valid background colors, so we default to borders
    return theme.dataColors.borders[i % theme.dataColors.borders.length]
  }
}

export function createColorObject (colorDef, theme) {
  // First, make sure colorDef is always an object
  if (typeof colorDef === 'number') {
    colorDef = { index: colorDef }
  } else if (typeof colorDef === 'string') {
    colorDef = { border: colorDef }
  }
  
  if (colorDef.index !== undefined) {
    // If we have an index, lookup that border and background
    colorDef = {
      border: modifyColor(getBorderColor(theme, colorDef.index), colorDef.modifiers),
      background: modifyColor(getBackgroundColor(theme, colorDef.index), colorDef.modifiers)
    }
  }
  if (colorDef.background === undefined && Array.isArray(theme.dataColors.backgrounds[0])) {
    // If we don't have a background, and we have background modifiers, apply them to the given border
    colorDef = {
      border: colorDef.border,
      background: modifyColor(colorDef.border, theme.dataColors.backgrounds)
    }
  }
  
  // Always set the hovers
  colorDef.hoverBorder = modifyColor(colorDef.border, theme.dataColors.hovers)
  colorDef.hoverBackground = modifyColor(colorDef.background, theme.dataColors.hovers)
  return colorDef
  
}

function colorizeDataset (theme, dataset) {
  // if dataset.color is an array...
  if (Array.isArray(dataset.color)) {
    const colorObjects = dataset.color.map(colorDef => createColorObject(colorDef, theme))
    dataset.backgroundColor = colorObjects.map(color => color.border)
    dataset.hoverBackgroundColor = colorObjects.map(color => color.hoverBorder)
    
  } else if (dataset.color !== undefined) {
    const colorObject = createColorObject(dataset.color, theme)
    dataset.borderColor = colorObject.border
    dataset.backgroundColor = colorObject.background
    dataset.hoverBorderColor = colorObject.hoverBorder
    dataset.hoverBackgroundColor = colorObject.hoverBackground
  }
}

function fontStackToString (stack) {
  // Join the stack array, enclosing any font with space in single quotes
  return stack.map(font => font.includes(' ') ? `'${font}'` : font).join(', ')
}

function createFont (fontDef, fontStacks) {
  /*
  
  Create a Chart.js font object from a theme font definition
  
  Get the font stack from the theme font stacks. If fontStacks is _not_ an array of arrays, just return it all.
   */
  const stack = Array.isArray(fontStacks[0]) ? fontStacks[fontDef.stack || 0] : fontStacks
  return {
    family: fontStackToString(stack),
    size: fontDef.size,
    style: fontDef.style || 'normal',
    weight: fontDef.weight || 'normal',
    lineHeight: fontDef.lineHeight || 1.2
  }
}

// If it ends in a dot, we append `font` or `color`. If not, it's `Font` or `Color`

const plugins = ['title', 'subtitle', 'legend', 'tooltip']
const elements = ['arc', 'line', 'point', 'bar']

const fontMap = {
  title: 'title.',                  // default: Chart.defaults.color
  subtitle: 'subtitle.',            // default: Chart.defaults.color
  legend: 'legend.labels.',         // default: Chart.defaults.color
  legendTitle: 'legend.title.',     // default: Chart.defaults.color
  tooltip: 'tooltip.body',          // default: '#fff'
  tooltipTitle: 'tooltip.title',    // default: '#fff'
  tooltipFooter: 'tooltip.footer',  // default: '#fff'
  
  // Scale can be suffixed with Y or X to target just those axes
  scalesTicks: 'scales.ticks.',                     // default: Chart.defaults.color
  scalesTitle: 'scales.title.'                      // default: Chart.defaults.color
}

// These we all append "Color" or `color` too. If it ends =, then we don't append. Scales also a special case
const borderColorMap = {
  tooltip: 'tooltip.border',                     // default: 'rgba(0, 0, 0, 0)'
  scales: 'scales.border.',                      // default: Chart.defaults.borderColor
  scalesGrid: 'scales.grid.',                    // default: Chart.defaults.borderColor  (nb grid.color=borderColor of grid)
  scalesGridTick: 'scales.grid.tick'             // default: whatever grid.color is set to
}

const backgroundColorMap = {
  tooltip: 'tooltip.background', // default: 'rgba(0, 0, 0, 0.8)'
  tooltipMultikey: 'tooltip.multikeyBackground=',  // default: '#fff'
  scales: 'scales.background',                   // default: undefined (?)
  scalesTicks: 'scales.ticks.backdrop'          // default: rgba(255, 255, 255, 0.75)
}

/* width is assumed. Will be capitalized if it doesn't end with a dot */
const borderWidthMap = {
  bar: 'bar.border',
  point: 'point.border',
  arc: 'arc.border',
  line: 'line.border',
  tooltip: 'tooltip.border',
  scalesGrid: 'scales.grid.line',
  scalesGridTick: 'scales.grid.tick',
  scales: 'scales.border.'
}

const paddingMap = {
  layout: 'layout.',                            // default: 0
  legendTitle: 'legend.title.',                 // default: 0
  tooltip: 'tooltip.',                          // default: 6
  tooltipBox: 'tooltip.box',                    // default: 1
  tooltipCaret: 'tooltip.caret',                // default: 2
  title: 'title.',                              // default: 10
  subtitle: 'subtitle.',                        // default: 10
  legendLabels: 'legend.labels.',               // default: 10
  scalesTicks: 'scales.ticks.',                 // default: 0
  scalesTicksBackdrop: 'scales.ticks.backdrop', // default: 2
  scalesAutoSkip: 'scales.autoSkip',            // default: 3
  scalesTitle: 'scales.title.'                  // default: 4
}

const spacingMap = {
  tooltipBody: 'tooltip.body',
  tooltipTitle: 'tooltip.title',
  tooltipFooter: 'tooltip.footer',
  tooltipTitleBottom: 'tooltip.titleMarginBottom=',
  tooltipFooterTop: 'tooltip.footerMarginTop='
}

const alignMap = {
  title: 'title.',
  subtitle: 'subtitle.',
  legend: 'legend.'
}

const radiusMap = {
  point: 'point.',
  pointHover: 'point.hover',
  pointHit: 'point.hit',
  bar: 'bar.border',
  tooltip: 'tooltip.corner'
}

const interactionMap = {
  mode: 'interaction.mode',
  intersect: 'interaction.intersect',
  axis: 'interaction.axis',
  animationDuration: 'animation.duration',
  legendToggle: 'legend.onClick'  // set false to prevent toggling datasets on the legend
}

function buildDotPath (dotPath, suffix) {
  /*
  Changes the dotPath string, adding a suffix, prefixing plugins, elements or scales.
  
  If there is no suffix, make no changes.
  If it ends with `.`, append suffix to it.
  If it ends with '=', append nothing.
  Otherwise, append `suffix` to it with the first letter uppercased.
  
  We also prefix the dotpath with `plugins.` if it starts with a plugin name,
  or 'elements.' if it starts with an element name. If it starts with 'scales'
  we remove it, as we will already be deep into scales.
  
  Example:
  suffixDotPath('tooltip.', 'color') => 'plugins.tooltip.color'
  suffixDotPath('tooltip=', 'color') => 'plugins.tooltip'
  suffixDotPath('tooltip', 'color') => 'plugins.tooltipColor'
  */
  if (suffix) {
    if (dotPath.endsWith('.')) {
      dotPath += suffix
    } else if (dotPath === '.') {
      dotPath = suffix
    } else if (dotPath.endsWith('=')) {
      dotPath = dotPath.slice(0, -1)
    } else {
      dotPath += suffix[0].toUpperCase() + suffix.slice(1)
    }
  }
  if (plugins.some(plugin => dotPath.startsWith(plugin))) {
    dotPath = 'plugins.' + dotPath
  } else if (elements.some(element => dotPath.startsWith(element))) {
    dotPath = 'elements.' + dotPath
  }
  if (dotPath.startsWith('scales.')) {
    dotPath = dotPath.slice(7)
  }
  return dotPath
}

function setNestedObject (obj, dotPath, newValue) {
  /*
  Get the target object in a nested object structure.
  
  Takes a dotPath string like 'a.b.c' and sets the object at obj.a.b to newValue
  */
  let target = obj
  const path = dotPath.split('.')
  while (path.length > 1) {
    const segment = path.shift()
    if (!target[segment]) {
      target[segment] = {}
    }
    target = target[segment]
  }
  target[path.shift()] = newValue
}

function apply (chartOptions, map, themeSection, valueSetter) {
  /*
  Apply a setting value to opts.
  
  This loops through all paths in map and looks up a value from the theme. It
  calls the given valueSetter to apply the value to the chart options.
  */
  Object.keys(map).forEach(key => {
    let dotPath = map[key]
    if (dotPath.startsWith('scales.')) {
      // remove `scales.` from the dotpath
      Object.keys(chartOptions.scales).forEach(scaleKey => {
        const scale = chartOptions.scales[scaleKey]
        valueSetter(scale, dotPath, key, scale.axis.toUpperCase())
      })
    } else {
      valueSetter(chartOptions, dotPath, key)
    }
  })
}

function getFirstDefined (...args) {
  // Return the first value that is not undefined
  return args.find(value => value !== undefined)
}

function applyColors (chartOptions, colorMap, themeSection) {
  /*
  Applies color settings from the theme to the chart options.
  */
  apply(chartOptions, colorMap, themeSection, (obj, dotPath, key, axis) => {
    const indexes = [themeSection[key + axis], themeSection[key], 0]
    const color = indexes.map(index => themeSection.colors[index]).find(color => color !== undefined)
    setNestedObject(obj, buildDotPath(dotPath, 'color'), color)
  })
}

function applyFonts (chartOptions, themeSection) {
  const setFontAndColor = (obj, dotPath, key, axis) => {
    const fontDef = {
      ...themeSection.base,
      ...themeSection[key],
      ...themeSection[key + axis]
    }
    const font = createFont(fontDef, themeSection.stacks)
    const color = themeSection.colors[fontDef?.color || 0]
    setNestedObject(obj, buildDotPath(dotPath, 'font'), font)
    setNestedObject(obj, buildDotPath(dotPath, 'color'), color)
  }
  apply(chartOptions, fontMap, themeSection, setFontAndColor)
}

function applyBorderWidths (chartOptions, themeSection) {
  apply(chartOptions, borderWidthMap, themeSection, (obj, dotPath, key, axis) => {
    dotPath = buildDotPath(dotPath, 'width')
    const width = getFirstDefined(themeSection[key + axis], themeSection[key], themeSection.base)
    if (Array.isArray(width)) {
      // If an array, the second value is the hover version
      setNestedObject(obj, dotPath, width[0])
      setNestedObject(obj, dotPath.replace('border', 'hoverBorder'), width[1])
    } else {
      setNestedObject(obj, dotPath, width)
    }
  })
}

function applyInteractions (chartOptions, themeSection) {
  apply(chartOptions, interactionMap, themeSection, (obj, dotPath, key) => {
    const suffixedDotPath = buildDotPath(dotPath)
    const val = themeSection[key]
    
    if (dotPath === 'interaction.mode') {
      // Skip setting interaction mode from theme - it's now set based on chart type
      
    } else if (dotPath === 'legend.onClick') {
      // special case for legend.onClick, which is a boolean - requires explicit 'false' to disable
      if (val === false) {
        setNestedObject(obj, suffixedDotPath, false)
      } else {
        // Force-setting to undefined will use the plugin default - which toggles the dataset
        setNestedObject(obj, suffixedDotPath, undefined)
      }
    } else {
      setNestedObject(obj, suffixedDotPath, val)
    }
  })
}

function applySection (chartOptions, themeSection, map, suffix) {
  apply(chartOptions, map, themeSection, (obj, dotPath, key) => {
    setNestedObject(obj, buildDotPath(dotPath, suffix), themeSection[key] || themeSection.base)
  })
}

function createMqls (chart, options, themeModes) {
  /*
  Set up event listeners.
  
  changeMode is a function that takes a key and a boolean.
  If the boolean is true, it adds the key to the modes array.
  If false, it removes it.
  
  We set up a listener for each theme mode that has a 'media' condition,
  and call changeMode. We keep track of all listeners so they
  can be destroyed properly in beforeDestroy.
   */
  const changeMode = (key, matches) => {
    if (matches) {
      if (!chart.$theme.selectedAutoModes.includes(key)) {
        chart.$theme.selectedAutoModes.push(key)
        chart.update()
      }
    } else {
      const index = chart.$theme.selectedAutoModes.indexOf(key)
      if (index > -1) {
        chart.$theme.selectedAutoModes.splice(index, 1)
        chart.update()
      }
    }
  }
  for (const key in themeModes) {
    // Only create media queries for modes that have a media condition
    const media = themeModes[key].condition.media
    if (media) {
      const mql = window.matchMedia(media)
      const listener = e => changeMode(key, e.matches)
      mql.addEventListener('change', listener)
      chart.$theme.mqls.push([mql, listener])
    }
  }
}

export const chartThemePlugin = {
  id: 'themes',
  
  defaults: {
    // Chosen theme object for this chart. Will be merged with baseTheme
    theme: {},
    
    // Manually selected modes
    mode: undefined,
    modes: [],
    
    // can be true to enable, false to disable, or an array of modes to support
    allowAutoModes: true
    
  },
  
  beforeDestroy (chart) {
    // destroy all chart listeners in chart.mqls
    for (const [mql, listener] of chart.$theme.mqls) {
      mql.removeEventListener('change', listener)
    }
  },
  
  beforeDraw: (chart) => {
    // https://www.chartjs.org/docs/latest/configuration/canvas-background.html
    const themeBgColors = chart.$theme.modded.backgroundColors
    if (themeBgColors.canvas !== undefined) {
      chart.ctx.save()
      chart.ctx.fillStyle = themeBgColors.colors[themeBgColors.canvas]
      chart.ctx.fillRect(0, 0, chart.width, chart.height)
      chart.ctx.restore()
    }
  },
  
  beforeInit: function (chart, args, pluginOptions) {
    /*
    Build the base theme on load.
    
    This will be further tweaked with modes in beforeLayout. We also have to set a callback for
    custom legend padding.
    */
    
    // All our plugin settings are stored here
    chart.$theme = {
      mqls: [],
      allowedAutoModes: [],
      selectedAutoModes: [],
      base: {},
      modded: {}
    }
    
    // Build up a new theme object from scratch
    const theme = {}
    merge(theme, baseTheme)
    merge(theme, pluginOptions.theme)
    if (pluginOptions.allowAutoModes) {
      chart.$theme.allowedAutoModes = Object.fromEntries(Object.entries(theme.modes).filter(([key]) => pluginOptions.allowAutoModes === true || pluginOptions.allowAutoModes.includes(key)))
    }
    createMqls(chart, pluginOptions, chart.$theme.allowedAutoModes)
    for (const [key, mode] of Object.entries(chart.$theme.allowedAutoModes)) {
      // Check every media query manually to set the initial modes that match the window
      if (mode.condition.media && window.matchMedia(mode.condition.media).matches) {
        chart.$theme.selectedAutoModes.push(key)
      }
    }
    chart.$theme.base = theme
    
    // Special legend padding [https://stackoverflow.com/questions/42585861/chart-js-increase-spacing-between-legend-and-chart/67723827#67723827
    const originalFit = chart.legend.fit
    chart.legend.fit = function fit () {
      originalFit.bind(chart.legend)()
      if (chart.$theme.modded) { // make sure only fire after beforeLayout
        const paddingOptions = chart.$theme.modded.padding
        this.height += (paddingOptions.legendBottom || paddingOptions.base)
      }
    }
  },
  
  beforeLayout (chart, args, pluginOptions) {
    // Build a new theme based on the saved one done on init + any user themes
    const theme = {}
    merge(theme, chart.$theme.base)
    
    // Check manual automodes: chart type and chart width. We build up a new list from scratch each time to save having to remove
    const conditionalAutoModes = []
    for (const [key, mode] of Object.entries(chart.$theme.allowedAutoModes)) {
      // Chart type conditions
      if (mode.condition.type && mode.condition.type === chart.config.type) {
        conditionalAutoModes.push(key)
      }
      
      // Chart width conditions
      if (mode.condition.width) {
        const width = mode.condition.width
        if (chart.width > (width.min || -1) && chart.width < (width.max || Infinity)) {
          conditionalAutoModes.push(key)
        }
      }
    }
    
    // join all modes in the set into a string, space separated
    const allModes = new Set([...chart.$theme.selectedAutoModes, ...conditionalAutoModes, pluginOptions.mode, ...pluginOptions.modes].filter(Boolean))
    chart.canvas.setAttribute('data-modes', [...allModes].join(' '))
    for (const mode of allModes) {
      merge(theme, theme.modes[mode])
    }
    
    // save to our object, as required in other methods
    chart.$theme.modded = theme
    // Now we finally apply our theme - to our datasets and then each section onto the chart options
    chart.data.datasets.forEach(ds => colorizeDataset(theme, ds))
    const opts = chart.options
    
    // HACK - we just can't get our setNestedObject code playing nicely with Chart.js' (v confusing!) option caching system
    // if it doesn't exist, then we get errors when we try to print out the raw options - but I can't work out how actually
    // one detects if it is "present" or not as it always falls through to the default value. So we just set it to an empty object here.
    opts.elements = {}
    
    // Now apply every setting in term
    applyFonts(opts, theme.fonts)
    applyColors(opts, borderColorMap, theme.borderColors)
    applyColors(opts, backgroundColorMap, theme.backgroundColors)
    applyBorderWidths(opts, theme.borderWidths)
    applySection(opts, theme.padding, paddingMap, 'padding')
    applySection(opts, theme.spacing, spacingMap, 'spacing')
    applySection(opts, theme.align, alignMap, 'align')
    applySection(opts, theme.radius, radiusMap, 'radius')
    applyInteractions(opts, theme.interactions)
  }
}
