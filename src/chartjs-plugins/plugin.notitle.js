import { modifyColor } from '@/chartjs-plugins/plugin.themes.js'

export const noTitlePlugin = {
  id: 'noTitle',
  defaults: {
    message: 'Add a chart title...',
    modifiers: ['clearer', 0.4]
  },
  beforeLayout: (chart, event, options) => {
    const title = chart.options.plugins.title
    title.display = true
    title.text = options.message
    title.color = modifyColor(title.color, options.modifiers)
  }
}
