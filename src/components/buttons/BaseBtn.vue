<template>
  <button
      :type="isSubmit ? 'submit' : 'button'"
      :disabled="loading || disabled"
      class="font-medium flex items-center group"
      :class="[
        isText
          ? 'border border-transparent bg-transparent hover:bg-transparent'
          : 'border rounded-md shadow-xs',
        fullWidth ? 'w-full justify-center' : '',
        loading || disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
        small ? 'px-2.5 py-1.5 text-xs' : 'px-4 py-2 text-sm',
        large ? 'px-6 py-3 text-lg' : '',
        xl ? 'px-8 py-4 text-xl' : '',
        // for button blocks
        isFirst ? 'border-r-0 rounded-r-none' : '',
        isMiddle ? 'border-r-0 rounded-l-none rounded-r-none' : '',
        isLast ? 'rounded-l-none' : '',
        this.typeClasses,
      ]"
  >
    <span v-if="iconComponent && iconLeft" class="btn-image block shrink-0">
      <component
          v-if="iconComponent"
          :is="iconComponent"
          :class="allIconClasses"
      />
    </span>

    <span class="block btn-text flex-1">
      <slot></slot>
    </span>

    <span v-if="!iconLeft && (loading || iconComponent)" class="btn-image block shrink-0">
      <component
          v-if="iconComponent"
          :is="iconComponent"
          :class="allIconClasses"
      />
      <svg
          v-else
          class="inline-block animate-spin ml-3 h-5 w-5 text-white"
          :class="iconClasses"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
      >
        <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
        ></circle>
        <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
    </span>
  </button>
</template>

<script>
export default {
  props: {
    fullWidth: Boolean,
    loading: Boolean,
    disabled: Boolean,
    small: Boolean,
    large: Boolean,
    xl: Boolean,

    iconComponent: [Function, Object],
    iconClasses: {
      type: String,
      default: ''
    },

    // iconPosition can be one of - left, right, undefined. If undefined, we set it auto
    iconLeft: {
      type: Boolean,
      default: false
    },

    isText: Boolean,
    isSubmit: Boolean,
    type: String,
    outline: Boolean,

    // for button blocks
    isFirst: Boolean,
    isMiddle: Boolean,
    isLast: Boolean
  },
  computed: {

    typeClasses () {
      if (this.isText) {
        switch (this.type) {
          case 'warning':
            return 'text-red-500 hover:text-red-400'

          case 'primary':
            return 'text-fuchsia-500 hover:text-fuchsia-400'

          case 'dark':
            return 'text-zinc-300 hover:text-zinc-100'

          case 'light':
          default:
            return 'text-zinc-700 hover:text-zinc-500'
        }

      } else if (this.outline) {
        switch (this.type) {
          case 'warning':
            return 'text-red-500 hover:text-white border-red-500 hover:bg-red-600 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500'

          case 'primary':
            return 'text-fuchsia-500 hover:text-white border-fuchsia-500 hover:bg-fuchsia-600 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-fuchsia-500'

          case 'dark':
            return 'text-zinc-300 hover:text-white border-zinc-500 hover:bg-zinc-600 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-fuchsia-500'

          case 'light':
          default:
            return 'text-zinc-700 hover:text-white border-zinc-300 hover:bg-zinc-600 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-fuchsia-500'
        }

      } else {
        switch (this.type) {
          case 'warning':
            return 'text-white hover:text-white border-zinc-300 bg-red-600 hover:bg-red-500 focus:online-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'

          case 'primary':
            return 'text-white hover:text-white border-transparent bg-fuchsia-600 hover:bg-fuchsia-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-fuchsia-500'

          case 'dark':
            return 'text-white hover:text-white border-transparent bg-zinc-700 hover:bg-zinc-600 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-fuchsia-500'

          case 'light':
          default:
            return 'text-zinc-700 border-zinc-300 bg-white hover:bg-zinc-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-fuchsia-500'
        }
      }
    },

    allIconClasses () {
      const size = `size-${this.small ? 4 : 5}`
      const margin = this.iconLeft ? 'mr-2' : 'ml-2'
      return `${this.iconClasses} ${size} ${margin}`
    }

  }
}
</script>

<style scoped></style>
