<template>
  <div
      v-if="showChart"
      class="h-full w-full chart-viewer p-2"
      :key="chartKey">
    <data-hero-chart
        :type="chartConfig.type"
        :data="chartConfig.data"
        :show-no-title="!chartStoreTitle"
        :options="chartConfig.options"
        @box-click="$emit('box-click', $event)"
        @point-click="$emit('cell-click', pointToTableCell($event))"
    />
  </div>
</template>

<script>

/*
ChartViewer: A wrapper around DataHeroChart that takes a chartId and renders the chart.

This can take a chartId and looks up its config from chart store (if that one is loaded)
or from the table store (if the chart is not being edited). It combines this with
the table data (columns and rows).

It also listens for clicks on the chart and bubbles them up with relevant table rows/cells information.
 */

import { mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'
import { useTableStore } from '@/stores/table.js'
import { getChartConfig } from '@/charts/chartConfig.js'
import DataHeroChart from '@/components/datahero/DataHeroChart.vue'
import { getChartData, getChartProps } from '@/charts/chartProps.js'

// noinspection JSValidateTypes
export default {
  components: { DataHeroChart },
  emits: ['cell-click', 'box-click'],
  data: () => ({
    chartConfig: undefined,
    chartData: undefined
  }),
  props: {
    chartId: {
      type: String,
      required: false
    },
    alwaysRender: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(useTableStore, {
      tableStoreLastUpdate: 'lastUpdate',
      tableStoreCharts: 'charts',
      tableStoreDbTable: 'dbTable'
    }),

    chartStore () {
      const store = useChartStore()
      if (this.chartId === store.id) {
        // We do this so it reacts immediately to store changes as we edit it
        return store
      }
      // But if we're not editing it, we just get it from the tableStoreCharts
      return this.tableStoreCharts.find(chart => chart.id === this.chartId)
    },

    chartStoreData () {
      return this.chartStore?.data
    },

    chartKey () {
      // In the past we've done a more complex chart key to get around some render issues (see bug graveyard, chart key)
      // we have it disabled for now and just use the chartId; is problem resolved? needs more testing
      // return this.chartStoreData.datasets.map(ds => ds.series.join(''))
      // Note that this can be null when creating a new chart; this doesn't seem to cause issues and makes for a nice
      // animation as the user changes the data range
      return this.chartId
    },

    showChart () {
      // We show the chart if we have an id, _or_ specifically if we're in 'add' mode - we don't
      // want to show otherwise, as we might be building a new chart that's about to be saved. If
      // we just use e.g. data, we can get a nasty 'double render' of a chart when creating new charts
      return (this.chartId || this.alwaysRender) && this.chartConfig?.data !== undefined
    },

    chartStoreTitle () {
      return this.chartStore?.title
    },

    chartStoreViewId () {
      return this.chartStore?.viewId
    }
  },

  watch: {
    /* when anything in the chart config changes, we build up a props object */
    chartStoreData: {
      handler (newVal) {
        if (newVal) {
          this.configureChart()
        }
      },
      deep: true,
      immediate: true
    },

    // ths is outside csd; doesn't have to be deep
    chartStoreTitle: 'configureChart',

    // when the table changes, we rebuild the chart
    tableStoreLastUpdate: 'configureChart'
  },

  methods: {

    async configureChart () {
      if (!this.chartStoreData || !this.chartStoreData.datasets.length) return
      const tableCols = useTableStore().columns
      const view = useTableStore().views.find(v => v.id === this.chartStoreViewId)
      const props = getChartProps(this.chartStoreTitle, this.chartStoreData, tableCols, view)
      this.chartData = await getChartData(props, this.tableStoreDbTable)
      this.chartConfig = getChartConfig(this.chartData)
    },

    pointToTableCell ({ datasetIndex, index: pointIndex }) {
      // Takes a selected point in the event and returns a row and column that maps to the table
      const { type: chartType } = this.chartConfig
      const { byRow } = this.chartStoreData

      // Handle different chart types
      if (chartType === 'pie') {
        const ds = this.chartStoreData.datasets[datasetIndex]
        return byRow
               ? { column: ds.values[pointIndex], row: ds.series[0] }
               : { column: ds.series[0], row: ds.values[pointIndex] }
      }

      if (chartType === 'scatter') {
        // Scatter charts have only one dataset with two series (X and Y axes)
        const ds = this.chartStoreData.datasets[0]

        if (byRow) {
          // In byRow mode, series are row IDs and values are column IDs
          return { column: ds.values[pointIndex], row: ds.series[datasetIndex] }
        } else {
          // In normal mode, highlight the X-axis column (first series)
          const row = this.chartData.rows[pointIndex].id
          return { column: ds.series[0], row }
        }
      }

      // Handle bar, line, and mixed charts
      const ds = this.chartConfig.data.datasets[datasetIndex]

      if (byRow) {
        // In byRow mode, values represent columns and series represent rows
        return { column: this.chartStoreData.datasets[0].values[pointIndex], row: ds.refId }
      } else {
        // In normal mode, series represent columns and point index maps to row
        const row = this.chartData.rows[pointIndex].id
        return { column: ds.refId, row }
      }
    }
    /*
    // this old code gets the automatic min/max values when a chart is rendered to use
    // for defaults on sliders
    getChartValuesRange () {
      // If we ever reimplement this, don't hard-code y-axis-0, + this should be flexible based on horiz or vert chart
      const yaxis = this.getActiveChart().scales['y-axis-0']
      return { min: yaxis.min, max: yaxis.max }
    },
    chartRendered() {
      this.setReportedValuesMinMax(this.getChartValuesRange());
    },
    chartUpdated() {
      this.setReportedValuesMinMax(this.getChartValuesRange());
    },
     */
  }
}
</script>

<style scoped>
.chart-viewer > div {
  width: 100%;
  height: 100%;
}
</style>
