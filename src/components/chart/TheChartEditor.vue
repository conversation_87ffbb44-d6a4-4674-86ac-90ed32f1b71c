<template>
  <div class="flex flex-col bg-white text-gray-900 text-base h-full">
    <header>
      <div class="py-6 px-4 bg-fuchsia-700 sm:px-6">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-white">Edit Chart</h2>
          <div class="ml-3 h-7 flex items-center">
            <button type="button"
                    class="bg-fuchsia-700 rounded-md text-fuchsia-200 hover:text-white cursor-pointer"
                    @click="saveAndClose">
              <span class="sr-only">Close panel</span>
              <X class="h-6 w-6" aria-hidden="true"/>
            </button>
          </div>
        </div>
      </div>
    </header>
    <step-selector
        class="flex-none"
        :steps="steps"
        :active-step="step"
        @step-to="routeToStep($event)"
    />
    <div class="flex-1 flex flex-col justify-between overflow-y-auto" v-if="showComponent">
      <component
          :is="steps[step].component"
          :dataset-index="steps[step].datasetIndex"
          :row-options="rowOptions.value"
          :column-options="columnOptions.value"
          :axis-limits="axisLimits"
          @next="nextStep"
          @previous="prevStep"
      />
    </div>
    <div class="flex-none flex p-4 border-t bg-gray-100 border-gray-300">
      <btn-group class="flex-1">
        <base-btn is-text :disabled="step === 0" @click.prevent="prevStep">&laquo; Prev</base-btn>
        <base-btn is-text :disabled="step === steps.length - 1" @click.prevent="nextStep">Next &raquo;</base-btn>
      </btn-group>
      <btn-group>
        <base-btn @click.prevent="revertAndClose" type="warning" is-text>Undo</base-btn>
        <base-btn @click.prevent="saveAndClose" type="primary" is-submit>Save and Close</base-btn>
      </btn-group>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { mapActions, mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'
import { useTableStore } from '@/stores/table.js'
import { X } from 'lucide-vue-next'
import { getChartType } from '@/utils/chartTypes.js'
import BaseSide from '@/components/slider/BaseSide.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import ChartStepData from '@/components/chart/editor/ChartStepData.vue'
import ChartStepDesign from '@/components/chart/editor/ChartStepDesign.vue'
import ChartStepSelector from '@/components/chart/editor/StepSelector.vue'
import StepSelector from '@/components/chart/editor/StepSelector.vue'
import ChartStepLabels from '@/components/chart/editor/ChartStepLabels.vue'
import BtnGroup from '@/components/buttons/BtnGroup.vue'
import { autoRowLabel, getColumnHeaderById } from '@/charts/chartConfig.js'
import { isNumericType, repr } from '@/utils/formats.js'
import { filterQuerySpec, getChartQuerySpec } from '@/db/query/utils.js'

export default {
  components: {
    BtnGroup,
    StepSelector,
    BaseSide,
    BaseBtn,
    X,
    ChartStepData,
    ChartStepDesign,
    ChartStepSelector
  },
  data () {
    return {
      // Make this a reactive property
      rowOptions: ref([]),
      columnOptions: ref([]),
      axisLimits: {
        min: 0,
        max: 100
      }
    }
  },
  emits: ['update:step', 'save-chart', 'cancel', 'close'],

  props: {
    step: Number,
    viewId: String
  },

  // watch viewId change and call setOptions
  watch: {
    'dataset.series': {
      handler () {
        this.updateAxisValues()
      },
      immediate: true,
      deep: true
    },
    'dataset.values': {
      handler () {
        this.updateAxisValues()
      },
      immediate: true,
      deep: true
    },

    viewId: {
      immediate: true,
      handler () {
        this.setOptions()
      }
    },

    // Update options when table changes
    tableStoreLastUpdate: 'setOptions',

    // Also refresh options when user selects a label column
    'chartStoreData.axis.column': 'setOptions'

  },

  computed: {
    ...mapState(useChartStore, {
      chartStoreId: 'id',
      chartStoreData: 'data',
      chartStoreLabelColumnId: 'labelColumnId'
    }),
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreDbTable: 'dbTable',
      tableStoreLastUpdate: 'lastUpdate'
    }),

    showComponent () {
      // Only load component once we have row and columns options set, so all child
      // components can rely on filled arrays
      return this.rowOptions.value?.length && this.columnOptions.value?.length
    },

    steps () {
      const chartLabel = (chartType) => getChartType(chartType)?.text
      const dsLabel = (chartType) => chartType ? `${chartLabel(chartType)} Chart` : 'Chart Data'
      return [
        {
          component: ChartStepData,
          label: dsLabel(this.chartStoreData.datasets[0]?.chartType),
          datasetIndex: 0,
          visible: true
        },
        {
          component: ChartStepData,
          label: dsLabel(this.chartStoreData.datasets[1]?.chartType),
          datasetIndex: 1,
          visible: this.chartStoreData.datasets.length === 2
        },
        {
          component: ChartStepDesign,
          label: 'Design',
          visible: true
        },
        {
          component: ChartStepLabels,
          label: 'Titles & Labels',
          visible: true
        }
      ]
    },

    columns () {
      return this.tableStoreGetViewColumns(this.viewId)
    },

    querySpec () {
      return getChartQuerySpec(
          useTableStore().columns,
          useTableStore().views.find(v => v.id === this.viewId)
      )
    },

    dataset () {
      return this.chartStoreData.datasets[this.steps[this.step].datasetIndex]
    }

  },

  methods: {
    ...mapActions(useChartStore, {
      chartStoreLoadFromTable: 'loadFromTable'
    }),
    ...mapActions(useTableStore, {
      tableStoreGetViewColumns: 'getViewColumns',
      tableStoreGetQuerySpec: 'getQuerySpec'
    }),

    getChartMinMaxAggregates () {
      // Get the min/max values for the chart's series
      const numericColumnIds = this.columns.filter(c => isNumericType(c.type)).map(c => c.id)
      if (this.chartStoreData.byRow) {
        // If we're doing by row, then we need to get the min/max for each column in values
        // and limit the query to the rows in series
        const querySpec = filterQuerySpec(this.querySpec, { rowIds: this.dataset.series })

        return this.dataset.values.filter(id => numericColumnIds.includes(id)).map(colId =>
            this.tableStoreDbTable.aggregate(colId, querySpec, ['min', 'max'])
        )
      }

      // Otherwise, we just need to get the min/max for each column in series; select on all rows (even if chart is limited
      // to a row, its more natural to show the full potential range here)
      return this.dataset.series.filter(id => numericColumnIds.includes(id)).map(colId =>
          this.tableStoreDbTable.aggregate(colId, ['min', 'max'], this.querySpec)
      )
    },

    async updateAxisValues () {
      if (!this.dataset?.series?.length) {
        this.axisLimits = {
          min: 0,
          max: 100
        }
        return
      }
      const results = await Promise.all(this.getChartMinMaxAggregates())
      this.axisLimits = {
        min: Math.min(...results.map(r => r.min ?? 0)),
        max: Math.max(...results.map(r => r.max ?? 0))
      }
    },

    async setOptions () {
      const labelCol = this.columns.find(c => c.id === this.chartStoreLabelColumnId)
      const colIds = labelCol === undefined ? [] : [labelCol.id]
      const rowObjects = await this.tableStoreDbTable.rowObjects(colIds, this.querySpec)
      this.rowOptions.value = rowObjects.map((row, index) => {
        const dbLabel = row[`c${this.chartStoreLabelColumnId}`]
        const label = (dbLabel === undefined || dbLabel === null)
                      ? autoRowLabel(undefined, index)
                      : repr(dbLabel, labelCol.type, labelCol.props)

        // Hack - we duplicate label because some components expect it to be in the format [`c`id...]
        return { id: row.id, label }
      })

      this.columnOptions.value = this.columns.map(col => ({
        id: col.id,
        label: getColumnHeaderById(this.columns, col.id)
      }))
    },

    routeToStep (step) {
      this.$router.push({
        name: `app-${this.viewId ? 'view' : 'table'}-chart-edit`,
        params: {
          tableId: this.tableStoreId,
          chartId: this.chartStoreId,
          editStep: step
        }
      })
    },

    nextStep () {
      // get the next step in steps that has visible = true
      const nextStep = this.steps.findIndex((s, i) => i > this.step && s.visible)
      this.routeToStep(nextStep)
    },

    prevStep () {
      // get the previous step in steps that has visible = true
      const goBack = this.steps
          .slice(0, this.step)
          .reverse()
          .findIndex((s) => s.visible)
      const prevStep = this.step - goBack - 1
      this.routeToStep(prevStep)
    },

    async saveAndClose () {
      this.$emit('save-chart')
      this.$emit('close')
    },

    async revertAndClose () {
      await this.chartStoreLoadFromTable(this.chartStoreId)
      this.$emit('close')
    }
  }
}
</script>

<style scoped></style>
