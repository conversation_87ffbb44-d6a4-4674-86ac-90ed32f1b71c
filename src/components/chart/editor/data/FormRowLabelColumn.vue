<template>
  <form-grid-row v-if="chartStoreData.axis" label="Column for labels" input-id="label-column">
    <select
        v-model.number="chartStoreData.axis.column"
        id="label-column"
        class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
      <option disabled>(Select)</option>
      <option
          v-for="col in columnOptions"
          :value="col.id"
          :key="col.id">{{ col.label }}
      </option>
    </select>
  </form-grid-row>
</template>
<script>
import { mapState } from 'pinia'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import { useChartStore } from '@/stores/chart.js'

export default {
  name: 'form-row-label-column',
  components: { FormGridRow },
  props: {
    columnOptions: Array
  },
  computed: {
    ...mapState(useChartStore, {
      chartStoreData: 'data'
    })
  }
}
</script>
