<template>
  <form-grid-section>
    <form-grid-row label="Values for X axis" input-id="x-axis">
      <select
          v-model.number="xAxis"
          id="x-axis"
          class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
        <option disabled>(Select)</option>
        <option v-for="opt in indexOptionsX" :value="opt.id" :key="opt.id">{{ opt.label }}</option>
      </select>
    </form-grid-row>
    <form-grid-row label="Values for Y axis" input-id="y-axis">
      <select
          v-model.number="yAxis"
          id="y-axis"
          class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
        <option disabled>(Select)</option>
        <option v-for="opt in indexOptionsY" :value="opt.id" :key="opt.id">{{ opt.label }}</option>
      </select>
    </form-grid-row>
  </form-grid-section>

</template>

<script>
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import FormLabelDatasets from './ChartStepDataLabelDatasets.vue'
import { mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'

export default {
  name: 'FormRowScatterSelect',
  components: {
    FormGridSection,
    FormLabelDatasets,
    FormGridRow
  },
  props: {
    rowOptions: Array,
    columnOptions: Array
  },
  computed: {
    ...mapState(useChartStore, {
      chartStoreData: 'data'
    }),

    ds () {
      return this.chartStoreData.datasets[0]
    },
    xAxis: {
      get () {
        return this.ds.series[0]
      },
      set (value) {
        this.ds.series[0] = value
      }
    },
    yAxis: {
      get () {
        return this.ds.series[1]
      },
      set (value) {
        this.ds.series[1] = value
      }
    },
    indexOptionsX () {
      return this.chartStoreData.byRow ? this.rowOptions : this.columnOptions.filter(opt => opt.id !== this.chartStoreData.axis.column)
    },
    indexOptionsY () {
      const opts = this.chartStoreData.byRow ? this.rowOptions : this.columnOptions.filter(opt => opt.id !== this.chartStoreData.axis.column)
      return opts.filter(opt => opt.id !== this.xAxis)
    }
  }
}

</script>

<style scoped>

</style>
