<template>

  <form-grid-row label="Show axis">
    <base-toggle
        v-model="dataset.axis.show"
        left-label="No"
        label="Yes"
        @update:model-value="handleAxisToggle"/>
  </form-grid-row>

  <template v-if="dataset.axis.show">
    <form-grid-row label="Scale range">
      <select
          :value="dataset.axis.range"
          @change="e => e.target.value ? dataset.axis.range = e.target.value : delete dataset.axis.range"
          class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
        <option value="">Automatic</option>
        <option value="zero">Zero to max</option>
        <option value="auto">Min to max</option>
        <option value="custom">Custom range</option>
      </select>
    </form-grid-row>

    <template v-if="dataset.axis.range === 'custom'">
      <form-grid-row label="Minimum value">
        <div class="flex items-center gap-4">
          <input
              type="range"
              :min="sliderMin"
              :max="sliderMax"
              :step="stepSize"
              :value="dataset.axis.min"
              @input="handleMinInput"
              class="block accent-fuchsia-500 w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md"/>
          <input
              type="number"
              :value="dataset.axis.min"
              @input="handleMinInput"
              :step="stepSize"
              class="w-24 shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md"/>
        </div>
      </form-grid-row>

      <form-grid-row label="Maximum value">
        <div class="flex items-center gap-4">
          <input
              type="range"
              :min="sliderMin"
              :max="sliderMax"
              :step="stepSize"
              :value="dataset.axis.max"
              @input="handleMaxInput"
              class="block accent-fuchsia-500 w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md"/>
          <input
              type="number"
              :value="dataset.axis.max"
              @input="handleMaxInput"
              :step="stepSize"
              class="w-24 shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md"/>
        </div>
      </form-grid-row>
    </template>
  </template>
  <form-grid-row v-if="dataset.axis.show" label="Max ticks">
    <div class="flex items-center gap-4 mt-2">
      <input
          type="range"
          min="2"
          max="16"
          step="1"
          :value="dataset.axis.maxTicks ?? 11"
          @input="e => {dataset.axis.maxTicks = parseInt(e.target.value)}"
          class="block accent-fuchsia-500 w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md"/>
      <span class="text-sm text-gray-500">{{ dataset.axis.maxTicks ?? 11 }}</span>
    </div>
  </form-grid-row>

</template>

<script>
import { mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'

export default {
  name: 'ChartStepDataAxis',
  components: {
    FormGridSection,
    FormGridRow,
    BaseToggle
  },
  props: {
    datasetIndex: {
      type: Number,
      required: true
    },
    axisLimits: {
      type: Object,
      required: true
    }
  },
  emits: ['check-stacking'],
  computed: {
    ...mapState(useChartStore, {
      chartStoreData: 'data'
    }),

    dataset () {
      return this.chartStoreData.datasets[this.datasetIndex]
    },

    suggestedMin () {
      return Math.floor(this.axisLimits.min / this.stepSize) * this.stepSize
    },

    suggestedMax () {
      return Math.ceil(this.axisLimits.max / this.stepSize) * this.stepSize
    },

    sliderMin () {
      return Math.min(0, this.suggestedMin)
    },

    sliderMax () {
      return Math.max(0, this.suggestedMax + this.stepSize)
    },

    stepSize () {
      const range = this.axisLimits.max - this.axisLimits.min
      const magnitude = Math.pow(10, Math.floor(Math.log10(range)))

      const factors = [1, 2, 5]
      for (const factor of factors) {
        const step = magnitude * factor
        const numSteps = Math.ceil(range / step)
        if (numSteps <= 8) {
          return step
        }
      }
      return magnitude * 5
    }
  },

  methods: {
    handleAxisToggle (value) {
      this.$emit('check-stacking')
      if (!value) {
        // Reset axis configuration to defaults
        this.dataset.axis = {
          show: false
        }
      }
    },
    handleMinInput (e) {
      const input = e.target
      const newMin = Number(input.value)
      if (newMin < this.dataset.axis.max) {
        this.dataset.axis.min = newMin
      } else {
        input.value = this.dataset.axis.min ?? 0
      }
    },

    handleMaxInput (e) {
      const input = e.target
      const newMax = Number(input.value)
      if (newMax > this.dataset.axis.min) {
        this.dataset.axis.max = newMax
      } else {
        input.value = this.dataset.axis.max ?? 0
      }
    }
  },

  watch: {
    'dataset.axis.range': {
      handler: function () {
        if (this.dataset?.axis) {
          if (this.dataset.axis.range === 'custom') {
            this.$nextTick(() => {
              this.dataset.axis.min = this.suggestedMin
              this.dataset.axis.max = this.suggestedMax
            })
          } else {
            delete this.dataset.axis.min
            delete this.dataset.axis.max
          }
        }
      },
      deep: true
    }
  }
}
</script>
