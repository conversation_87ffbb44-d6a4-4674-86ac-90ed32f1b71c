<template>
  <div class="text-base font-medium text-gray-900 sm:text-sm sm:text-gray-700">
    {{ label }}
    <div>
      <a
          class="text-xs text-fuchsia-600 underline cursor-pointer"
          @click.prevent="$emit('select-all')">check all</a><br>
      <a
          class="text-xs text-fuchsia-600 underline cursor-pointer"
          @click.prevent="$emit('select-none')">uncheck all</a>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ChartStepDataLabelDatasets',
  props: {
    label: String
  }
}
</script>
