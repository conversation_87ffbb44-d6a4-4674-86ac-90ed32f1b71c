<template>
  <form-grid-section title="Line style options">
    <form-grid-row label="Line style">
      <select
          v-model="lineStyle"
          id="lineStyle"
          class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
        <option value="normal">Normal</option>
        <option value="stepped">Stepped</option>
        <option value="tension">Smooth</option>
      </select>
    </form-grid-row>

    <form-grid-row v-if="lineStyle === 'stepped'" label="Step position">
      <select
          :value="dataset.stepped ?? 'before'"
          @input="e => dataset.stepped = e.target.value"
          id="stepped"
          class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
        <option value="before">Before</option>
        <option value="middle">Middle</option>
        <option value="after">After</option>
      </select>
    </form-grid-row>

    <form-grid-row v-if="lineStyle === 'tension'" label="Line smoothing">
      <div class="flex items-center mt-2 gap-4">
        <input
            type="range"
            min="0"
            max="0.5"
            step="0.1"
            :value="dataset.tension ?? 0"
            @input="e => {
              const val = parseFloat(e.target.value);
              dataset.tension = val === 0 ? undefined : val;
            }"
            class="block accent-fuchsia-500 w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md"/>
        <span class="text-sm text-gray-500">{{ dataset.tension }}</span>
      </div>
    </form-grid-row>

    <form-grid-row label="Fill under line?">
      <base-toggle v-model="dataset.lineFill" left-label="Line" label="Area"
                   @update:model-value="checkStacking"/>
    </form-grid-row>
  </form-grid-section>
</template>

<script>
import FormGridSection from '@/components/forms/FormGridSection.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import { mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'

export default {
  name: 'ChartStepDataLine',
  components: {
    FormGridSection,
    FormGridRow,
    BaseToggle
  },
  props: {
    datasetIndex: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      lineStyle: 'normal'
    }
  },
  computed: {
    ...mapState(useChartStore, {
      chartStoreData: 'data'
    }),
    dataset () {
      return this.chartStoreData.datasets[this.datasetIndex]
    }
  },
  mounted () {
    // Set initial lineStyle based on dataset properties
    if (this.dataset.stepped !== undefined) {
      this.lineStyle = 'stepped'
    } else if (this.dataset.tension) {
      this.lineStyle = 'tension'
    } else {
      this.lineStyle = 'normal'
    }
  },
  watch: {
    lineStyle (newStyle) {
      if (newStyle === 'stepped') {
        this.dataset.stepped = this.dataset.stepped ?? 'before'
        delete this.dataset.tension
      } else if (newStyle === 'tension') {
        this.dataset.tension = this.dataset.tension || 0.2
        delete this.dataset.stepped
      } else {
        delete this.dataset.stepped
        delete this.dataset.tension
      }
    }
  },
  methods: {
    checkStacking () {
      this.$emit('check-stacking')
    }
  }
}
</script>
