<template>
  <form-grid-section label="Select the data for your pie chart">
    <form-row-label-column :column-options="columnOptions"/>
    <form-grid-row
        :label="chartStoreData.byRow ? 'Row to chart' : 'Column to chart'"
    >
      <select
          v-model.number="datasetIndex"
          id="datasetRow"
          name="datasetRow"
          class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md"
      >
        <option disabled>(Select)</option>
        <option
            v-for="opt in datasetOptions"
            :value="opt.id"
            :key="opt.id"
        >
          {{ opt.label }}
        </option>
      </select>
    </form-grid-row>

    <form-grid-row>
      <template #label>
        <form-label-datasets
            label="Segments to show"
            @select-all="storeDataset.values = valueOptions.map((opt) => opt.id)"
            @select-none="storeDataset.values = []"/>
      </template>
      <sortable-checkbox-list
          v-model="storeDataset.values"
          :options="valueOptions"
      />
    </form-grid-row>
  </form-grid-section>
</template>
<script>
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormLabelDatasets from '@/components/chart/editor/data/ChartStepDataLabelDatasets.vue'
import { mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import FormRowLabelColumn from '@/components/chart/editor/data/FormRowLabelColumn.vue'
import SortableCheckboxList from '@/components/forms/SortableCheckboxList.vue'

export default {
  name: 'ChartStepDataPie',
  components: {
    SortableCheckboxList,
    FormRowLabelColumn,
    FormGridSection,
    FormLabelDatasets,
    FormGridRow
  },
  props: {
    rowOptions: Array,
    columnOptions: Array
  },
  computed: {
    ...mapState(useChartStore, {
      chartStoreData: 'data'
    }),

    storeDataset () {
      return this.chartStoreData.datasets[0]
    },

    datasetIndex: {
      get () {
        return this.storeDataset.series[0]
      },
      set (value) {
        this.storeDataset.series[0] = value
      }
    },

    filteredColumnOptions () {
      return this.columnOptions.filter((opt) => opt.id !== this.chartStoreData.axis.column)
    },

    datasetOptions () {
      return this.chartStoreData.byRow
             ? this.rowOptions
             : this.filteredColumnOptions
    },

    valueOptions () {
      return this.chartStoreData.byRow
             ? this.filteredColumnOptions
             : this.rowOptions
    },

    dataset () {
      return this.chartStoreData.datasets[0]
    }
  },

  methods: {
    resetOptions () {
      // when we change byRow, reset user selection. We do this rather than on the
      // watcher because we only want to trigger it when user changes, not when val set
      this.datasetIndex = undefined
      this.storeDataset.values = []
    }
  }
}
</script>
