<template>
  <aside class="flex my-4">
    <div class="flex-1 mr-auto">
      <btn-group>
        <base-btn small is-first @click.stop="$emit('edit')" :icon-component="Pencil">Edit</base-btn>
        <base-btn small is-middle @click.stop="$emit('copy')" :icon-component="Copy">Clone</base-btn>
        <base-btn small is-last @click.stop="$emit('delete')" :icon-component="Trash">Delete</base-btn>
      </btn-group>
    </div>
    <div class="flex-none mr-1">
      <btn-group>
        <base-btn small @click.stop="$emit('export')" :icon-component="ArrowRightFromLine">
          Export
        </base-btn>
      </btn-group>
    </div>


  </aside>
</template>

<script>
import BtnGroup from '@/components/buttons/BtnGroup.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { ArrowRightFromLine, Bookmark, Copy, FilePlus, Pencil, Trash } from 'lucide-vue-next'

export default {
  emits: ['edit', 'copy', 'delete', 'export'],
  components: {
    BaseBtn,
    BtnGroup
  },

  data: () => ({
    Bookmark,
    FilePlus,
    Trash,
    Copy,
    Pencil,
    ArrowRightFromLine
  })
}
</script>


<style scoped>

</style>
