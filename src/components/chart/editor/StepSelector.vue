<template>
  <div class="border-b bg-gray-900 border-gray-200 px-4">
    <nav class="-mb-px flex space-x-4" aria-label="Tabs">
      <template v-for="(step, index) in steps">
        <a
            v-if="step.visible"
            :key="step.label"
            @click.prevent="$emit('stepTo', index)"
            :class="[
            index === activeStep
              ? 'border-fuchsia-600 text-fuchsia-600'
              : 'border-transparent text-gray-300 hover:border-gray-300 hover:text-gray-100',
            'whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium cursor-pointer',
          ]"
            :aria-current="index === activeStep ? 'page' : undefined"
        >{{ step.label }}</a
        >
      </template>
    </nav>
  </div>
</template>

<script>

export default {
  emits: ['stepTo'],
  props: {
    steps: Array,
    activeStep: Number
  }
}
</script>

<style scoped></style>
