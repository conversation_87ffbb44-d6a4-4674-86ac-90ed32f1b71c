<template>
  <chart-config-step>
    <template #header>
      Set design options
    </template>

    <template #body>
      <form-grid>
        <form-grid-section title="Chart design" v-if="chartStoreType !== 'scatter'">
          <form-grid-row v-if="chartStoreType === 'pie'" label="Donut cutout">
            <input type="range" list="donutCutoutList" min="0" max="100" step="5"
                   v-model.number.lazy="chartStoreDatasets[0].donutCutout"
                   name="donutCutout"
                   class="block accent-fuchsia-500 w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md"/>
            <datalist id="donutCutoutList">
              <option v-for="i in 10" :key="i" :value="i * 10"></option>
            </datalist>
          </form-grid-row>

          <template v-else>
            <form-grid-row label="Orientation">
              <base-toggle v-model="chartStoreData.horizontal" left-label="Vertical" label="Horizontal"/>
            </form-grid-row>
          </template>

        </form-grid-section>

        <form-grid-section title="Chart Colors">
          <form-grid-row v-if="chartStoreType !== 'scatter'" label="Color system">
            <select
                v-model="colorSystem"
                id="theme"
                class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
              <option disabled>(Choose a color system)</option>
              <option v-for="(cs, key) in colorSystems" :key="key" :value="key">{{ cs.label }}</option>
            </select>
          </form-grid-row>
          <template v-if="colorSystem === 'step'">
            <form-grid-row label="Starting color">
              <transition-group name="colors" tag="div" class="flex flex-wrap mt-2 gap-1">
                <color-box
                    v-for="c in orderedColors"
                    :key="c.index"
                    :color="c.color"
                    :selected="chartStoreColors.startColorIndex === c.index"
                    @click="chartStoreColors.startColorIndex = c.index"
                />
              </transition-group>
            </form-grid-row>
            <form-grid-row label="Order colors by">
              <select
                  v-model="chartStoreColors.order"
                  id="order"
                  class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                <option v-for="(order, index) in colorStepOrders" :key="index" :value="index">{{ order.label }}</option>
              </select>
            </form-grid-row>
          </template>
          <template v-else-if="colorSystem === 'mono'">
            <form-grid-row label="Variation">
              <div class="flex flex-wrap mt-2 gap-1">
                <color-box
                    v-for="c in allColors"
                    :key="c.index"
                    :color="c.color"
                    :selected="chartStoreColors.color === c.index"
                    @click="chartStoreColors.color = c.index"
                />
              </div>
            </form-grid-row>
            <form-grid-row label="Variation">
              <select
                  v-model="chartStoreColors.variation"
                  id="variation"
                  class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                <option v-for="(variation, index) in monoVariations" :key="index" :value="variation.variation">{{
                    variation.label
                  }}
                </option>
              </select>
            </form-grid-row>
          </template>
        </form-grid-section>
        <form-grid-section>
          <form-grid-row v-if="chartStoreType !== 'scatter'" label="Series colors">
            <transition-group name="colors" tag="div" class="mt-2">
              <div v-for="series in coloredItems" :key="`${series.dsIndex}-${series.sIndex}`" class="flex mb-1">
                <color-box :color="getColorObject(series.color) "/>
                <div class="flex-1 ml-2 text-sm">{{ series.label }}</div>
              </div>
            </transition-group>
          </form-grid-row>
        </form-grid-section>
      </form-grid>
    </template>

  </chart-config-step>
</template>

<script>
import { mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'
import ChartConfigStep from '@/components/chart/editor/ChartStep.vue'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import { createColorObject } from '@/chartjs-plugins/plugin.themes.js'
import ColorBox from '@/components/ui/ColorBox.vue'
import { colorStepOrders, colorSystems, getColorMap, monoVariations } from '@/charts/chartColors.js'
import { baseTheme } from '@/chartjs-plugins/baseTheme.js'
import { merge } from 'chart.js/helpers'

export default {
  name: 'ChartStepDesign',
  components: {
    ColorBox,
    BaseToggle,
    FormGridSection,
    FormGridRow,
    FormGrid,
    ChartConfigStep
  },
  data () {
    return {
      colorSystems,
      colorStepOrders,
      monoVariations
    }
  },

  props: {
    rowOptions: Array,
    columnOptions: Array
  },

  computed: {
    ...mapState(useChartStore, {
      chartStoreType: 'chartType',
      chartStoreData: 'data',
      chartStoreLabelColumnId: 'labelColumnId',
      chartStoreColors: store => store.data.design.colors,
      chartStoreDatasets: store => store.data.datasets,
      chartStoreByRow: store => store.data.byRow
    }),

    colorSystem: {
      get () {
        return this.chartStoreType === 'scatter' ? 'mono' : this.chartStoreColors.system
      },
      set (value) {
        this.chartStoreColors.system = value
      }
    },

    coloredItems () {
      return getColorMap(
          this.chartStoreDatasets,
          this.columnOptions,
          this.rowOptions,
          this.chartStoreByRow,
          this.chartStoreLabelColumnId,
          this.chartStoreColors  // N2S: this is the color 'system'; which itself has a 'system' property (mono, step etc) and other attributes
      )
    },

    theme () {
      // Theme takes this chart's theme, and merges with our base. Note that this will exclude modes
      const theme = this.chartStoreData.theme || {}
      merge(theme, baseTheme)
      return theme
    },

    allColors () {
      /*
      Get all colors from the theme.

      Turns each into an object with the original index (for consistent list keys) and
      a color object created from the index and the theme.
       */
      return this.theme.dataColors.borders.map((_, i) => {
        return {
          index: i,
          color: createColorObject(i, this.theme)
        }
      })
    },

    orderedColors () {
      // Return all allColors, but starting at startColorIndex and looping around
      if (this.chartStoreColors.startColorIndex === undefined || this.chartStoreColors.startColorIndex === 0) {
        return this.allColors
      }
      return this.allColors.slice(this.chartStoreColors.startColorIndex).concat(this.allColors.slice(0, this.chartStoreColors.startColorIndex))
    }
  },

  methods: {
    getColorObject (index) {
      return createColorObject(index, this.theme)
    }
  }
}
</script>

<style scoped>

@reference "tailwindcss"

  /* 1. declare transition */
.colors-move {
  transition: all 0.2s cubic-bezier(0.55, 0, 0.1, 1);
}

.custom-range::-webkit-slider-thumb,
.custom-range::-moz-range-thumb {
  -webkit-appearance: none;
  border: 0;
  @apply w-4 h-4 rounded-full bg-fuchsia-500 hover:bg-fuchsia-600;
}

</style>
