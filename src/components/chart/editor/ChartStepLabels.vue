<template>
  <chart-config-step>
    <template #header>
      Set chart titles and labels
    </template>
    <template #body>
      <form-grid>
        <form-grid-section title="Titles">
          <form-grid-row label="Title">
            <input id="title" v-model.lazy="chartStoreTitle" name="title" type="text"
                   class="block w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md">
          </form-grid-row>
          <form-grid-row label="Subtitle">
            <input id="subtitle" v-model.lazy="chartStoreData.subtitle" name="subtitle" type="text"
                   class="block w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md">
          </form-grid-row>
        </form-grid-section>

        <form-grid-section v-if="ds1 && chartStoreType !== 'pie'" title="Put labels on your axes">
          <form-grid-row label="Labels">
            <input id="horizontal-x" v-model.lazy="chartStoreData.axis.label" name="xAxis" type="text"
                   class="block w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md">
          </form-grid-row>
          <form-grid-row v-if="ds1 && ds1.axis.show" label="Values">
            <input id="vertical-y" v-model.lazy="ds1.axis.label" name="yAxis" type="text"
                   class="block w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md">
          </form-grid-row>
          <form-grid-row v-if="ds2 && ds2.axis.show"
                         label="Values on the right">
            <input id="vertical-y2" v-model.lazy="ds2.axis.label" name="yaxis2" type="text"
                   class="block w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md">
          </form-grid-row>
        </form-grid-section>
      </form-grid>
    </template>
  </chart-config-step>
</template>


<script>
import ChartConfigStep from '@/components/chart/editor/ChartStep.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import FormGrid from '@/components/forms/FormGrid.vue'
import { mapState, mapWritableState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'

export default {
  components: {
    FormGrid,
    FormGridSection,
    FormGridRow,
    ChartConfigStep
  },
  computed: {
    ...mapState(useChartStore, {
      chartStoreType: 'chartType'
    }),
    ...mapWritableState(useChartStore, {
      chartStoreData: 'data',
      chartStoreTitle: 'title'
    }),

    ds1 () {
      return this.chartStoreData.datasets[0]
    },
    ds2 () {
      return this.chartStoreData.datasets[1]
    }
  }
}
</script>
<style scoped>

</style>
