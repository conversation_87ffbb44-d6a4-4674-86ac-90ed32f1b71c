<template>
  <chart-config-step>

    <template #header>
      Select the data for your chart
    </template>

    <template #body>
      <form-grid v-if="dataset">
        <form-grid-section>
          <form-grid-row label="Chart type">
            <select
                v-model="dataset.chartType"
                id="chartType"
                @change="checkStacking"
                class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
              <option disabled>(Select)</option>
              <option
                  v-for="type in chartTypes.filter(t => t.id !== 'mixed').filter(t => datasetIndex === 1 ? t.canBeMixed : true)"
                  :selected="type.id === chartType"
                  :value="type.id"
                  :key="type">{{ type.text }}
              </option>
            </select>
          </form-grid-row>
          <form-grid-row v-if="!chartStoreIsMixed && canBeMixed" class="pt-0">
            <base-btn
                class="text-sm"
                is-text
                type="primary"
                @click="createSecondDataset"
            >Add a second chart type?
            </base-btn>
          </form-grid-row>
          <form-grid-row v-else-if="datasetIndex === 1" class="pt-0">
            <base-btn
                class="text-sm"
                is-text
                type="primary"
                @click="deleteSecondDataset"
            >Remove second chart type?
            </base-btn>
          </form-grid-row>
        </form-grid-section>

        <form-grid-section title="Datasets and series">
          <template v-if="chartType === 'pie'">
            <chart-step-data-pie
                :row-options="rowOptions"
                :column-options="columnOptions"
            />
          </template>

          <template v-else>
            <form-row-label-column
                v-if="datasetIndex === 0"
                :column-options="columnOptions"
            />
            <form-row-scatter-select
                v-if="dataset.chartType === 'scatter'"
                :row-options="rowOptions"
                :column-options="columnOptions"
            />
            <form-grid-row v-else>
              <template #label>
                <form-label-datasets
                    label="Data to chart"
                    @select-all="dataset.series = indexOptions.map(opt => opt.id)"
                    @select-none="dataset.series = []"
                />
              </template>
              <sortable-checkbox-list
                  :options="indexOptions"
                  v-model="dataset.series"
              />
            </form-grid-row>

            <form-grid-row v-if="chartStoreData.byRow && datasetIndex === 0">
              <template #label>
                <form-label-datasets
                    label="Select columns to show"
                    @select-all="dataset.values = valueOptions.map(opt => opt.id)"
                    @select-none="dataset.values = []"
                />
              </template>
              <sortable-checkbox-list
                  :options="valueOptions"
                  v-model="dataset.values"
              />
            </form-grid-row>

            <form-grid-row v-else-if="!chartStoreData.byRow && datasetIndex === 0"
                           label="Limit rows?"
                           input-id="min-row">
              <div class="flex space-x-2 p-2">
                <div>
                  <input id="min-row" type="number" v-model.number="displayRowFrom" placeholder="From" min="1"
                         class="block w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                  <input id="max-row" type="number" v-model.number="displayRowTo" placeholder="To" min="1"
                         class="block w-full shadow-xs focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm border-gray-300 rounded-md">
                </div>
              </div>
            </form-grid-row>
          </template>
        </form-grid-section>

        <chart-step-data-line
            v-if="dataset.chartType === 'line'"
            :dataset-index="datasetIndex"
            @check-stacking="checkStacking"
        />


        <form-grid-section v-if="chartType !== 'pie' && chartType !== 'scatter'" title="Chart axis">

          <form-grid-row
              v-if="showStackingOptions"
              label="Stack the dataset?" input-id="stacking-none">
            <div class="text-sm col-span-2 space-y-2 mt-2">
              <div>
                <input type="radio" v-model="dataset.stacking" value="" id="stacking-none"
                       class="h-4 w-4 border-gray-300 text-fuchsia-600 focus:ring-fuchsia-600">
                <label class="font-medium text-gray-700 ml-2" for="stacking-none">No stacking</label>
              </div>
              <div>
                <input type="radio" v-model="dataset.stacking" value="stacked" id="stacking-stacked"
                       class="h-4 w-4 border-gray-300 text-fuchsia-600 focus:ring-fuchsia-600">
                <label class="font-medium text-gray-700 ml-2" for="stacking-stacked">Stacked</label>
              </div>
              <div v-if="!chartStoreIsMixed">
                <input type="radio" v-model="dataset.stacking" value="stacked100" id="stacking-stack100"
                       class="h-4 w-4 border-gray-300 text-fuchsia-600 focus:ring-fuchsia-600">
                <label class="font-medium text-gray-700 ml-2" for="stacking-stack100">Stacked to show
                  proportions</label>
              </div>
            </div>
          </form-grid-row>

          <chart-step-data-axis
              :dataset-index="datasetIndex"
              :axis-limits="axisLimits"
              @check-stacking="checkStacking"
          />
        </form-grid-section>

      </form-grid>
    </template>
  </chart-config-step>

</template>

<script>
import { mapActions, mapState } from 'pinia'
import { useChartStore } from '@/stores/chart.js'

import ChartConfigStep from '@/components/chart/editor/ChartStep.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormLabelDatasets from '@/components/chart/editor/data/ChartStepDataLabelDatasets.vue'
import FormFieldColumns from '@/components/forms/charts/FormFieldColumns.vue'
import ChartStepDataPie from '@/components/chart/editor/data/ChartStepDataPie.vue'
import FormRowLabelColumn from '@/components/chart/editor/data/FormRowLabelColumn.vue'
import { chartTypes, getChartType } from '@/utils/chartTypes.js'
import FormRowScatterSelect from '@/components/chart/editor/data/FormRowScatterSelect.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import { Sortable } from 'sortablejs-vue3'
import { moveInArray } from '@/utils/helpers.js'
import SortableCheckboxList from '@/components/forms/SortableCheckboxList.vue'
import ChartStepDataLine from '@/components/chart/editor/data/ChartStepDataLine.vue'
import ChartStepDataAxis from '@/components/chart/editor/data/ChartStepDataAxis.vue'

export default {
  name: 'ChartStepData',
  emits: ['next', 'previous'],
  components: {
    BaseToggle,
    FormGridSection,
    BaseBtn,
    FormRowScatterSelect,
    FormRowLabelColumn,
    ChartStepDataPie,
    FormFieldColumns,
    FormLabelDatasets,
    FormGrid,
    FormGridRow,
    ChartConfigStep,
    Sortable,
    SortableCheckboxList,
    ChartStepDataLine,
    ChartStepDataAxis
  },
  props: {
    datasetIndex: Number,
    rowOptions: Array,
    columnOptions: Array,
    axisLimits: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      chartTypes,
      sortableOptions: {
        handle: '.drag-handle',
        animation: 150,
        ghostClass: 'sortable-ghost',
        dragClass: 'sortable-drag'
      }
    }
  },

  watch: {
    'dataset.series': {
      handler (newSeries) {
        if (!this.dataset) return
        if (newSeries.length <= 1) {
          this.dataset.stacking = ''
        }
      },
      deep: true
    },
    axisLimits: {
      handler: 'updateAxisValues',
      deep: true
    },

    // when axis.range is changed by the user, update the min/max values
    'dataset.axis.range': {
      handler: 'updateAxisValues',
      deep: true
    },

    // Add watchers to ensure proper validation of row ranges
    'chartStoreData.rows.from': {
      handler (newFrom) {
        if (newFrom === null || newFrom === undefined) return

        // Ensure from is never negative (0-based internal value should be >= 0)
        if (newFrom < 0) {
          this.$nextTick(() => {
            this.chartStoreData.rows.from = 0
          })
          return
        }

        // Ensure to is not smaller than from
        if (this.chartStoreData.rows.to !== null &&
            this.chartStoreData.rows.to !== undefined &&
            this.chartStoreData.rows.to < newFrom) {
          this.$nextTick(() => {
            this.chartStoreData.rows.to = newFrom
          })
        }
      },
      immediate: true
    },

    'chartStoreData.rows.to': {
      handler (newTo) {
        if (newTo === null || newTo === undefined) return

        // Ensure to is never negative (0-based internal value should be >= 0)
        if (newTo < 0) {
          this.$nextTick(() => {
            this.chartStoreData.rows.to = 0
          })
          return
        }

        // Ensure from is not bigger than to
        if (this.chartStoreData.rows.from !== null &&
            this.chartStoreData.rows.from !== undefined &&
            this.chartStoreData.rows.from > newTo) {
          this.$nextTick(() => {
            this.chartStoreData.rows.from = newTo
          })
        }
      },
      immediate: true
    },

    indexOptions (opts) {
      // When indexOptions changes, we make sure anything selected in the dataset is still valid
      if (!this.dataset) return
      this.dataset.series = this.dataset.series.filter(id => opts.some(opt => opt.id === id))
    },

    valueOptions (opts) {
      // When valueOptions changes, we make sure anything selected in the dataset is still valid
      if (!this.dataset) return
      this.dataset.values = this.dataset.values.filter(id => opts.some(opt => opt.id === id))
    }
  },

  computed: {
    ...mapState(useChartStore, {
      chartStoreIsMixed: 'isMixed',
      chartStoreData: 'data'
    }),

    dataset () {
      return this.chartStoreData.datasets[this.datasetIndex]
    },

    // New computed properties for 1-based UI display
    displayRowFrom: {
      get () {
        // Convert from 0-based internal to 1-based display
        return this.chartStoreData.rows.from !== undefined && this.chartStoreData.rows.from !== null
               ? this.chartStoreData.rows.from + 1
               : null
      },
      set (value) {
        if (value === null || value === undefined || value === '') {
          // Allow blank/empty input
          this.chartStoreData.rows.from = undefined
        } else {
          // Ensure minimum value is 1, then convert to 0-based
          const newValue = Math.max(1, parseInt(value) || 1)
          this.chartStoreData.rows.from = newValue - 1
        }
      }
    },

    displayRowTo: {
      get () {
        // Convert from 0-based internal to 1-based display
        return this.chartStoreData.rows.to !== undefined && this.chartStoreData.rows.to !== null
               ? this.chartStoreData.rows.to + 1
               : null
      },
      set (value) {
        if (value === null || value === undefined || value === '') {
          // Allow blank/empty input
          this.chartStoreData.rows.to = undefined
        } else {
          // Ensure minimum value is 1, then convert to 0-based
          const newValue = Math.max(1, parseInt(value) || 1)
          this.chartStoreData.rows.to = newValue - 1
        }
      }
    },

    chartType () {
      return this.dataset.chartType
    },

    chartTypeData () {
      return getChartType(this.chartType)
    },

    canBeMixed () {
      return this.chartTypeData.canBeMixed
    },

    filteredColumnOptions () {
      return this.columnOptions.filter(opt => opt.id !== this.chartStoreData.axis.column)
    },

    indexOptions () {
      return this.chartStoreData.byRow
             ? this.rowOptions
             : this.filteredColumnOptions
    },

    valueOptions () {
      return this.chartStoreData.byRow
             ? this.filteredColumnOptions
             : this.rowOptions
    },

    showStackingOptions () {
      // No stacking on 'mixed' charts that are both line or bar (see also: check stacking)
      if (this.chartStoreIsMixed && this.chartStoreData.datasets[0].chartType === this.chartStoreData.datasets[1].chartType) {
        return false
      }

      // If we're in the second dataset, and we aren't on its own axis - don't show stacking. it will always be
      // whatever the first dataset is one
      if (this.datasetIndex === 1 && !this.dataset.axis.show) {
        return false
      }

      // Stacking if more than one series, and its either bar or line with fill
      return this.dataset.series.length > 1 &&
          (this.dataset.chartType === 'bar' || (
                  this.dataset.chartType === 'line' && this.dataset.lineFill
              )
          )
    },

    checkedItems () {
      return this.indexOptions
          .filter(opt => this.dataset.series.includes(opt.id))
          .sort((a, b) => this.dataset.series.indexOf(a.id) - this.dataset.series.indexOf(b.id))
    },

    uncheckedItems () {
      return this.indexOptions.filter(opt => !this.dataset.series.includes(opt.id))
    }

    // suggestedMin, suggestedMax, sliderMin, sliderMax, stepSize removed as they're now in ChartStepDataAxis
  },

  methods: {
    ...mapActions(useChartStore, {
      chartStoreAddDataSet: 'addDataSet',
      chartStoreRemoveDataSet: 'removeDataSet'
    }),
    createSecondDataset () {
      // We don't allow stacked100 for mixed charts; if we have that, switch to stacked
      if (this.dataset.stacking === 'stacked100') {
        this.dataset.stacking = 'stacked'
      }
      this.chartStoreAddDataSet(this.chartType === 'bar' ? 'line' : 'bar')
      this.$emit('next')
    },

    deleteSecondDataset () {
      this.chartStoreRemoveDataSet(1)
      this.$emit('previous')
    },

    checkStacking () {
      // No stacking for line charts without area fill
      if (!this.showStackingOptions) {
        this.dataset.stacking = ''

        // No stacking for *anything* if we have 2 of the same type of chart in a mixed structure
        if (this.chartStoreIsMixed && this.chartStoreData.datasets[0].chartType === this.chartStoreData.datasets[1].chartType) {
          this.chartStoreData.datasets[0].stacking = ''
          this.chartStoreData.datasets[1].stacking = ''
        }
      }
    },

    updateAxisValues () {
      if (!this.dataset?.axis) return
      if (this.dataset.axis.range === 'custom') {
        // Needs to be on next tick so that the slider min/max/step are updated first before values are set
        this.$nextTick(() => {
          this.dataset.axis.min = this.suggestedMin
          this.dataset.axis.max = this.suggestedMax
        })

      } else {
        delete this.dataset.axis.min
        delete this.dataset.axis.max
      }
    },

    seriesSortUpdate (event) {
      const { oldIndex, newIndex } = event
      const newSeries = [...this.dataset.series]
      moveInArray(newSeries, oldIndex, newIndex)
      this.dataset.series = newSeries
    }
  }
}
</script>

<style scoped>
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-gutter: stable;
}
</style>
