<template>
  <base-dialog
      ref="title"
      title="Set chart title"
      placeholder="Enter chart title"
      @change="changeChart('title', $event)"
  />
  <base-dialog
      ref="subtitle"
      title="Set chart subtitle"
      placeholder="Enter chart subtitle"
      @change="changeChart('subtitle', $event)"
  />
  <base-dialog
      ref="labelAxis"
      title="Set axis label"
      placeholder="Enter chart axis label"
      @change="changeChart('labelAxis', $event)"
  />
  <base-dialog
      ref="labelAxis2"
      title="Set axis label"
      placeholder="Enter chart axis label"
      @change="changeChart('labelAxis2', $event)"
  />
  <base-dialog
      ref="dataAxis"
      title="Set axis label"
      placeholder="Enter chart axis label"
      @change="changeChart('dataAxis', $event)"
  />

  <context-menu ref="menu">
    <menu-item-group>
      <menu-item-button @click="openDialog('title')">Set chart title</menu-item-button>
      <menu-item-button @click="openDialog('subtitle')">Set chart subtitle</menu-item-button>
      <template v-if="chartStoreType !== 'pie'">
        <menu-item-button @click="openDialog('dataAxis')">Set X axis label</menu-item-button>
        <menu-item-button v-if="chartStoreType !== 'mixed'" @click="openDialog('labelAxis')">Set Y axis label
        </menu-item-button>
        <template v-else>
          <menu-item-button @click="openDialog('labelAxis')">Set left Y axis label</menu-item-button>
          <menu-item-button @click="openDialog('labelAxis2')">Set right Y axis label</menu-item-button>
        </template>
      </template>
    </menu-item-group>
  </context-menu>
</template>

<script>
import BaseDialog from '@/components/modal/BaseDialog.vue'
import { mapActions, mapState, mapWritableState } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import { useChartStore } from '@/stores/chart.js'
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'

export default {
  name: 'ChartEditDialogs',
  components: {
    MenuItemButton,
    MenuItemGroup,
    ContextMenu,
    BaseDialog
  },

  computed: {
    ...mapState(useChartStore, {
      chartStoreId: 'id',
      chartStoreType: 'chartType'
    }),
    ...mapWritableState(useChartStore, {
      chartStoreTitle: 'title',
      chartStoreData: 'data'
    })
  },

  methods: {
    ...mapActions(useChartStore, { chartStoreSave: 'save' }),
    ...mapActions(useTableStore, { tableStoreReloadCharts: 'reloadCharts' }),

    dialogToProp (dialog) {
      return {
        title: this.chartStoreTitle,
        subtitle: this.chartStoreData.subtitle,
        labelAxis: this.chartStoreData.datasets[0].axis?.label,
        labelAxis2: this.chartStoreData.datasets[1]?.axis?.label,
        dataAxis: this.chartStoreData.axis?.label
      }[dialog]
    },

    openDialog (dialog) {
      this.$refs[dialog].open(this.dialogToProp(dialog))
    },

    openContextMenu (e) {
      this.$refs.menu.showMenu(e)
    },

    async changeChart (ref, val) {

      if (ref === 'title') {
        this.chartStoreTitle = val

      } else if (ref === 'labelAxis') {
        this.chartStoreData.datasets[0].axis.label = val

      } else if (ref === 'labelAxis2') {
        this.chartStoreData.datasets[1].axis.label = val

      } else if (ref === 'dataAxis') {
        this.chartStoreData.axis.label = val

      } else if (ref === 'subtitle') {
        this.chartStoreData.subtitle = val
      }
      // Save to dbConn only if we are editing (rather than creating new)
      if (this.chartStoreId) {
        await this.chartStoreSave()
        await this.tableStoreReloadCharts()
      }
    }
  }
}
</script>


<style scoped>

</style>
