<template>
  <div>
    <!-- Chart sections -->
    <div v-for="(section, index) in chartSections" :key="index"
         :class="{ 'mb-4': index === 0 && section.charts.length > 0 && chartSections[1].charts.length > 0 }">
      <template v-if="section.charts.length > 0">
        <h3 class="px-4 py-2 text-sm font-medium text-gray-500 bg-gray-50">{{ section.title }}</h3>
        <ul role="list" class="divide-y divide-gray-100">
          <chart-list-item
              v-for="chart in section.charts"
              :key="chart.id"
              :chart="chart"
              :chart-id="chartId"
              @edit="editChart"
              @copy="copyChart"
              @delete="deleteChart"
              @export="exportChart"
          />
        </ul>
      </template>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import { useChartStore } from '@/stores/chart.js'
import ChartListItem from '@/components/chart/ChartListItem.vue'

export default {
  name: 'the-chart-list',
  props: {
    chartId: String
  },
  components: {
    ChartListItem
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreCharts: 'charts'
    }),

    // Get the current view ID from the route
    currentViewId () {
      return this.$route.params.viewId
    },

    // Filter charts that belong to the current view or table
    currentViewOrTableCharts () {
      if (this.currentViewId) {
        // Filter charts that belong to the current view
        return this.tableStoreCharts.filter(chart => chart.view === this.currentViewId)
      } else {
        // Filter charts that don't have a view (belong directly to the table)
        return this.tableStoreCharts.filter(chart => !chart.view)
      }
    },

    // Filter charts that don't belong to the current view or table
    otherCharts () {
      if (this.currentViewId) {
        // Charts that don't belong to the current view
        return this.tableStoreCharts.filter(chart => chart.view !== this.currentViewId)
      } else {
        // Charts that have a view (don't belong directly to the table)
        return this.tableStoreCharts.filter(chart => chart.view)
      }
    },

    // Organize charts into sections
    chartSections () {
      return [
        {
          title: `Charts for current ${this.currentViewId ? 'view' : 'table'}`,
          charts: this.currentViewOrTableCharts
        },
        {
          title: 'Other charts',
          charts: this.otherCharts
        }
      ]
    }
  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreReloadCharts: 'reloadCharts'
    }),
    ...mapActions(useChartStore, {
      chartStoreLoadFromTable: 'loadFromTable',
      chartStoreSaveAsNew: 'saveAsNew',
      chartStoreDelete: 'delete'
    }),

    editChart () {
      this.$router.push({
        name: `app-${this.$route.params.viewId ? 'view' : 'table'}-chart-edit`,
        params: {
          tableId: this.tableStoreId,
          chartId: this.chartId,
          editStep: 0
        }
      })
    },

    async copyChart () {
      await this.chartStoreLoadFromTable(this.chartId)
      const chartId = await this.chartStoreSaveAsNew()
      await this.tableStoreReloadCharts()
      this.$router.push({
        name: `app-${this.$route.params.viewId ? 'view' : 'table'}-chart`,
        params: {
          tableId: this.tableStoreId,
          chartId: chartId
        }
      })
    },

    async deleteChart () {
      await this.chartStoreDelete()
      await this.tableStoreReloadCharts()
      if (this.tableStoreCharts.length) {
        this.$router.push({
          name: `app-${this.$route.params.viewId ? 'view' : 'table'}-charts`,
          params: {
            tableId: this.tableStoreId
          }
        })
      } else {
        this.$router.push({
          name: `app-${this.$route.params.viewId ? 'view' : 'table'}`,
          params: {
            tableId: this.tableStoreId
          }
        })
      }
    },

    exportChart () {
      this.$router.push({
        name: `app-${this.$route.params.viewId ? 'view' : 'table'}-chart-export`,
        params: {
          tableId: this.tableStoreId,
          chartId: this.chartId
        }
      })
    }
  }
}
</script>
