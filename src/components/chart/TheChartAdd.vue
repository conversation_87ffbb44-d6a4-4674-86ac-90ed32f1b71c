<template>
  <ul role="list" class="divide-y divide-gray-100">
    <li
        v-for="type in chartTypesWithPresets"
        :key="type.id"
        @click="chartType = type.id"
        class="relative flex justify-between gap-x-2 px-4 py-4"
        :class="{
        'bg-gray-100': type.id === chartType,
        'cursor-pointer hover:bg-gray-50':  type.id !== chartType
      }">
      <figure class="d-block w-8">
        <component
            :is="type.icon"
            class="size-8 text-gray-900"
        ></component>
      </figure>
      <div class="info w-full ml-4">
        <h3 class="text-sm font-bold" :class="{ 'text-fuchsia-600': type.id === chartType }">{{ type.title }}</h3>
        <p class="text-xs mt-2">{{ type.desc }}</p>
        <transition-fixed-appear-down>
          <fieldset v-if="chartType === type.id" class="overflow-y-hidden p-2">
            <div
                v-for="(p, val) in type.presets(this.selectedSeriesLength)"
                :key="val"
                class="flex items-center px-2 space-2">
              <input
                  :id="val"
                  type="radio"
                  :value="val"
                  v-model="preset"
                  name="preset"
                  class="h-4 w-4 border-gray-300 text-fuchsia-600 focus:ring-fuchsia-600"/>
              <label
                  :for="val"
                  class="ml-3 block text-sm font-medium leading-6 text-gray-900">{{ p.label }}</label>
            </div>
          </fieldset>
        </transition-fixed-appear-down>
      </div>
    </li>
  </ul>
</template>
<script>
import { chartTypes } from '@/utils/chartTypes.js'
import { mapActions, mapState, mapWritableState } from 'pinia'
import { createDataset, useChartStore } from '@/stores/chart.js'
import { useAppStore } from '@/stores/app.js'
import { useTableStore } from '@/stores/table.js'
import TransitionFixedAppearDown from '@/components/transitions/TransitionFixedAppearDown.vue'
import { range } from '@/utils/helpers.js'
import { getChartQuerySpec } from '@/db/query/utils.js'

export default {
  components: { TransitionFixedAppearDown },

  data () {
    return {
      chartTypes,
      preset: null,
      chartType: 'bar'
    }
  },

  mounted: function () {
    this.defineChart()
  },
  watch: {
    /* Every time columns, rows or the chart type changes, we rebuild the whole chart def. */
    appStoreSelectedColumnIds: {
      handler: 'defineChart',
      deep: true
    },
    appStoreSelectedRowIds: {
      handler: 'defineChart',
      deep: true
    },
    appStoreSelectedCells: {
      handler: 'defineChart',
      deep: true
    },
    preset: 'defineChart',
    chartType: 'defineChart'
  },

  computed: {
    ...mapState(useAppStore, {
      appStoreSelectedColumnIds: 'selectedColumnIds',
      appStoreSelectedRowIds: 'selectedRowIds'
    }),
    ...mapState(useAppStore, {
      appStoreSelectedCells: 'selectedCells'
    }),
    ...mapState(useTableStore, {
      tableStoreDbTable: 'dbTable',
      tableStoreLabelColumnId: 'labelColumnId'
    }),
    ...mapWritableState(useChartStore, {
      chartStoreData: 'data',
      chartStoreView: 'view'
    }),

    selectedCellsColumnIndexes () {
      // return an array of all column *indexes* in the selected range
      if (!this.appStoreSelectedCells) return
      const { cols: [start, end] } = this.appStoreSelectedCells
      return range(start, end)
    },

    selectedSeriesLength () {
      if (this.appStoreSelectedCells) {
        const { rows: [from, to] } = this.appStoreSelectedCells
        // If just one row selected, we do "by row" so we return the single row index
        if (to - from === 0) {
          return 1

        } else {
          return this.selectedCellsColumnIndexes.length
        }

      } else if (this.appStoreSelectedRowIds.length) {
        return this.appStoreSelectedRowIds.length

      } else if (this.appStoreSelectedColumnIds.length >= 2) {
        // If we have more than one column selected, filter out the label column
        return this.appStoreSelectedColumnIds.filter(id => id !== this.tableStoreLabelColumnId).length

      } else {
        // But if there's only one, i guess you could still try to render the labels
        return this.appStoreSelectedColumnIds.length
      }
    },

    chartTypesWithPresets () {
      // Get all chart types that have at least one preset given the selected indexes
      if (this.selectedSeriesLength === 0) return []
      return this.chartTypes.filter(type => Object.keys(type.presets(this.selectedSeriesLength)).length)
    },

    viewId () {
      return this.$route.params.viewId
    },

    columns () {
      return this.tableStoreGetViewColumns(this.viewId)
    },

    querySpec () {
      return getChartQuerySpec(useTableStore().columns, useTableStore().views.find(v => v.id === this.viewId))
    }

  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetViewColumns: 'getViewColumns'
    }),

    validatePreset () {
      /*
      Gets a valid preset given the user selection.

      If anything is not valid - wrong chart type, preset doesn't match selected chart type, etc,
      the method will return undefined, and the chart will not be built.
      */

      // If there are no chart types with presets available, return - this means no cols or row selected
      if (this.chartTypesWithPresets.length === 0) {
        return
      }

      // Get the selected chart type; if that can't be found, set to the first available chart type
      const ct = this.chartTypesWithPresets.find(type => type.id === this.chartType)
      if (!ct) {
        this.chartType = this.chartTypesWithPresets[0].id
        return
      }

      // If we don't have a preset, set it to the first chartType.
      // Return, because this change will trigger a new call on this method
      const preset = { ...ct.presets(this.selectedSeriesLength)[this.preset] }
      if (!preset.label) {
        this.preset = Object.keys(ct.presets(this.selectedSeriesLength))[0]
        return
      }

      // Delete the label (as we don't want to store it in the dataset) and return
      delete preset.label
      return preset
    },

    async getChartSeriesAndValues () {
      if (this.appStoreSelectedCells) {
        const { rows: [from, to] } = this.appStoreSelectedCells
        if (to - from === 0) {
          // If only one row selected, default to byRow and flip series/values
          return {
            byRow: true,
            series: await this.tableStoreDbTable.rowIds([from], this.querySpec),
            values: this.selectedCellsColumnIndexes.map(index => this.columns[index].id).filter(id => id !== this.tableStoreLabelColumnId)
          }
        } else {
          return {
            byRow: false,
            series: this.selectedCellsColumnIndexes.map(index => this.columns[index].id),
            values: [],
            rows: { from, to }
          }
        }
      } else if (this.appStoreSelectedColumnIds.length) {
        const rowLimits = await this.tableStoreDbTable.autoRowLimits(this.appStoreSelectedColumnIds, this.querySpec)
        return {
          byRow: false,
          series: this.appStoreSelectedColumnIds.slice(),
          values: [],
          rows: rowLimits
        }
      } else {
        // For all byRow chart types, set values to all columns (ex label col), skipping the label column id
        // For rows, just map the index to the ID
        return {
          byRow: true,
          series: this.appStoreSelectedRowIds.slice(),
          values: this.columns
              .filter(col => col.id !== this.tableStoreLabelColumnId)
              .map(col => col.id)
        }
      }
    },

    async defineChart () {
      const preset = this.validatePreset()
      if (!preset) {
        this.chartStoreData.datasets = []
        return
      }

      let { byRow, series, values, rows } = await this.getChartSeriesAndValues()
      const csd = this.chartStoreData

      this.chartStoreView = this.$route.params.viewId
      csd.byRow = byRow

      csd.horizontal = preset.horizontal

      /* If chart type is pie then get all value ids from database */
      if (this.chartType === 'pie' && !byRow) {
        values = await this.tableStoreDbTable.rowIds(rows, this.querySpec)

      } else {
        // For all other charts, rows can be a from/to limit, or undefined for all rows
        csd.rows = rows || {}
      }

      if (this.chartStoreView) {
        // For auto charts of views, set the axis label column to the first group column; if that doesn't exist
        // (eg filtered view), defer to the table's label column
        csd.axis.column = this.columns.find(c => c.isGroup)?.id
        if (csd.axis.column === undefined) {
          csd.axis.column = this.tableStoreLabelColumnId
        }

      } else if (series.length === 1 && series[0] === this.tableStoreLabelColumnId) {
        // if the only series is the label column, don't set it as the axis column
        csd.axis.column = undefined

      } else if (this.tableStoreLabelColumnId !== undefined) {
        // Set the axis column to the label column
        csd.axis.column = this.tableStoreLabelColumnId
      }

      // remove labelColumnId from series/values if it exists
      if (series.length >= 2) {
        if (!byRow && series.includes(this.tableStoreLabelColumnId)) {
          series.splice(series.indexOf(this.tableStoreLabelColumnId), 1)

        } else if (byRow && values.includes(this.tableStoreLabelColumnId)) {
          values.splice(values.indexOf(this.tableStoreLabelColumnId), 1)
        }
      }

      // Create datasets on our new chart
      if (this.chartType === 'mixed') {
        // ds1 should be the first chart type and takes all props, and all but the last index; ds2 should be just the last index
        let ds1 = { ...createDataset(preset.chartTypes[0], series.slice(0, -1), values), ...preset.props }
        let ds2 = { ...createDataset(preset.chartTypes[1], series.slice(-1), values) }
        csd.datasets = [ds1, ds2]
      } else {
        let ds1 = { ...createDataset(this.chartType, series, values), ...preset }
        csd.datasets = [ds1]
      }

    }
  }

}
</script>
