<template>
  <div class="flex-1 flex min-h-0 leading-normal text-gray-600">
    <transition-fixed-slide-boolean
        :show="showLeftSide"
        slide-from="left"
        class="flex-none shrink-0 w-96 border-r border-b border-gray-100 flex flex-col overflow-y-auto"
    >

      <div class="mt-1 p-1 pb-0 border-b border-gray-100 space-x-0.5">
        <base-tab
            v-for="tab in tabs"
            :key="tab.name"
            :to="tab.to"
            :name="tab.name"
            :is-active="tab.isActive"
            :icon="tab.icon"
        />
      </div>

      <div class="flex-1 overflow-y-auto panel-body">
        <the-chart-list v-if="!addMode" :chart-id="chartStoreId"/>
        <the-chart-add v-else/>
      </div>

      <div class="border-t border-gray-200 text-gray-800" v-if="addMode">
        <div class="flex p-2">
          <btn-group class="flex-1">
            <router-link :to="{ name: 'app-table', params: { tableId: tableStoreId } }">
              <base-btn is-text type="warning">Cancel</base-btn>
            </router-link>
          </btn-group>
          <btn-group class="flex-shrink-0">
            <base-btn :disabled="!chartIsVisible" is-text type="light" @click="customize">Customize</base-btn>
            <base-btn :disabled="!chartIsVisible" @click="addChart" type="primary" is-submit>Save
              Chart
            </base-btn>
          </btn-group>
        </div>
      </div>
    </transition-fixed-slide-boolean>

    <div class="flex-1 flex flex-col overflow-hidden">
      <div v-if="chartIsVisible" class="flex-1 p-4 overflow-hidden">
        <chart-viewer
            @contextmenu.exact.prevent="$emit('chart-context-menu', $event)"
            :chart-id="chartStoreId"
            :always-render="addMode"
            @box-click="$emit('box-click', $event)"
            @cell-click="addMode ? undefined : $emit('cell-click', $event)"
        />
      </div>
      <div v-else class="flex-1 flex flex-col justify-center">
        <p class="mx-auto m-12 text-4xl leading-normal font-light text-gray-300 p-12">
          <template v-if="addMode">
            Select columns, rows or cells in the table above to preview your chart.
          </template>
          <template v-else>
            Select a chart on the left to see a preview here.
          </template>
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import { List, PlusCircle } from 'lucide-vue-next'
import { mapActions, mapState } from 'pinia'
import { useTableStore } from '@/stores/table.js'

import ChartViewer from '@/components/chart/ChartViewer.vue'
import { useChartStore } from '@/stores/chart.js'
import TransitionFixedSlideBoolean from '@/components/transitions/TransitionFixedSlideBoolean.vue'
import TheChartList from '@/components/chart/TheChartList.vue'
import TheChartAdd from '@/components/chart/TheChartAdd.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import BtnGroup from '@/components/buttons/BtnGroup.vue'
import BaseTab from '@/components/ui/tabs/BaseTab.vue'

export default {
  components: {
    BaseTab,
    BtnGroup,
    BaseBtn,
    TheChartAdd,
    TheChartList,
    TransitionFixedSlideBoolean,
    ChartViewer,
    List,
    PlusCircle
  },
  props: {
    showLeftSide: {
      type: Boolean,
      default: true
    },
    addMode: {
      type: Boolean,
      required: true
    }
  },
  emits: ['chart-context-menu', 'box-click', 'cell-click'],

  computed: {
    ...mapState(useTableStore, {
      tableStoreId: 'id'
    }),
    ...mapState(useChartStore, {
      chartStoreId: 'id',
      chartStoreData: 'data'
    }),

    chartIsVisible () {
      return this.chartStoreData.datasets.length > 0
    },

    tabs () {
      const viewId = this.$route.params.viewId
      return [
        {
          name: 'Chart List',
          isActive: !this.addMode,
          to: {
            name: `app-${viewId ? 'view' : 'table'}-charts`,
            params: {
              tableId: this.tableStoreId,
              viewId: viewId
            }
          },
          icon: List
        },
        {
          name: 'Add New Chart',
          isActive: this.addMode,
          to: {
            name: `app-${viewId ? 'view' : 'table'}-chart-add`,
            params: {
              tableId: this.tableStoreId,
              viewId: viewId
            }
          },
          icon: PlusCircle
        }
      ]
    }
  },
  methods: {
    ...mapActions(useTableStore, {
      tableStoreReloadCharts: 'reloadCharts'
    }),
    ...mapActions(useChartStore, {
      chartStoreSave: 'save'
    }),

    async routeToChart ({
      chartId = null,
      edit = false,
      editStep = 0
    }) {
      this.$router.push({
        name: `app-${this.$route.params.viewId ? 'view' : 'table'}-chart${edit ? '-edit' : ''}`,
        params: {
          tableId: this.tableStoreId,
          chartId: chartId || this.chartStoreId,
          editStep: edit ? editStep : null
        }
      })
    },

    async addChart () {
      const chartId = await this.chartStoreSave()
      await this.tableStoreReloadCharts()
      await this.routeToChart({ chartId })
    },

    async customize () {
      const chartId = await this.chartStoreSave()
      await this.tableStoreReloadCharts()
      await this.routeToChart({
        chartId,
        edit: true
      })
    }

  }
}
</script>


<style scoped>

.panel-body {
  scrollbar-width: thin;
}

</style>
