<template>
  <li
      class="relative flex flex-col px-4 py-4"
      :data-chart-id="chart.id"
      :class="{
      'bg-gray-100': chart.id === chartId,
      'hover:bg-gray-50':  chart.id !== chartId
    }">
    <div class="flex-1 flex justify-between gap-x-6">
      <div class="flex items-center min-w-0 gap-x-4">
        <figure class="flex-none size-6">
          <component
              :is="datasetsToChartType(chart.data.datasets)?.icon"
              class="text-gray-900 w-full h-full"
          ></component>
        </figure>
        <div class="min-w-0 flex-auto">
          <p class="text-sm font-semibold leading-6 text-gray-600">
            <router-link :to="chartLink(chart.id)">
              <span class="absolute inset-x-0 -top-px bottom-0"></span>
              {{ chart.title.trim().length ? chart.title : 'Untitled chart' }}
            </router-link>
          </p>
          <p class="text-xs leading-5 text-gray-500">
            Last updated
            <time :datetime="chart.date_last_updated">{{ timeSince(chart.date_last_updated) }}</time>
          </p>
        </div>
      </div>
      <div class="flex shrink-0 items-center gap-x-4">
        <ChevronRight
            class="h-5 w-5 flex-none text-gray-400 transition-transform duration-200"
            :class="{
              'rotate-90': chart.id === chartId,
            }"
            aria-hidden="true"/>
      </div>
    </div>
    <transition-fixed-appear-down @after-enter="scrollIntoView">
      <div class="flex-none min-h-0 overflow-y-hidden" v-if="chart.id === chartId">
        <chart-buttons
            @edit="$emit('edit')"
            @copy="$emit('copy')"
            @delete="$emit('delete')"
            @export="$emit('export')"
        />
      </div>
    </transition-fixed-appear-down>
  </li>
</template>

<script>
import { ChevronRight } from 'lucide-vue-next'
import ChartButtons from '@/components/chart/editor/ChartButtons.vue'
import TransitionFixedAppearDown from '@/components/transitions/TransitionFixedAppearDown.vue'
import { timeSince } from '@/utils/dates.js'
import { datasetsToChartType } from '@/utils/chartTypes.js'
import { useTableStore } from '@/stores/table.js'
import { mapState } from 'pinia'

export default {
  name: 'chart-list-item',
  components: {
    TransitionFixedAppearDown,
    ChartButtons,
    ChevronRight
  },
  props: {
    chart: {
      type: Object,
      required: true
    },
    chartId: {
      type: String,
      required: false
    }
  },
  emits: ['edit', 'copy', 'delete', 'export'],

  computed: {
    ...mapState(useTableStore, {
      tableStoreId: 'id'
    })
  },
  mounted: function () {
    this.scrollIntoView()
  },

  methods: {
    datasetsToChartType,
    timeSince,

    scrollIntoView () {
      if (this.chartId !== this.chart.id) return
      this.$el.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      })
    },

    chartLink (chartId) {
      if (chartId === this.chartId) {
        // deselect - go back to charts view
        return {
          name: `app-${this.$route.params.viewId ? 'view' : 'table'}-charts`,
          params: {
            tableId: this.tableStoreId,
            viewId: this.$route.params.viewId
          }
        }
      } else {
        const chartViewId = this.chart.view
        return {
          name: `app-${chartViewId ? 'view' : 'table'}-chart`,
          params: {
            tableId: this.tableStoreId,
            viewId: chartViewId,
            chartId: chartId
          }
        }
      }
    }
  }
}
</script>
