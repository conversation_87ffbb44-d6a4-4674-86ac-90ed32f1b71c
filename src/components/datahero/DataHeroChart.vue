<template>
  <component
      @click="chartClick"
      @mousemove="chartMouseOver"
      @mouseout="chartMouseOut"
      ref="chart"
      :is="component"
      :data="data"
      :options="moddedOptions"
      :plugins="plugins"
  />
</template>


<script>
/*
DataHeroChart: Renders a chart.js chart (bar, line, pie, scatter) with additional plugins.

Does not use or refer to any store; it relies purely on props and emits events to parent components.

requires:
- type: the type of chart to render (bar, line, pie, scatter)
- data: the data object to render
- options: the options object to render

optional:
- createImage: whether to include the createImage plugin
- noEvents: whether to disable all events and animation
- showNoTitle: whether to show a prompt to set a title if there's no title
- legendInteractions: whether to enable legend interactions
- bgColor: the background color to use (default is transparent)

emits:
- point-click: when a point is clicked
- box-click: when a box (title, legend, axis, etc) is clicked
- image-data: when an image is created
 */

import { Bar, Line, Pie, Scatter } from 'vue-chartjs'

import { Chart, registerables } from 'chart.js'
import 'chartjs-adapter-date-fns'
import ChartjsPluginStacked100 from 'chartjs-plugin-stacked100'
import { chartThemePlugin } from '@/chartjs-plugins/plugin.themes.js'
import { mouseoverPlugin } from '@/chartjs-plugins/plugin.mouseover.js'
import { noTitlePlugin } from '@/chartjs-plugins/plugin.notitle.js'
import { createImagePlugin } from '@/chartjs-plugins/plugin.createImage.js'
import { legendClickPlugin } from '@/chartjs-plugins/plugin.legendClick.js'
import { merge } from 'chart.js/helpers'

Chart.register(...registerables)

export default {
  name: 'DataHeroChart',
  emits: ['point-click', 'box-click', 'image-data'],
  props: {
    type: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      required: true
    },
    createImage: {
      type: Boolean,
      required: false
    },
    noEvents: {
      type: Boolean,
      default: false
    },
    showNoTitle: {
      type: Boolean,
      default: false
    },
    legendInteractions: {
      type: Boolean,
      default: false
    },
    bgColor: {
      type: Number,
      default: -1  // transparent
    }
  },
  data: () => ({
    mouseOverBox: undefined,
    plugins: [
      ChartjsPluginStacked100,
      chartThemePlugin,
      noTitlePlugin,
      mouseoverPlugin,
      createImagePlugin,
      legendClickPlugin
    ]
  }),
  computed: {
    component () {
      return {
        line: Line,
        pie: Pie,
        scatter: Scatter
      }[this.type] || Bar
    },

    activeChart () {
      return this.$refs.chart.chart
    },

    moddedOptions () {
      const opts = { ...this.options }
      const mods = {
        animation: this.noEvents ? false : {},
        plugins: {
          mouseover: this.noEvents ? false : {
            box: this.mouseOverBox
          },
          noTitle: this.showNoTitle,
          createImage: !this.createImage ? false : {
            onImageData: (imageData) => {
              this.$emit('image-data', imageData)
            }
          }
        }
      }
      if (this.bgColor !== undefined) {
        mods.plugins.themes = {
          theme: {
            backgroundColors: {
              canvas: this.bgColor === -1 ? undefined : this.bgColor,
              scales: this.bgColor === -1 ? undefined : this.bgColor
            }
          }
        }
      }
      merge(opts, mods)
      return opts
    }
  },

  methods: {
    getBoxAtEvent (e) {
      const isInBox = function (e, box) {
        return e.offsetX >= box.left && e.offsetX <= box.right && e.offsetY >= box.top && e.offsetY <= box.bottom
      }
      const horiz = this.options.indexAxis === 'y'
      const scales = this.activeChart.scales
      // noinspection JSUnresolvedReference  - titleBlock not recognized
      const boxes = {
        title: this.activeChart.titleBlock,
        legend: this.activeChart.legend,
        dataAxis: horiz ? scales.y : scales.x,
        labelAxis: horiz ? scales.x : scales.y,
        labelAxis2: horiz ? scales.x2 : scales.y2
      }
      for (const [name, box] of Object.entries(boxes)) {
        if (box !== undefined && isInBox(e, box)) {
          return name
        }
      }

      // still here? check the array of boxes and see what matches
      for (const box of this.activeChart.boxes) {
        if (isInBox(e, box)) {
          // if it's on top we assume its subtitle - this might detect some false negatives but
          // there doesn't seem to be a more robust way of identifying the subtitle
          if (box.position === 'top') {
            return 'subtitle'
          }
        }
      }
    },

    chartClick (e) {
      if (this.noEvents) return
      const points = this.activeChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, true)
      if (points.length > 0) {
        this.$emit('point-click', points[0])
        return
      }
      const box = this.getBoxAtEvent(e)
      if (box) {
        this.$emit('box-click', box)
      }
    },

    chartMouseOver (e) {
      if (this.noEvents) return
      this.mouseOverBox = this.getBoxAtEvent(e)
      if (this.mouseOverBox) {
        e.target.style.cursor = 'pointer'
      } else {
        e.target.style.cursor = 'default'
      }
    },

    chartMouseOut (e) {
      if (this.noEvents) return
      this.mouseOverBox = undefined
      e.target.style.cursor = 'default'
    }

  }
}
</script>


<style scoped>

</style>
