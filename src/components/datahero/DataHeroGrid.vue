<template>
  <div
      class="overflow-auto flex-1 h-full"
      :class="{
        'has-header': false,
        'charted-cells': chartColumns?.length,
        'range-selection': this.rangeSelected
      }"
      @touchstart="touchStart"
      @touchmove="touchMove"
      @touchend="touchEnd"
      @keydown="keyDown">
    <ag-grid-vue
        class="h-full w-full ag-theme-alpine"
        :default-col-def="defaultColDef"
        :get-row-id="getRowId"
        :animate-rows="false"
        :value-cache="true"

        row-selection="multiple"

        :row-model-type="dataSource === undefined ? 'clientSide' : 'infinite'"
        :row-drag-managed="dataSource === undefined"

        :row-height="35"
        :header-height="35"
        :suppress-row-click-selection="true"

        :row-drag-multi-row="true"
        :row-class-rules="rowClassRules"

        :suppress-row-drag="!interactive"
        :suppress-drag-leave-hides-columns="true"

        :undo-redo-cell-editing="true"
        :undo-redo-cell-editing-limit="100"

        @grid-ready="gridReady"
        @grid-pre-destroyed="gridDestroyed"
        :tab-to-next-cell="tabToNextCell"

        @contextmenu.exact="showContextMenu"
        @selection-changed="selectionChanged"


        @row-drag-enter="rowDragEnter"
        @row-drag-move="rowDragMove"
        @row-drag-end="rowDragEnd"
        @row-drag-leave="rowDragLeave"

        @cell-value-changed="cellValueChanged"
        @cell-mouse-down="cellMouseDown"
        @cell-mouse-over="cellMouseOver"
        @cell-mouse-out="cellMouseOut"
        @cell-editing-started="cellEditingStarted"
        @cell-key-down="cellKeyDown"

        @column-pinned="$emit('column-pinned', $event)"
        @column-moved="$emit('column-moved', $event)"
        @column-resized="$emit('column-resized', $event)"

    />
  </div>
</template>

<script>
/*
DataHeroGrid - a wrapper around ag-grid. Used for both viewing and editing.

This is independent of any stores - you can just connect rows and columns to
props and it will render the table. It handles highlighting of rows, columns and
ranges, and emits a lot of events for parent components to handle.

Rather than use props, various methods can be called programatically to set the
rows and columns, or to insert, update, or delete rows. This uses ag-grid transactions
where appropriate.
*/

import 'ag-grid-community/styles/ag-grid.css' // Core grid CSS, always needed
import 'ag-grid-community/styles/ag-theme-alpine.css' // Optional theme CSS
import { AgGridVue } from 'ag-grid-vue3'
import TableEditorHeader from '@/components/table/TableEditorHeader.vue'
import equal from 'fast-deep-equal'
import { colIsAuto, colToColDef, rowDragDef, rowPositionDef } from '@/utils/grid/columns.js'

export default {
  name: 'DataHeroGrid',
  emits: [
    'grid-ready',
    'update:selectedColumnIds',
    'update:selectedRowIds',
    'update:selectedCells',
    'column-pinned',
    'column-moved',
    'column-resized',
    'column-right-click',
    'rows-moved',
    'delete-selection',
    'filter-column',
    'cell-changed',
    'contextmenu',
    'cell-value-changed',
    'cell-mouse-down',
    'insert-and-edit',
    'insert-and-focus',
    'edit-down'
  ],
  components: { AgGridVue },

  props: {
    interactive: {
      type: Boolean,
      default: false
    },

    showFilters: {
      type: Boolean,
      default: false
    },

    showRowPositionColumn: {
      type: Boolean,
      default: true
    },

    // For infinite grid, pass in the data source interface -
    // https://www.ag-grid.com/vue-data-grid/infinite-scrolling/
    dataSource: {
      type: Object,
      default: undefined
    },

    labelColumnId: {
      type: Number,
      required: false
    },
    selectedColumnIds: {
      type: Array,
      required: false,
      default: () => []
    },
    selectedRowIds: {
      type: Array,
      required: false,
      default: () => []
    },
    selectedCells: {
      type: Object,
      required: false,
      default: () => {}
    },

    columnSelection: {
      type: Boolean,
      required: false,
      default: false
    },

    rowSelection: {
      type: Boolean,
      required: false,
      default: false
    },

    rangeSelection: {
      type: Boolean,
      required: false,
      default: false
    },

    toggleRows: {
      type: Boolean,
      required: false,
      default: false
    },

    // one way of using this is passing in props for rows and columns; whenever they change, we rebuild the grid
    // however this is slow; so we don't actually use this in TableEditor
    columns: {
      type: Array,
      required: false
    },

    rows: {
      type: Array,
      required: false
    },

    // Column ids currently being charted
    chartColumns: {
      type: Array,
      required: false,
      default: () => []
    },

    // Either an array of row indexes, or an object with 'from' and 'to' keys
    chartRows: {
      type: [Array, Object],
      required: false
    }
  },

  watch: {
    labelColumnId: 'rerender',

    selectedColumnIds: {
      handler: 'rerender'
    },

    selectedCells: {
      handler: 'rerender',
      deep: true
    },
    selectedRowIds: {
      // This can't be immediate because grid wont be ready on first load; hence the call on gridReady too
      handler: function (rowIds, oldRowIds) {
        if (!equal(rowIds, oldRowIds)) {
          this.selectGridRows(rowIds)
        }
      }
    },

    hoverColumnId: 'rerender',
    chartColumns: 'rerender',
    chartRows: {
      handler: 'rerender',
      deep: true
    },
    columns: 'setColumns',
    rows: 'setRowData',

    dataSource: {
      handler () {
        this.gridApi.setGridOption('datasource', this.dataSource)
      },
      deep: true
    }
  },

  data () {
    return {
      // when selecting multiple rows, this is the first row selected
      startRowIndex: undefined,
      colDefs: null,
      colDefPrefixes: null,

      // { start: [], end: [] -  tuple of col index/row index. NB: index, not Id!
      selectedRange: null,
      rowDragging: false,
      rowDragOverIndex: null,

      // for touch events, we flag if we are touch-moving; this helps us disambiguate scrolling from 'clicking'
      touchMoving: false,
      hoverColumnId: null,

      // Row styles for insertion; we do 'below' insert by default as that already has the border - apply this if
      // we are one below the 'drag over' index or we are bottom row with a -1; only do top row if inserting at top
      rowClassRules: {
        'row-insert-above': params => this.rowDragging && this.rowDragOverIndex === 0 && params.node.rowIndex === 0,
        'row-insert-below': params => this.rowDragging && (this.rowDragOverIndex === params.node.rowIndex + 1 ||
            (this.rowDragOverIndex === -1 && params.node.rowIndex === this.gridApi.getDisplayedRowCount() - 1)
        )
      },

      defaultColDef: {
        resizable: true,
        editable: this.interactive,
        suppressKeyboardEvent: (params) => {
          return params.event.key === 'Delete'
        },
        headerComponent: TableEditorHeader,
        headerComponentParams: {
          showColumnType: this.interactive,
          showFilter: this.interactive,
          hookHover: this.columnHover,
          hookLeftClick: this.columnLeftClick,
          hookRightClick: this.columnRightClick,
          hookFilter: (e, colId) => {
            this.$emit('filter-column', e, colId)
          },
          hookDelete: (e, colId) => {
            this.$emit('delete-selection', { colId })
          }
        },

        cellClassRules: {
          selected: params => this.columnSelection && !params.node.rowPinned && this.selectedColumnIds.includes(params.colDef.colId),
          labeled: params => params.colDef.colId === this.labelColumnId,
          hovered: params => this.columnSelection && !params.node.rowPinned && !this.selectedCells && this.hoverColumnId === params.colDef.colId,
          ranged: this.rangeSelection && this.isCellInRange,
          charted: this.isCellCharted,
          locked: this.isCellLocked
        }
      }
    }
  },

  computed: {

    rangeSelected () {
      return this.rangeSelection && this.selectedRange && this.selectedRange.end !== null
    }
  },

  methods: {

    /* Public methods - these can be called by parents */
    load (columns, rows) {
      this.hoverColumnId = null
      this.setColumns(columns)
      this.setRowData(rows)
    },

    setRowData (rows) {
      if (this.gridApi) {
        this.gridApi.setGridOption('rowData', rows)
        this.refetch()
      }
    },

    insertRows (rows, insertIndex) {
      this.gridApi.applyTransaction({ add: rows, addIndex: insertIndex })
      this.refetch()
    },

    insertRow (row, insertIndex) {
      this.insertRows([row], insertIndex)
    },

    updateRows (rows) {
      this.gridApi.applyTransaction({ update: rows })
      this.refetch()
    },

    deleteRows (rowIndexes) {
      const rows = rowIndexes.map(rowIndex => this.gridApi.getDisplayedRowAtIndex(rowIndex).data)
      this.gridApi.applyTransaction({ remove: rows })
      this.refetch()
    },

    rerender () {
      // This re-renders the grid with the gridApi refreshCells method - doing things like
      // redoing cellClassRules and other things that might have changed. It does not re-poll
      // the data source.
      if (this.gridApi) {
        this.gridApi.refreshCells()
      }
    },

    async refetch () {
      // This actually re-polls the database and recalculates the row count
      if (this.gridApi) {
        if (this.dataSource) {
          // Create a promise that resolves when the data refresh is complete
          return new Promise(resolve => {
            const onCacheUpdated = () => {
              this.gridApi.removeEventListener('modelUpdated', onCacheUpdated)
              if (this.showRowPositionColumn) {
                this.gridApi.refreshCells({ columns: ['pos'], force: true })
              }
              resolve()
            }

            this.gridApi.addEventListener('modelUpdated', onCacheUpdated)
            this.gridApi.refreshInfiniteCache()
          })
        } else {
          this.rerender()
          return Promise.resolve()
        }
      }
      return Promise.resolve()
    },

    getRowFromIndex (index) {
      return this.gridApi.getDisplayedRowAtIndex(index).data
    },

    forEachNode (callback) {
      this.gridApi.forEachNode(callback)
    },

    selectRow (id, clearOthers = false) {
      const rowNode = this.gridApi?.getRowNode(id.toString())
      if (rowNode) {
        rowNode.setSelected(true, clearOthers)
      }
    },

    getFocusedCell () {
      // Get a cell with the properties of colId, rowIndex, and rowPinned
      const cell = this.gridApi.getFocusedCell()
      return cell ? {
        rowId: this.gridApi.getDisplayedRowAtIndex(cell.rowIndex).data.id,
        colId: cell.column.colDef.colId,
        rowIndex: cell.rowIndex,
        rowPinned: cell.rowPinned
      } : undefined
    },

    setFocusedCell (rowIndex, colId, startEdit = false) {
      const column = this.gridApi.getColumn(String(colId))
      this.gridApi.ensureColumnVisible(column)
      if (rowIndex !== undefined) {
        this.gridApi.ensureIndexVisible(rowIndex)
        this.gridApi.setFocusedCell(rowIndex, column)
        if (startEdit) {
          this.gridApi.startEditingCell({ rowIndex, colKey: column })
        }
      }
    },

    refreshHeader () {
      // Refresh header triggers 'params' to change on the header component, TableEditorHeader
      this.gridApi.refreshHeader()
    },

    setColumns (columns) {
      this.colDefPrefixes = []
      if (this.interactive) {
        this.colDefPrefixes.push(rowDragDef)
      }
      if (this.showRowPositionColumn) {
        this.colDefPrefixes.push(rowPositionDef)
      }
      this.colDefs = columns.map((col, index) => colToColDef(col, index, this.interactive, !this.showFilters))

      // Handle ag-grid bug, whereby if you don't wipe first, then it tries to stop duplicate ids, replacing
      // colId=0 to colId=0_1 etc. This is a workaround - clearing first stops this dupe check.
      this.gridApi.setGridOption('columnDefs', [])
      this.gridApi.setGridOption('columnDefs', [...this.colDefPrefixes, ...this.colDefs])
    },

    pinColumn (colId, pinned) {
      // pinned can be one of: left, right, null
      colId = String(colId)
      this.gridApi.applyColumnState({
        state: [{ colId, pinned }]
      })
    },

    setAggregateRow (totals) {
      // This shouldn't be necessary, but it is - if we don't blank it first, it doesn't update, even with refresh
      this.gridApi.setGridOption('pinnedBottomRowData', [])
      if (Object.keys(totals).length) {
        this.gridApi.setGridOption('pinnedBottomRowData', [totals])
      }
    },

    /* Selection related methods -------------------------------------*/
    cellValueChanged (params) {
      const { colDef: { colId }, rowIndex, newValue, oldValue, node: { data } } = params
      this.$emit('cell-value-changed', { colId, rowIndex, newValue, oldValue, rowData: data })
    },

    clearSelection (...args) {
      /* Clears the specified selections in the table editor. */
      if (args.includes('columns') && this.selectedColumnIds.length) {
        this.$emit('update:selectedColumnIds', [])
      }
      if (args.includes('rows') && this.selectedRowIds.length) {
        this.gridApi.deselectAll()
      }
      if (args.includes('range') && this.selectedRange) {
        this.selectedRange = null
        this.$emit('update:selectedCells', null)
      }
    },

    rangeToCells () {
      if (!this.rangeSelected) return null
      return {
        cols: [this.selectedRange.start[0], this.selectedRange.end[0]].sort((a, b) => a - b),
        rows: [this.selectedRange.start[1], this.selectedRange.end[1]].sort((a, b) => a - b)
      }
    },

    selectAll () {
      if (this.columnSelection) {
        // Preferred is select all columns (rather than all rows) so that copy includes the headers
        this.clearSelection('rows', 'range') // clears all row selection
        this.$emit('update:selectedColumnIds', this.colDefs.map(c => c.colId))

      } else if (this.rowSelection) {
        this.gridApi.selectAll()

      } else if (this.rangeSelection) {
        this.selectedRange = {
          start: [0, 0],
          end: [this.colDefs.length - 1, this.gridApi.getDisplayedRowCount() - 1]
        }
        this.$emit('update:selectedCells', this.rangeToCells())
      }
    },

    selectRowsBetween (startIndex, endIndex) {
      // Select all rows between start and end, inclusive
      for (let i = Math.min(startIndex, endIndex); i <= Math.max(startIndex, endIndex); i++) {
        this.selectRow(this.gridApi.getDisplayedRowAtIndex(i).data.id)
      }
    },

    /* Cell events ------------------------------------ */
    selectRows (node, rowIndex, shift, ctrl) {
      this.clearSelection('columns', 'range')
      if (shift) {
        // remove all selection, then select everything from the startRowIndex to this one, inclusive
        this.clearSelection('rows')
        this.selectRowsBetween(this.startRowIndex, rowIndex)
      } else {
        // If ctrl is not held down, deselect all other rows
        if (!(ctrl || this.toggleRows)) {
          this.clearSelection('rows')
        }
        if (node.isSelected()) {
          node.setSelected(false)
          this.startRowIndex = undefined
        } else {
          // starting a fresh selection - mark it
          node.setSelected(true)
          this.startRowIndex = rowIndex
        }
      }
    },

    changeSelection (colId, rowIndex, shiftKey = false, ctrlKey = false) {
      /*
      Handles a selection change given a click or touch event on the given cell.
      */
      const node = this.gridApi.getDisplayedRowAtIndex(rowIndex)
      if (node.rowPinned) {
        // If this is the totals row, just clear all selection
        this.clearSelection('columns', 'range', 'rows')
        this.startRowIndex = undefined

      } else if (colId === 'rowDrag' && this.rowSelection && this.selectedRowIds.includes(node.data.id)) {
        // If we're clicking on a selected row in row drag, do nothing; it's the start of a drag

      } else if (this.rowSelection && colIsAuto(colId)) {
        // If we're in the row count column, select the row
        this.selectRows(node, rowIndex, shiftKey, ctrlKey)

      } else if (this.rangeSelection && !colIsAuto(colId)) {
        this.clearSelection('columns', 'rows')
        if (this.selectedRange && shiftKey) {
          // shift-click to create a range between cells
          this.setSelectionRangeEnd(this.colDefs.findIndex(c => c.colId === colId), rowIndex)
        } else {
          // start a new range selection
          this.selectedRange = {
            start: [this.colDefs.findIndex(c => c.colId === colId), rowIndex],
            end: null
          }
          this.$emit('update:selectedCells', null)
        }

      } else {
        this.clearSelection('columns', 'rows', 'range')
      }
      this.$emit('cell-mouse-down', { rowIndex, colId })
    },

    cellMouseDown (params) {
      const { rowPinned, event: { button, shiftKey, ctrlKey }, colDef: { colId }, rowIndex } = params
      if (rowPinned) return
      if (button === 0) {
        this.changeSelection(colId, rowIndex, shiftKey, ctrlKey)
      }
    },

    cellMouseOver (params) {
      // If the mouse button is held down in the event, we might be extending a row or cell selection
      if (this.rowDragging) return
      const { event, rowIndex, node: { rowPinned }, colDef: { colId } } = params
      if (event.buttons === 1 && !rowPinned) {
        if (this.rowSelection && this.selectedRowIds.length) {
          this.selectRowsBetween(this.startRowIndex, rowIndex)
          this.clearSelection('range')

        } else if (this.rangeSelection && this.selectedRange && !colIsAuto(colId)) {
          this.setSelectionRangeEnd(this.colDefs.findIndex(c => c.colId === colId), rowIndex)
        }
      }
    },

    cellMouseOut (params) {
      // if we're in pos and mouse is down when exiting the cell, and we're not selected, select it
      if (this.rowDragging) return
      const { node, event, column } = params
      if (this.rowSelection && event.buttons === 1 && column.colId === 'pos' && !this.selectedRange) {
        if (!node.isSelected()) {
          node.setSelected(true)
        }
      }
    },

    cellEditingStarted () {
      if (!this.interactive) return
      this.clearSelection('columns', 'rows', 'range')
    },

    setSelectionRangeEnd (colPos, rowIndex) {
      // Sets the end selection, but if it equals the start then clera it
      if (colPos < 0) {
        colPos = 0
      }
      const end = [colPos, rowIndex]
      this.gridApi.setFocusedCell(rowIndex, this.colDefs[colPos].colId)
      if (equal(this.selectedRange.start, end)) {
        this.selectedRange.end = null
        this.$emit('update:selectedCells', null)

      } else if (!equal(this.selectedRange.end, end)) {
        this.selectedRange.end = end
        this.$emit('update:selectedCells', this.rangeToCells())
      }
    },

    async cellKeyDown (params) {
      const {
        node,
        rowIndex,
        event: { key, shiftKey, ctrlKey, altKey, metaKey },
        colDef: { colId }
      } = params

      if (this.isCellEditing()) {
        return
      }

      if (this.interactive && key === 'Delete') {
        // Note that the automatic behavior to delete the selected cell is disabled on defaultColDef.suppressKeyboardEvent
        this.$emit('delete-selection', { colId, rowIndex, rowData: node.data })

      } else if (this.rangeSelection && ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
        // if shift is held down, and we are going up/down/left/right, start or continue a range selection

        if (shiftKey) {
          this.clearSelection('rows', 'columns')

          // cut out the 'Arrow' part of the key
          const direction = key.slice(5).toLowerCase()

          // work out what cell we are moving to
          const prevCell = [this.colDefs.findIndex(c => c.colId === colId), rowIndex]
          const nextCell = this.getNextCell(prevCell, direction)
          // Is the nextCell valid?
          if (nextCell) {
            if (!this.rangeSelected || !equal(prevCell, this.selectedRange.end)) {
              // if we haven't started the range yet, or we aren't extending the range, reset the start cell
              this.selectedRange = {
                start: prevCell,
                end: null
              }
            }
            this.setSelectionRangeEnd(...nextCell)
          } else {
            // if we're at the edge of the grid, clear the range
            this.clearSelection('range')
          }
        }

      } else if (key.length === 1 && !ctrlKey && !altKey && !metaKey && !shiftKey) {
        // Ensure the event.key is a single character (letter, number, symbol)
        this.clearSelection('rows', 'columns', 'range')
      }
    },

    getNextCell (prevCell, direction) {
      // Given a cell and a direction (up, down, left, right), return the cell we're moving to.
      const nextCell = [...prevCell]
      switch (direction) {
        case 'up':
          nextCell[1]--
          break
        case 'down':
          nextCell[1]++
          break
        case 'left':
          nextCell[0]--
          break
        case 'right':
          nextCell[0]++
          break
      }

      // check all dimensions are within range; return undefined if either are out
      if (nextCell[0] < 0 || nextCell[0] >= this.colDefs.length) {
        return undefined
      }
      if (nextCell[1] < 0 || nextCell[1] >= this.gridApi.getDisplayedRowCount()) {
        return undefined
      }
      if (nextCell[0] < 0 ||
          nextCell[0] >= this.colDefs.length ||
          nextCell[1] < 0 || nextCell[1] >= this.gridApi.getDisplayedRowCount()) {
        return
      }
      return nextCell
    },

    /* Other private methods ---------------------------------------------*/
    gridReady (params) {
      this.gridApi = params.api
      if (this.columns?.length && this.rows?.length) {
        this.load(this.columns, this.rows)
      }

      if (this.columns) {
        this.setColumns(this.columns)
      }
      if (this.rows) {
        this.setRowData(this.rows)
      }

      if (this.selectedRowIds) {
        this.selectGridRows(this.selectedRowIds)
      }
      this.$emit('grid-ready', params)
    },

    getRowId (params) {
      // This check is needed otherwise it will throw an error when the grid is destroyed/recreated
      if (this.gridApi) {
        return String(params.data.id)
      }
    },

    gridDestroyed () {
      this.gridApi = null
    },

    selectGridRows (rowIds) {
      if (this.rowSelection) {
        this.gridApi.deselectAll()
        rowIds.forEach(id => this.selectRow(id, false))
        this.rerender()
      }
    },

    rowDragEnter () {
      // Flag start of row drag, but don't enable if row selection not allowed
      if (!this.rowSelection) return
      this.rowDragging = true
    },

    rowDragMove ({ overIndex }) {
      if (this.rowDragOverIndex === overIndex) return
      this.rowDragOverIndex = overIndex ?? -1
      this.gridApi.redrawRows()
    },

    rowDragEnd ({ nodes, node, overNode }) {
      // successful end of drag. If we've started and ended on the same spot, just end
      this.rowDragging = false
      this.rowDragOverIndex = null
      this.gridApi.redrawRows()

      if (node === overNode) {
        return
      }

      // If there's no overnode, but drag hasn't cancelled, we can assume at the bottom, so we can insert at the end
      const insertIndex = (overNode === undefined) ? this.gridApi.getDisplayedRowCount() : overNode.rowIndex
      const rowIds = nodes.map(n => n.data.id)

      this.$emit('rows-moved', rowIds, insertIndex)
    },

    rowDragLeave () {
      // This occurs when dragged outside of grid
      this.rowDragOverIndex = null
      this.gridApi.redrawRows()
    },

    columnHover (e, colId) {
      if (this.columnSelection && !colIsAuto(colId)) {
        this.hoverColumnId = colId
      }
    },

    columnLeftClick (e, colId) {
      if (!this.columnSelection) return
      this.hoverColumnId = undefined
      this.clearSelection('rows', 'range')
      if (colIsAuto(colId)) {
        this.selectAll()
        return
      }

      const newSelectedColIds = [...this.selectedColumnIds]

      if (e.shiftKey) {
        const lastSelectedColId = newSelectedColIds[newSelectedColIds.length - 1]
        const lastSelectedColIndex = this.colDefs.findIndex(c => c.colId === lastSelectedColId)
        const thisColIndex = this.colDefs.findIndex(c => c.colId === colId)
        const minIndex = Math.min(lastSelectedColIndex, thisColIndex)
        const maxIndex = Math.max(lastSelectedColIndex, thisColIndex)

        // go through all columns between min and max, and add into our selectedColumnId if it doesn't already exist
        for (let i = minIndex; i <= maxIndex; i++) {
          const colId = this.colDefs[i].colId
          if (!newSelectedColIds.includes(colId)) {
            newSelectedColIds.push(colId)
          }
          this.$emit('update:selectedColumnIds', newSelectedColIds)
        }

      } else if (e.ctrlKey) {
        if (newSelectedColIds.includes(colId)) {
          // pop off the colId from newSelectedColIds
          this.$emit('update:selectedColumnIds', newSelectedColIds.filter(id => id !== colId))

        } else {
          // If alt is held down in the event modifier, then append colId to the list
          newSelectedColIds.push(colId)
          this.$emit('update:selectedColumnIds', newSelectedColIds)
        }
      } else {
        this.$emit('update:selectedColumnIds', [colId])
      }

    },

    columnRightClick (e, colId) {
      this.hoverColumnId = undefined
      this.clearSelection('rows', 'range')
      if (colIsAuto(colId)) {
        this.selectAll()

      } else if (this.columnSelection && (this.selectedColumnIds.length === 0 || !this.selectedColumnIds.includes(colId))) {
        this.$emit('update:selectedColumnIds', [colId])
      }
      this.$emit('column-right-click', e, colId)
    },

    isCellInRange (params) {
      const cells = this.selectedCells
      const { rowIndex, node: { rowPinned }, colDef: { colId } } = params
      if (!cells || rowPinned) return false
      const colIndex = this.colDefs.findIndex(c => c.colId === colId)
      return (
          colIndex >= cells.cols[0] && colIndex <= cells.cols[1] &&
          rowIndex >= cells.rows[0] && rowIndex <= cells.rows[1]
      )
    },

    isCellLocked: function (params) {
      // Whether the column is considered locked - which is !colDef 'editable'
      const { colDef: { colId }, node: { rowPinned } } = params
      if (!this.interactive || rowPinned || colIsAuto(colId)) return false
      return (!this.colDefs.find(c => c.colId === colId)?.editable)
    },

    isColCharted: function (colId) {
      return colId === this.labelColumnId || this.chartColumns.includes(colId)
    },

    isRowIndexCharted: function (rowIndex) {
      // if chart rows is array, check if row index is in array
      // noinspection JSUnresolvedReference
      return (
          rowIndex >= (this.chartRows?.from || 0) &&
          rowIndex <= (this.chartRows?.to || Infinity)
      )
    },

    isRowIdCharted: function (rowId) {
      return this.chartRows.includes(rowId)
    },

    isCellCharted: function (params) {
      if (!params.data) return
      const { colDef: { colId }, rowIndex, data: { id } } = params
      if (colIsAuto(colId)) return false
      if (Array.isArray(this.chartRows)) {
        // If we have an array for chartRows, they are Ids - so check if the rowId is in the array
        return (this.isColCharted(colId) && this.isRowIdCharted(id))
      } else {
        // otherwise, we assume a 'from' and 'to', which are indexes in the array
        return (this.isColCharted(colId) && this.isRowIndexCharted(rowIndex))
      }

    },

    getEditableColumn (last) {
      // get the first or last editable column in this.colDefs
      if (!this.interactive) return
      const cols = last ? this.colDefs.slice().reverse() : this.colDefs
      return cols.find((col) => col.editable)
    },

    tabToNextCell (params) {
      // Check if we are on the last cell of the last row
      if (this.interactive && (params.nextCellPosition === null || params.nextCellPosition.rowPinned)) {
        // Add a new row to the grid
        const rowIndex = params.backwards ? 0 : (params.previousCellPosition.rowIndex + 1)
        const colId = this.getEditableColumn(params.backwards)?.colId
        if (colId === undefined) return false
        if (params.editing) {
          this.$emit('insert-and-edit', rowIndex, colId)
        } else {
          this.$emit('insert-and-focus', rowIndex, colId)
        }
        return false
      }

      // Allow normal tabbing behavior
      const ncp = params.nextCellPosition
      if (ncp === null) {
        return false
      }
      return params.nextCellPosition
    },

    isCellEditing () {
      return this.gridApi.getCellEditorInstances().length > 0
    },

    showContextMenu (event) {
      if (!this.isCellEditing()) {
        this.$emit('contextmenu', event)
      }
    },

    selectionChanged () {
      const selectedNodeIds = this.gridApi.getSelectedNodes().map(node => node.data.id)
      if (!equal(selectedNodeIds, this.selectedRowIds)) {
        this.$emit('update:selectedRowIds', selectedNodeIds)
      }

    },

    keyDown (event) {
      const { key, ctrlKey, metaKey } = event
      if (!this.isCellEditing() && key === 'a' && (ctrlKey || metaKey)) {
        event.preventDefault()
        this.selectAll()
      } else if (!this.isCellEditing() && key === 'Enter') {
        // Confusingly, if we're _not_ editing it means we just _were_ editing
        this.$emit('edit-down', this.getFocusedCell())
      }
    },

    touchStart () {
      this.touchMoving = false
    },
    touchMove () {
      this.touchMoving = true
    },
    touchEnd () {
      if (!this.touchMoving) {
        const cell = this.getFocusedCell()
        if (cell && !this.isCellEditing()) {
          const { rowIndex, colId } = cell
          this.changeSelection(colId, rowIndex)
        }
      } else {
        this.touchMoving = false
      }
    }

  }

}
</script>


<!--suppress CssUnusedSymbol -->
<style>
@reference "tailwindcss"

.ag-theme-alpine .ag-root-wrapper {
  border-style: none;
}

.ag-theme-alpine .ag-header-row {
  text-transform: uppercase;
  font-size: theme('fontSize.xs') !important;
  color: theme('colors.gray.400') !important;
}

.ag-cell-focus {
  border-color: theme('colors.fuchsia.300') !important;
}

.ag-cell-focus:focus-within {
  border-color: theme('colors.fuchsia.500') !important;
}

/* Make sure only main rows get the hover color; this excludes pinned bottom row (aggregates row) */
.ag-body .ag-row .hovered,
.ag-body .ag-row-hover::before {
  background-color: color-mix(in oklab, theme('colors.fuchsia.500') 10%, transparent) !important;
}

.ag-body .ag-row-hover.ag-row-selected::before {
  background-color: color-mix(in oklab, theme('colors.fuchsia.500') 20%, transparent) !important;
  background-image: none;
}

.ag-row .ranged,
.ag-row .selected,
.ag-row-selected::before {
  background-color: color-mix(in oklab, theme('colors.fuchsia.500') 15%, transparent) !important;
}


/* blank 1px border so that the insertion border doesn't change the horizontal layout */
.ag-row-first {
  border-top: 1px solid transparent;
}

.ag-row.row-insert-above {
  border-top: 1px solid theme('colors.fuchsia.500');
}

.ag-row.row-insert-below {
  border-bottom: 1px solid theme('colors.fuchsia.500');
}

.ag-row .locked {
  color: theme('colors.lime.600');
}

/* Disables the focus display on the left cell in the aggregates row */
.ag-floating-bottom .ag-grid-auto-cell {
  border-left: none !important;
  border-top: none !important;
  border-bottom: none !important;
  border-right-color: theme('colors.zinc.300') !important;
}

/* darken more locked cell color when selected */
.ag-row .ranged.locked,
.ag-row .selected.locked,
.ag-row.ag-row-selected .locked {
  color: theme('colors.lime.700') !important;
}

/* disable row hover when doing a range selection and on pinned bottom (aggregates) row */
.ag-floating-bottom .ag-row-hover::before,
.range-selection .ag-row-hover:not(.ag-full-width-row)::before {
  background-color: inherit !important;
}

.ag-body .ag-cell.labeled {
  color: theme('colors.fuchsia.600');
  font-weight: theme('fontWeight.semibold');
}

.ag-body .ag-cell.labeled.selected {
  color: theme('colors.fuchsia.700');
}

.charted-cells .ag-body .ag-cell:not(.charted):not(.ag-grid-auto-cell) {
  opacity: theme('opacity.20');
}


/* Left pinned cells -------------- */
.ag-grid-row-drag .ag-cell-value {
  display: none;
}

.ag-header-cell-resize::after {
  background-color: theme('colors.zinc.300');
}

.ag-header,
.ag-pinned-left-cols-container .ag-grid-auto-cell {
  background-color: theme('colors.zinc.100');
}

.ag-pinned-left-cols-container .ag-row-drag {
  margin: 0;
  justify-content: center;
  flex-grow: 1 !important;
}

.ag-pinned-left-cols-container .ag-grid-auto-cell {
  text-align: center;
  font-size: theme('fontSize.xs');
  line-height: theme('lineHeight.8');
  padding: 0;
  border-color: transparent !important;
}

/* ------ Pinned left columns ---------------------------------- */
.ag-pinned-left-cols-container .ag-cell:nth-last-child(1 of .ag-grid-auto-cell),
.ag-pinned-left-cols-container .ag-cell-last-left-pinned:not(.ag-cell-focus) {
  border-right-style: solid;
  border-right-color: theme('colors.zinc.300') !important;
}

.ag-pinned-left-header .ag-header-cell:not(.ag-header-cell-sortable) {
  padding: 0;
}

.ag-pinned-left-header .ag-header-cell:nth-last-child(1 of :not(.ag-header-cell-sortable)) {
  border-right: 1px !important;
  border-right-style: solid !important;
  border-right-color: theme('colors.zinc.300') !important;
}

.ag-row-selected .ag-cell-last-left-pinned {
  border-right-color: theme('colors.fuchsia.500') !important;
}

.ag-cell.ag-grid-row-pos:hover {
  cursor: cell;
}

/* totals row on bottom */
.ag-floating-bottom {
  background-color: theme('colors.zinc.100') !important;
  color: theme('colors.slate.900') !important;
}

/* totals row, left hand side - might be a colspanned rowDrag or a single row (if in a view)
 - either way it's just showing a sum symbol */

.ag-floating-bottom .ag-grid-auto-cell {
  padding: 0 !important;
  border-right-color: theme('colors.zinc.300') !important;
  border-right-style: solid !important;
  font-size: theme('fontSize.xs') !important;
  line-height: theme('lineHeight.9') !important;
  text-transform: uppercase !important;
  text-align: center !important;
}

.ag-floating-bottom .ag-row {
  background-color: transparent !important;
}


</style>


