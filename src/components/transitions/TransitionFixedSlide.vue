<template>
  <transition
      :enter-active-class="transitionInStyle"
      :leave-active-class="transitionOutStyle"
      :enter-from-class="startStyle"
      :enter-to-class="finishStyle"
      :leave-from-class="finishStyle"
      :leave-to-class="startStyle"
      @after-enter="$emit('update:modelValue', 'opened')"
      @after-leave="$emit('update:modelValue', 'closed')">
    <div v-show="modelValue !== 'closing' && modelValue !== 'closed'">
      <slot/>
    </div>
  </transition>
</template>

<script>
/*
A transition that slides from hidden to a flexed position in the layout.

Goes through a transition of fixed as it slides out before popping into its space.
Emits a model of closed > opening > opened > closing > closed.
*/

export default {
  emits: ['update:modelValue'],
  props: {
    fixOnTransition: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: String,
      default: 'closed'
    },
    slideFrom: {
      type: String,
      default: 'left',
      validator: value => ['left', 'right', 'top', 'bottom'].includes(value)
    },
    instantOpen: {
      type: Boolean,
      default: false
    },
    instantClose: {
      type: Boolean,
      default: false
    }
  },

  mounted () {
    setTimeout(() => {
      this.afterMount = true
    }, 200)
  },

  beforeUnmount () {
    this.$emit('update:modelValue', 'closed')
  },

  data: () => ({
    afterMount: false
  }),

  computed: {
    transitionStyle () {
      let styles = 'transform transition ease-in-out z-40'
      if (this.fixOnTransition) {
        styles += ' fixed ' + {
          left: 'inset-y-0 left-0',
          right: 'inset-y-0 right-0',
          top: 'inset-x-0 top-0',
          bottom: 'inset-x-0 bottom-0'
        }[this.slideFrom]
      }
      return styles
    },
    transitionInStyle () {
      return (!this.afterMount || this.instantOpen) ? '' : (this.transitionStyle + ' duration-200')
    },
    transitionOutStyle () {
      return this.instantClose ? '' : (this.transitionStyle + ' duration-200')
    },
    startStyle () {
      return {
        left: '-translate-x-full',
        right: 'translate-x-full',
        top: '-translate-y-full',
        bottom: 'translate-y-full'
      }[this.slideFrom]
    },
    finishStyle () {
      return {
        left: 'translate-x-0',
        right: 'translate-x-0',
        top: 'translate-y-0',
        bottom: 'translate-y-0'
      }[this.slideFrom]
    }
  }
}
</script>

<style scoped>

</style>
