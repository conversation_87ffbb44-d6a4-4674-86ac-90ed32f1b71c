<template>
  <transition
    mode="out-in"
    enter-active-class="transition-all duration-300"
    leave-active-class="transition-all duration-200"
    enter-from-class="max-h-0"
    enter-to-class="max-h-28"
    leave-from-class="max-h-28"
    leave-to-class="max-h-0">
    <slot></slot>
  </transition>
</template>

<script>
export default {
  name: 'TransitionFixedAppearDown'
}
</script>


<style scoped>

</style>
