<template>
  <Dialog :open="open" as="div" class="fixed overflow-hidden z-40" @close="$emit('close')">
    <div class="absolute overflow-hidden">
      <div class="fixed inset-y-0 max-w-full flex right-0">
        <div class="w-112">
          <form class="h-full divide-y divide-gray-200 flex flex-col bg-white shadow-xl">
            <header>
              <div class="py-6 px-4 bg-fuchsia-700 sm:px-6">
                <slot name="header">
                  <div class="flex items-center justify-between">
                    <dialog-title class="text-lg font-medium text-white">
                      {{ title }}
                    </dialog-title>
                    <div class="ml-3 h-7 flex items-center">
                      <button type="button" class="bg-fuchsia-700 rounded-md text-fuchsia-200 hover:text-white"
                              @click="$emit('close')">
                        <span class="sr-only">Close panel</span>
                        <X class="h-6 w-6" aria-hidden="true"/>
                      </button>
                    </div>
                  </div>
                  <div v-if="subtitle" class="mt-1">
                    <p class="text-sm text-fuchsia-300">
                      {{ subtitle }}
                    </p>
                  </div>
                </slot>
              </div>
            </header>
            <main class="flex-1 flex overflow-y-auto">
              <slot></slot>
            </main>
            <footer class="px-4 py-4">
              <slot name="footer"></slot>
            </footer>
          </form>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script>
import { X } from 'lucide-vue-next'
import { Dialog, DialogTitle } from '@headlessui/vue'
import TransitionFixedSlide from '@/components/transitions/TransitionFixedSlide.vue'

export default {
  components: {
    TransitionFixedSlide,
    X,
    Dialog,
    DialogTitle
  },

  props: {
    open: Boolean,
    title: String,
    subtitle: String
  }
}
</script>

<style scoped>

</style>
