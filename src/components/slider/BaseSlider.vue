<template>
  <transition-root as="template" :show="open">
    <Dialog as="div" class="fixed inset-0 overflow-hidden z-40" @close="$emit('close')">
      <div class="absolute inset-0 overflow-hidden">
        <dialog-overlay class="absolute inset-0 bg-gray-900 opacity-20"/>
        <div class="fixed inset-y-0 max-w-full flex" :class="[rightSide ? 'right-0 pl-10' : 'left-0 pr-10']">
          <transition-child
              as="template"
              :enter="transition ? 'transform transition ease-in-out duration-500' : ''"
              :enter-from="rightSide ? 'translate-x-full' : '-translate-x-full'"
              enter-to="translate-x-0"
              :leave="transition ? 'transform transition ease-in-out duration-300' : ''"
              leave-from="translate-x-0"
              :leave-to="rightSide ? 'translate-x-full' : '-translate-x-full'">
            <div class="w-screen max-w-xl">
              <form class="h-full divide-y divide-gray-200 flex flex-col bg-white shadow-xl">
                <header>
                  <div class="py-6 px-4 bg-fuchsia-700 sm:px-6">
                    <slot name="header">
                      <div class="flex items-center justify-between">
                        <dialog-title class="text-lg font-medium text-white">
                          {{ title }}
                        </dialog-title>
                        <div class="ml-3 h-7 flex items-center">
                          <button type="button" class="bg-fuchsia-700 rounded-md text-fuchsia-200 hover:text-white"
                                  @click="$emit('close')">
                            <span class="sr-only">Close panel</span>
                            <X class="h-6 w-6" aria-hidden="true"/>
                          </button>
                        </div>
                      </div>
                      <div v-if="subtitle" class="mt-1">
                        <p class="text-sm text-fuchsia-300">
                          {{ subtitle }}
                        </p>
                      </div>
                    </slot>
                  </div>
                </header>
                <main class="flex-1 flex overflow-y-auto">
                  <slot></slot>
                </main>
                <footer class="px-4 py-4">
                  <slot name="footer"></slot>
                </footer>
              </form>
            </div>
          </transition-child>
        </div>
      </div>
    </Dialog>
  </transition-root>
</template>

<script>
import { X } from 'lucide-vue-next'
import { Dialog, DialogOverlay, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'

export default {
  components: {
    X,
    Dialog,
    DialogTitle,
    DialogOverlay,
    TransitionChild,
    TransitionRoot
  },

  props: {
    open: Boolean,
    title: String,
    subtitle: String,
    rightSide: Boolean,
    transition: Boolean
  }
}
</script>

<style scoped>

</style>
