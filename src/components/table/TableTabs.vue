<template>
  <div class="flex-1 shrink min-w-0 flex items-center overflow-hidden">
    <div class="mx-2 flex shrink-0 space-x-0.5 group-hover">
      <base-tab
          :to="{ name: 'app-table', params: { tableId: this.tableStoreId } }"
          :is-active="!viewId">
        <template #default="{ isActive }">

          <Table2 class="size-4"/>
          <div class="mx-3">
            <span v-if="tableStoreTitle">{{ tableStoreTitle }}</span>
            <em v-else>Untitled Table</em>
          </div>
          <Pencil
              @click="$emit('edit-table')"
              class="size-4 text-white transition-all duration-100 opacity-50 group-hover:opacity-100"
              :class="{
              'invisible opacity-0': !isActive
            }"
          />
        </template>

      </base-tab>
    </div>

    <button
        @click="$refs.modalViewList.open()"
        title="Add, edit and copy your views"
        class="p-2 bg-zinc-100 hover:bg-zinc-200 disabled:bg-transparent rounded-sm mr-1 cursor-pointer">
      <EllipsisVertical class="size-4 text-zinc-700" aria-hidden="true"/>
    </button>

    <button
        @click="$refs.modalView.open()"
        title="Add, edit and copy your views"
        class="p-2 bg-zinc-100 hover:bg-zinc-200 disabled:bg-transparent rounded-sm mr-1 cursor-pointer"
    >
      <Plus class="size-4 text-zinc-700" aria-hidden="true"/>
    </button>

    <tab-group v-if="tableStoreViews.length" :min-width="false">
      <base-tab
          v-for="view in tableStoreViews"
          :key="view.id"
          :to="{
            name: 'app-view',
            params: { tableId: this.tableStoreId, viewId: view.id }
          }"
          :is-active="view.id === viewId"
          :name="view.title">
        <template #default="{ isActive }">
          <component :is="getViewIcon(view)" class="size-4"/>
          <div class="mx-3">{{ view.title }}</div>
          <Pencil
              class="size-4 text-white transition-all duration-100 opacity-50 group-hover:opacity-100"
              :class="{
                'invisible opacity-0': !isActive
              }"
              @click="$refs.modalView.open(view)"
          />
        </template>
      </base-tab>
    </tab-group>
  </div>

  <teleport to="body">
    <modal-view-list
        ref="modalViewList"
        @delete-view="deleteView"
        @edit-view="this.$refs.modalView.open($event)"
        @add-view="addView"
        @add-new-view="$refs.modalView.open()"
    />
    <modal-view
        ref="modalView"
        @change-view="changeView"
        @add-view="addView"
        @delete-view="deleteView"
    />
  </teleport>
</template>
<script>
import { mapActions, mapState } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import TabGroup from '@/components/ui/tabs/TabGroup.vue'
import RouterLinkTab from '@/components/ui/tabs/RouterLinkTab.vue'
import { EllipsisVertical, Pencil, Plus, Table2 } from 'lucide-vue-next'
import ModalViewList from '@/components/modal/ModalViewList.vue'
import ModalView from '@/components/modal/ModalView.vue'
import BaseTab from '@/components/ui/tabs/BaseTab.vue'
import { getViewIcon } from '@/db/grouping.js'

export default {
  name: 'TableTabs',
  components: {
    BaseTab,
    ModalView,
    ModalViewList,
    Plus,
    EllipsisVertical,
    Pencil,
    RouterLinkTab,
    TabGroup,
    Table2
  },
  computed: {

    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreTitle: 'title',
      tableStoreViews: 'views'
    })
  },
  emits: ['edit-table', 'refresh-view'],

  props: {
    // Not currently doing anything, active tab is based on the route
    viewId: {
      type: String,
      required: false
    }
  },

  methods: {
    getViewIcon,
    ...mapActions(useTableStore, {
      tableStoreDeleteView: 'deleteView',
      tableStoreAddView: 'addView',
      tableStoreChangeView: 'changeView'
    }),

    deleteView (viewId) {
      // if we're currently viewing this view, navigate back to the table
      if (this.viewId === viewId) {
        this.$router.push({ name: 'app-table', params: { tableId: this.tableStoreId } })
      }
      this.tableStoreDeleteView(viewId)
    },

    async addView (viewData) {
      const view = await this.tableStoreAddView(viewData)
      this.$router.push({ name: 'app-view', params: { tableId: this.tableStoreId, viewId: view.id } })
    },

    async changeView (viewData) {
      await this.tableStoreChangeView(viewData)
      this.$emit('refresh-view')
    }
  }
}
</script>

