<template>
  <div class="h-full flex">
    <input ref="editor"
           v-model="value"
           class="text-right"
           :class="[baseInputStyles, valid ? '' : 'text-red-500' ]"/>
  </div>
</template>

<script>
import CellEditor from './CellEditor'

export default {
  name: 'PercentCellEditor',
  extends: CellEditor,
  computed: {
    valid () {
      return !isNaN(this.parse(this.value))
    }
  }
}
</script>

<style scoped>

</style>
