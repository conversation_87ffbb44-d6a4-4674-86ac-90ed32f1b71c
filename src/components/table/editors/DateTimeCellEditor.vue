<template>
  <div class="h-full flex items-center" ref="container">
    <input-date-time
        open-on-mount
        :format="format"
        v-model="dateValue"
        ref="editor"/>
  </div>
</template>

<script>
import CellEditor from './CellEditor'
import InputDateTime from '@/components/forms/input/DateTimeInput.vue'

export default {
  name: 'DateTimeCellEditor',
  extends: CellEditor,
  components: { InputDateTime },

  data () {
    return {
      format: '',
      dateValue: null
    }
  },

  methods: {
    setValue () {
      this.dateValue = this.params.value
      this.format = this.params.colProps.format
    },

    getValue () {
      return this.dateValue
    },

    setFocus () {
      this.$nextTick(() => {
        this.$refs.editor.$el.querySelector('input').focus()
      })
    }
  }
}
</script>
