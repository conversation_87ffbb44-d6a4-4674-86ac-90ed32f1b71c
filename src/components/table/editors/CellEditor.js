import { columnTypes } from '@/utils/formats.js'

export default {
  props: {
    params: Object
  },
  data () {
    return {
      value: '',
      baseInputStyles: 'block px-4 py-0 border-0 focus:border-0 focus:ring-0 active:border-0 w-full sm:text-sm'
    }
  },
  mounted () {
    this.setValue()
    this.processValue()
    this.processEntry()
    this.setFocus()
  },
  methods: {
    
    setValue () {
      const val = this.params.value ? this.params.value.toString() : ''
      this.value = this.processValue(val)
    },
    
    processValue (val) {
      // inheritors can hook into this and further process the value
      return val
    },
    
    processEntry () {
      /* Based on how the user entered the cell, modify the value */
      const key = this.params.eventKey
      if (key === 'Backspace') {
        // Delete one char on backspace - for some reason this
        // "carries through" and we don't have to manipulate the value
        this.value = this.params.value.toString()
        
      } else if (key && key !== 'F2' && key !== 'Enter') {
        // if key is not blank, not F2 and not Enter, change value to this keypress
        this.value = key
      }
    },
    
    setFocus () {
      this.$nextTick(() => {
        this.$refs.editor.focus()
      })
    },
    
    parse (value) {
      return columnTypes[this.params.colType].convert(value) ?? value
    },
    
    getValue () {
      return this.parse(this.value)
    }
  }
}
