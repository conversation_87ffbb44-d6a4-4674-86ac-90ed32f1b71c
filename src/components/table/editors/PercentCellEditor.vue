<template>
  <div class="h-full flex">
    <input
        ref="editor"
        type="text"
        v-model="value"
        placeholder="%"
        class="text-right"
        :class="[baseInputStyles, valid ? '' : 'text-red-500']"
    />
  </div>
</template>

<script>
import CellEditor from './CellEditor'
import { columnTypes } from '@/utils/formats.js'

export default {
  name: 'PercentCellEditor',
  extends: CellEditor,
  computed: {
    valid () {
      return !isNaN(this.parse(this.value))
    }
  },
  methods: {
    processValue (val) {
      /* If number is valid numeric, multiply by 100 */
      const native = columnTypes.percent.native(val)
      if (native !== null) {
        return native.times(100)
      }
      return val
    },

    parse (value) {
      /* If value doesn't end in a %, add one */
      let testValue = value
      if (!testValue.toString().endsWith('%')) {
        testValue = testValue + '%'
      }

      // Gotcha - make sure we return original value pre-% append, otherwise
      // we get things like `badstring%` instead of `badstring` returned!
      return columnTypes.percent.convert(testValue) ?? value
    }
  }
}
</script>

<style scoped>

</style>
