<template>
  <div class="h-full flex items-center justify-center">
    <input
        ref="editor"
        type="checkbox"
        v-model="displayValue"
        class="h-4 w-4 rounded-sm border-gray-300 ring-fuchsia-600 focus:ring-fuchsia-600"
        :indeterminate.prop="isNull"
        @keydown="handleKeyDown"
    />
  </div>
</template>

<script>
import CellEditor from './CellEditor'

export default {
  name: 'BooleanCellEditor',
  extends: CellEditor,
  data () {
    return {
      isNull: false
    }
  },
  computed: {
    displayValue: {
      get () {
        return this.value === true
      },
      set (newValue) {
        this.isNull = false
        this.value = newValue
      }
    }
  },

  methods: {
    setValue () {
      this.isNull = this.params.value === null
      this.value = this.params.value === true

      if (this.isNull) {
        this.$refs.editor.indeterminate = true
      }
    },

    processEntry () {
      /* Based on how the user entered the cell, modify the value */
      const key = this.params.eventKey
      if (key === 'Backspace') {
        this.isNull = true
        this.value = null
        this.$refs.editor.indeterminate = true
      }
    },

    getValue () {
      // If the user didn't interact with the checkbox, return the original value
      if (this.isNull) {
        return null
      }
      return this.value
    },
    handleKeyDown (event) {
      if (event.key === 'Delete') {
        event.preventDefault()
        this.isNull = true
        this.value = null
        this.$nextTick(() => {
          this.$refs.editor.indeterminate = true
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
