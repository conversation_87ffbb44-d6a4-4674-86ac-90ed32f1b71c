<template>
  <form class="w-full" @submit.prevent @change.prevent="save">
    <input type="text"
           v-model="tableStoreTitle"
           placeholder="Give your table a title"
           class="h-full w-full border-transparent py-2 pl-8 pr-3 text-base text-gray-900 focus:outline-hidden focus:ring-0 focus:border-transparent focus:placeholder-gray-400">
  </form>
</template>
<script>
import { mapActions, mapWritableState } from 'pinia'
import { useTableStore } from '@/stores/table.js'

export default {
  name: 'the-table-title-edit',
  computed: {
    ...mapWritableState(useTableStore, {
      tableStoreTitle: 'title'
    })
  },
  methods: {
    ...mapActions(useTableStore, {
      tableStoreSaveTitle: 'saveTitle'
    }),
    save () {
      this.tableStoreSaveTitle()
    }
  }
}
</script>
