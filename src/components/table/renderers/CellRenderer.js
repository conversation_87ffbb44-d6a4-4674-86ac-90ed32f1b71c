import { columnTypes } from '@/utils/formats.js'

export default {
  props: {
    params: Object
  },
  data () {
    return {
      error: false,
      value: '',
      colType: undefined,
      colProps: undefined,
      aggregate: undefined
    }
  },
  
  watch: {
    params: {
      handler () {
        this.parse()
      },
      immediate: true
    }
  },
  computed: {
    tooltip () {
      if (this.aggregate) {
        if (this.aggregate.header) {
          return `${this.aggregate.label} for ${this.aggregate.header}: ${this.tooltipValue}`
        } else {
          return `${this.aggregate.label}: ${this.tooltipValue}`
        }
      }
    },
    
    formattedValue () {
      return columnTypes[this.colType].repr(this.value, this.colProps)
    },
    
    tooltipValue () {
      return this.value
    }
  },
  
  methods: {
    parse () {
      this.colType = this.params.colType
      this.colProps = this.params.colProps
      this.aggregate = this.params.aggregate
      this.error = this.params.error
      this.value = this.params.value
    },
    
    copyToClipboard () {
      // copy this.tooltipValue to clipboard. Children should call this on cell click
      if (this.aggregate) {
        navigator.clipboard.writeText(this.tooltipValue).then(() => {
          // copied
        })
      }
    }
  }
}
