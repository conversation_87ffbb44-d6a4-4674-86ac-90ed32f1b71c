<template>
  <div
      class="flex items-center justify-center h-full"
      :title="this.tooltip"
      @click="copyToClipboard">
    <div
        v-if="error"
        :class="{ 'text-red-500': error }">{{ this.value }}
    </div>
    <div v-else>
      <CheckCircle v-if="this.value === true" class="h-5 w-5 text-green-500"/>
      <XCircle v-else-if="this.value === false" class="h-5 w-5 text-red-500"/>
    </div>
  </div>
</template>

<script>
import { CheckCircle, XCircle } from 'lucide-vue-next'
import CellRenderer from '@/components/table/renderers/CellRenderer.js'

export default {
  extends: CellRenderer,
  components: {
    CheckCircle,
    XCircle
  }
}
</script>

<style scoped>

</style>
