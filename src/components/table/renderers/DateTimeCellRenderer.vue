<template>
  <div
      @click="copyToClipboard"
      class="text-right"
      :class="{ 'text-red-500': error }"
      :title="this.tooltip">{{ formattedDate }}
  </div>
</template>
<script>
/*
Numeric cell renderer
 */
import CellRenderer from '@/components/table/renderers/CellRenderer.js'
import { columnTypes } from '@/utils/formats.js'

export default {
  extends: CellRenderer,

  computed: {
    formattedDate () {
      if (this.error) {
        return this.value
      }
      return columnTypes.datetime.repr(this.value, this.colProps)
    }
  }
}
</script>

<style scoped>

</style>
