<template>
  <div
      class="flex flex-row"
      :class="{ 'text-red-500': error }"
      :title="this.tooltip"
      @click="copyToClipboard">

    <span v-if="error" class="flex-1 text-right">{{ value }}</span>
    <template v-else>
      <span class="flex-1">{{ parts.symbol }}</span>
      <span class="shrink-0">{{ error ? value : parts.value }}</span>
    </template>

  </div>

</template>

<script>
import CellRenderer from '@/components/table/renderers/CellRenderer.js'
import { columnTypes } from '@/utils/formats.js'

export default {
  extends: CellRenderer,
  computed: {
    tooltipValue () {
      // this is only called in the base 'tooltip' method, and that only occurs
      // for aggregate cells, so shouldn't really be an error here
      return this.error ? this.value : this.parts.full
    },

    parts () {
      return columnTypes.currency.toParts(this.value, this.colProps)
    }
  }
}
</script>

<style scoped>

</style>
