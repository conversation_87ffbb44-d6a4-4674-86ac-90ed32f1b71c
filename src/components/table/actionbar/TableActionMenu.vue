<template>
  <v-menu as="div" class="relative inline-block text-left">
    <menu-button small :as="baseBtn" type="dark" :icon-component="ChevronDown" is-text>{{ label }}</menu-button>
    <transition-dropdown>
      <menu-items
          class="absolute text-sm left-0 w-64 mt-2 origin-top-right bg-white divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black/5 focus:outline-hidden">

        <slot></slot>
      </menu-items>
    </transition-dropdown>
  </v-menu>

</template>

<script>

import { defineComponent, markRaw } from 'vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { ChevronDown } from 'lucide-vue-next'
import { Menu as VMenu, MenuButton, MenuItems } from '@headlessui/vue'
import TransitionDropdown from '@/components/transitions/TransitionDropdown.vue'

export default defineComponent({
  components: { MenuItems, MenuButton, TransitionDropdown, VMenu },
  props: {
    label: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      baseBtn: markRaw(BaseBtn),
      ChevronDown: markRaw(ChevronDown)
    }
  }
})
</script>


<style scoped>

</style>
