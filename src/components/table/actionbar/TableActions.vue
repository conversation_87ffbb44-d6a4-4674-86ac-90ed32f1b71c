<template>
  <div class="flex-none shrink-0 bg-zinc-900 mr-1 flex items-center py-2">

    <table-action-menu v-if="viewId" label="View...">
      <menu-item-group>
        <menu-item-button @click="$emit('export-table')">
          <ArrowRightFromLine class="w-5 h-5 mr-2" aria-hidden="true"/>
          Export/embed view
        </menu-item-button>
      </menu-item-group>
    </table-action-menu>

    <table-action-menu v-else label="Table...">
      <menu-item-group>
        <menu-item-button @click="$emit('export-table')">
          <ArrowRightFromLine class="w-5 h-5 mr-2" aria-hidden="true"/>
          Export/embed table
        </menu-item-button>

        <menu-item-button @click="$emit('set-first-row-to-header')">
          <ListStart class="w-5 h-5 mr-2" aria-hidden="true"/>
          Set first row as header
        </menu-item-button>
      </menu-item-group>
      <menu-item-group>
        <menu-item-button @click="$refs.deleteTableDialog.open()" type="warning">
          <Trash class="w-5 h-5 mr-2" aria-hidden="true"/>
          Delete table...
        </menu-item-button>
      </menu-item-group>
    </table-action-menu>

    <table-action-button v-if="!chartPaneOpen" :icon="PanelBottomOpen" @click="toggleChartPane(true)"
                         label="View Charts"/>
    <table-action-button v-else :icon="PanelBottomClose" @click="toggleChartPane(false)"
                         label="Close Charts"/>

    <table-action-button :icon="ChartColumn" @click="addNewChart" label="Create Chart"/>


    <table-action-button :icon="Pi" @click="$emit('formula-editor')" label="Formula Editor"/>

    <btn-group v-if="isFiltered">
      <table-action-button
          is-highlighted
          is-first
          :icon="viewTypes.filter.icon"
          @click="addNewFilteredView"
          label="Create New View from Filters"
          title="Create a new filtered view with the current filters applied to the table."/>

      <table-action-button
          is-highlighted
          is-last
          @click="clearFilters"
          label="Clear all filters"
          :icon="X"
      />
    </btn-group>
  </div>

  <teleport to="body">
    <confirm-dialog
        title="Delete Table"
        text="Are you sure you want to delete this table?"
        ref="deleteTableDialog"
        @confirm="deleteTable"
    />
  </teleport>
</template>
<script>

import { markRaw } from 'vue'
import { Menu, MenuButton, MenuItems } from '@headlessui/vue'
import {
  ArrowRightFromLine,
  ChartColumn,
  ChevronDown,
  Copy,
  ListStart,
  PanelBottomClose,
  PanelBottomOpen,
  Pi,
  Table,
  Trash,
  X
} from 'lucide-vue-next'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import TransitionDropdown from '@/components/transitions/TransitionDropdown.vue'
import ConfirmDialog from '@/components/modal/ConfirmDialog.vue'
import BtnGroup from '@/components/buttons/BtnGroup.vue'
import TheTableTitleEdit from '@/components/table/TheTableTitleEdit.vue'
import TableActionButton from '@/components/table/actionbar/TableActionButton.vue'
import TableActionMenu from '@/components/table/actionbar/TableActionMenu.vue'
import { mapActions } from 'pinia'
import { createView, createViewColumn, useTableStore } from '@/stores/table.js'
import { useProjectsStore } from '@/stores/projects.js'
import { viewTypes } from '@/db/grouping.js'

export default {
  emits: ['close-chart-pane', 'export-table', 'set-first-row-to-header', 'formula-editor', 'refresh-columns'],
  components: {
    TableActionMenu,
    TableActionButton,
    TheTableTitleEdit,
    BtnGroup,
    ConfirmDialog,
    TransitionDropdown,
    MenuItemGroup,
    MenuItemButton,
    BaseBtn,
    VMenu: Menu,
    MenuButton,
    MenuItems,
    ChevronDown,
    ListStart,
    Trash,
    Table,
    ArrowRightFromLine,
    Copy
  },
  props: {
    chartPaneOpen: {
      type: Boolean,
      required: true
    },
    tableId: {
      type: String,
      required: true
    },
    viewId: {
      type: String,
      required: false
    }
  },
  data () {
    return {
      Pi: markRaw(Pi),
      PanelBottomOpen: markRaw(PanelBottomOpen),
      PanelBottomClose: markRaw(PanelBottomClose),
      ChartColumn: markRaw(ChartColumn),
      X: markRaw(X),
      viewTypes
    }
  },

  computed: {
    isFiltered () {
      return !this.viewId && !this.chartPaneOpen && useTableStore().columns.some(col => col.filters?.length > 0)
    }
  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreDelete: 'delete'
    }),
    ...mapActions(useProjectsStore, {
      projectsStoreReload: 'reload'
    }),

    route (mode) {
      this.$router.push({
        name: `app-${this.viewId ? 'view' : 'table'}${mode ? '-' + mode : ''}`,
        params: {
          tableId: this.tableId,
          viewId: this.viewId
        }
      })
    },

    async deleteTable () {
      await this.tableStoreDelete()
      await this.projectsStoreReload()
      await this.$router.push({ name: 'app-dashboard' })
    },

    toggleChartPane (show = false) {
      // update the router to be the given table ID (or the one in table store)
      // mode can optionally be either add (to add new chart) or charts (to show chart list)
      this.route(show ? 'charts' : '')
      if (!show) {
        this.$emit('close-chart-pane')
      }
    },

    addNewChart () {
      this.$router.push({
        name: `app-${this.viewId ? 'view' : 'table'}-chart-add`,
        params: {
          tableId: this.tableId,
          viewId: this.viewId
        }
      })
    },

    async clearFilters () {
      useTableStore().columns.forEach(col => {
        col.filters = []
      })
      useTableStore().saveColumns()
      this.$emit('refresh-columns')
    },

    async addNewFilteredView () {
      // Get the table store
      const tableStore = useTableStore()

      // Create a new view with the same columns as the table
      const newView = createView(tableStore.id)
      newView.title = `${tableStore.title} - Filtered`

      // Add all columns from the table to the view
      tableStore.columns.forEach((col, index) => {
        const viewCol = createViewColumn(col.id, index)
        viewCol.header = col.header
        viewCol.width = col.width
        newView.columns.push(viewCol)
      })

      // Copy over the filters from the table columns to the view
      const filters = {}
      tableStore.columns.forEach(col => {
        if (col.filters && col.filters.length > 0) {
          filters[col.id] = col.filters
        }
      })

      // Set the filters on the view
      if (Object.keys(filters).length > 0) {
        newView.filters = filters
      }

      // Add the view to the store and route to it
      const view = await tableStore.addView(newView)
      this.$router.push({ name: 'app-view', params: { tableId: this.tableId, viewId: view.id } })
      await this.clearFilters()
    }
  }
}
</script>
