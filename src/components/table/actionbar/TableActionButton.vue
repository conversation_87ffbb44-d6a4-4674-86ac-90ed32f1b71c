<template>
  <base-btn
      small
      icon-left
      :icon-component="icon"
      :is-text="!isHighlighted"
      type="dark"
  >
    {{ label }}
  </base-btn>
</template>

<script>

import BaseBtn from '@/components/buttons/BaseBtn.vue'

export default {
  components: { BaseBtn },
  props: {
    label: {
      type: String,
      required: true
    },
    icon: {
      type: [Function, Object],
      required: true
    },
    isHighlighted: {
      type: Boolean,
      default: false
    }

  }
}
</script>

<style scoped>

</style>
