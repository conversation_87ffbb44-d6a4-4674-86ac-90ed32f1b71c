<template>

  <data-hero-grid
      v-bind="$attrs"
      v-show="showGrid"
      ref="grid"

      :row-selection="true"
      :range-selection="true"
      :column-selection="true"

      v-model:selected-column-ids="appStoreSelectedColumnIds"
      v-model:selected-row-ids="appStoreSelectedRowIds"
      v-model:selected-cells="appStoreSelectedCells"

      :interactive="!viewId"
      :show-filters="!viewingChart"
      :label-column-id="chartStoreChartedColumns.length ? chartStoreLabelColumnId : tableStoreLabelColumnId"
      :chart-columns="chartStoreChartedColumns"
      :chart-rows="chartStoreChartedRows"

      :data-source="dataSource"

      @grid-ready="setGridColumns()"

      @column-pinned="columnPinned"
      @column-moved="columnMoved"
      @column-resized="columnResized"
      @column-right-click="columnRightClick"
      @delete-selection="deleteSelection"
      @filter-column="(e, colId) => $refs.filterContextMenu.showMenu(e, columns.find(c => c.id === colId))"

      @contextmenu="showCellContextMenu"
      @cell-value-changed="changeCell"
      @insert-and-focus="insertAndFocus"
      @insert-and-edit="insertAndEdit"
      @edit-down="editDown"
      @rows-moved="moveRows"
  />

  <teleport to="body">
    <row-context-menu
        ref="rowContextMenu"
        @create-chart="createChart"
        @insert="insertAtSelection"
        @delete="deleteSelectedRows"
    />
    <column-context-menu
        ref="columnContextMenu"
        @edit="$refs.columnEditor.open(getColumn($event))"
        @edit-calc="showCalcColumnEditor"
        @pin="changeColumnPin"
        @change-type="changeColumnType"
        @create-chart="createChart"
        @delete="deleteColumns"
        @insert="insertColumnAtSelection"
        @insert-calc="insertCalcColumn"
        @change-sort="setViewSort"
    />
    <range-context-menu ref="rangeContextMenu" @create-chart="createChart"/>

    <aggregate-context-menu
        ref="aggregateContextMenu"
        @more="$refs.columnEditor.open($event)"
        @set-aggregate="setColumnAggregate"/>
    <modal-edit-column ref="columnEditor" @edit-column="changeColumn"/>
    <modal-calculated-column
        ref="calcColumnEditor"
        @insert-column="insertCalcColumn"
        @change-column="changeCalcColumn"
    />
    <filter-context-menu
        ref="filterContextMenu"
        @set-filters="setColumnFilters"
        @save-filters="saveColumn"
    />
    <confirm-dialog
        ref="clearFiltersDialog"
        title="Remove Filters?"
        text="To perform this action, you need to remove filtering. Do you want to clear all filters?"
        button-text="Clear Filters"
        @confirm="clearFilters"
    />
  </teleport>

</template>


<script>
import { mapActions, mapState, mapWritableState } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import { createDataset, useChartStore } from '@/stores/chart.js'
import { useNotificationStore } from '@/stores/notifications.js'
import { columnTypes, convert, getDefaultColProps, repr } from '@/utils/formats.js'
import ModalCalculatedColumn from '@/components/modal/ModalCalculatedColumn.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import ModalEditColumn from '@/components/modal/ModalEditColumn.vue'
import ContextSubMenu from '@/components/ui/menu/ContextSubMenu.vue'
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import { calculatedColumns, generateAutoTitle } from '@/utils/calcs.js'
import { useAppStore } from '@/stores/app.js'
import { pluralize, range } from '@/utils/helpers.js'
import AggregateContextMenu from '@/components/menus/AggregateContextMenu.vue'
import RowContextMenu from '@/components/menus/RowContextMenu.vue'
import ColumnContextMenu from '@/components/menus/ColumnContextMenu.vue'
import RangeContextMenu from '@/components/menus/RangeContextMenu.vue'
import DataHeroGrid from '@/components/datahero/DataHeroGrid.vue'
import Big from 'big.js'
import { TableHistory, TableOperation } from '@/utils/grid/history'

import {
  calculatePasteRange,
  clearCellsFromSelection,
  getGridDataFromSelection,
  validatePasteSelection
} from '@/utils/grid/grid.js'
import { aggregates } from '@/utils/aggregation.js'
import { processPasteData } from '@/utils/parse.js'
import { colIsAuto } from '@/utils/grid/columns.js'
import FilterContextMenu from '@/components/menus/FilterContextMenu.vue'
import ConfirmDialog from '@/components/modal/ConfirmDialog.vue'
import { filterQuerySpec, getChartQuerySpec, getQuerySpec } from '@/db/query/utils.js'
import { detect } from '@/utils/detect.js'

function cellValuesEqual (a, b) {
  // If either value is null or empty string, treat them as equal
  if ((a === null || a === '') && (b === null || b === '')) {
    return true
  }
  // If either value is an instance of Big, compare as strings
  return ((a instanceof Big) ? a.toString() : a) === ((b instanceof Big) ? b.toString() : b)
}

export default {
  name: 'TableEditor',
  components: {
    ConfirmDialog,
    FilterContextMenu,
    DataHeroGrid,
    RangeContextMenu,
    ColumnContextMenu,
    RowContextMenu,
    AggregateContextMenu,
    ModalCalculatedColumn,
    MenuItemGroup,
    ContextSubMenu,
    ModalEditColumn,
    ContextMenu,
    MenuItemButton
  },
  emits: ['save-chart'],
  props: {
    viewId: {
      type: String,
      required: false
    },
    viewingChart: {
      type: Boolean,
      required: false
    }
  },
  computed: {
    ...mapWritableState(useAppStore, {
      appStoreSelectedColumnIds: 'selectedColumnIds',
      appStoreSelectedRowIds: 'selectedRowIds',
      appStoreSelectedCells: 'selectedCells'
    }),
    ...mapWritableState(useTableStore, {
      tableStoreViewRowCount: 'viewRowCount',
      tableStoreLastUpdate: 'lastUpdate'
    }),
    ...mapState(useChartStore, {
      chartStoreChartedColumns: 'chartedColumns',
      chartStoreChartedRows: 'chartedRows',
      chartStoreLabelColumnId: (store) => store.data.axis.column
    }),
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreLabelColumnId: 'labelColumnId',
      tableStoreLeftPinIndex: 'leftPinIndex',
      tableStoreRightPinIndex: 'rightPinIndex',
      tableStoreDbTable: 'dbTable',
      tableStoreColumns: 'columns',
      tableStoreViews: 'views'
    }),

    columns () {
      return this.tableStoreGetViewColumns(this.viewId)
    },

    querySpec () {
      const view = this.tableStoreViews.find(v => v.id === this.viewId)
      if (this.viewingChart) {
        return getChartQuerySpec(this.tableStoreColumns, view)
      } else {
        return getQuerySpec(this.tableStoreColumns, view)
      }
    },

    isFiltered () {
      // Return true if any columns have a filter set
      return !this.viewId && this.columns.some(col => col.filters?.length > 0)
    }

  },

  // on mount, mount a document event for copy events. Remove on dismount. We couldn't get @copy event
  // to work reliably (requiring interactivity first like seeing a context menu before it would fire) -
  // couldn't get to the bottom of it, but this works reliably. In copyToClipboard we check the active
  // element to see if it's an ag-grid element, and if not, we let the event bubble.
  mounted () {
    document.addEventListener('copy', this.copyToClipboard)
    document.addEventListener('cut', this.cutToClipboard)
    document.addEventListener('paste', this.handlePaste)
    document.addEventListener('keydown', this.handleKeyDown)
  },

  beforeUnmount () {
    document.removeEventListener('copy', this.copyToClipboard)
    document.removeEventListener('cut', this.cutToClipboard)
    document.removeEventListener('paste', this.handlePaste)
    document.removeEventListener('keydown', this.handleKeyDown)
  },

  data () {
    return {
      dataSource: {},
      history: new TableHistory(),
      showGrid: false,
      lockInsertion: false
    }
  },

  watch: {
    viewingChart () {
      // Whenever this changes, refresh grid columns
      this.setGridColumns()
    }
  },

  // set up a watch for columns being changed from an array to an object and throw an error!
  // trying to get to bottom of where this is happening!
  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetColumnPosition: 'getColumnPosition',
      tableStoreMoveColumn: 'moveColumn',
      tableStoreUnpinColumn: 'unpinColumn',
      tableStorePinColumn: 'pinColumn',
      tableStoreResizeColumn: 'resizeColumn',
      tableStoreDiffRows: 'diffRows',
      tableStoreClearColumnAggregate: 'clearColumnAggregate',
      tableStoreViewIsValid: 'viewIsValid',
      tableStoreChangeColumn: 'changeColumn',
      tableStoreMoveRows: 'moveRows',
      tableStoreInsertRows: 'insertRows',
      tableStoreInsertColumn: 'insertColumn',
      tableStoreInsertCalcColumn: 'insertCalcColumn',
      tableStoreChangeCalcColumn: 'changeCalcColumn',
      tableStoreDeleteColumns: 'deleteColumns',
      tableStoreSetAggregate: 'setColumnAggregate',
      tableStoreDeleteRows: 'deleteRows',
      tableStoreUpdateViewGroupOrColumn: 'updateViewGroupOrColumn',
      tableStoreSetViewSortToTop: 'setViewSortToTop',
      tableStoreGetViewColumns: 'getViewColumns',
      tableStoreGetQuerySpec: 'getQuerySpec',
      tableStoreGetColumnAutoHeader: 'getColumnAutoHeader'
    }),
    ...mapActions(useTableStore, {
      tableStoreSaveColumns: 'saveColumns',
      tableStoreSaveColumn: 'saveColumn'
    }),
    ...mapActions(useNotificationStore, {
      notificationStoreInfo: 'info',
      notificationStoreError: 'error'
    }),

    async setGridColumns (skipRefetch = false) {
      this.hoverColumnId = null

      if (!skipRefetch) {
        // Strange place to put this and to reset it each time, but nowhere else seems to make sense
        if (this.viewId && !this.tableStoreViewIsValid(this.viewId)) {
          console.warn(`View ${this.viewId} is invalid.`)
          this.dataSource = undefined
          this.showGrid = false
        } else {
          this.dataSource = { getRows: this.getRows }
          this.showGrid = true
          await this.refetch()
        }
      }
      this.$refs.grid.setColumns(this.columns)
    },

    getColumn (colId) {
      // We implement our own getColumn function to handle the view case
      return this.columns.find(col => col.id === colId)
    },

    async getRows ({ startRow, endRow, successCallback }) {
      const limitedSpec = filterQuerySpec(this.querySpec, {
        output: {
          limit: endRow - startRow,
          offset: startRow
        }
      })
      const [grid, count] = await Promise.all([
        this.tableStoreDbTable.rowObjects(this.columns.map(col => col.id), limitedSpec),
        this.tableStoreDbTable.count(this.querySpec)
      ])

      // Update the store row count, allowing other components to watch
      this.tableStoreViewRowCount = count

      // If we're in debug, check all the rows in grid. If we have any empty Ids or duplicate ids, warn to console
      if (process.env.NODE_ENV === 'development') {
        const ids = grid.map(row => row.id)
        if (ids.includes(undefined)) {
          console.warn('getRows: Found undefined row id in grid')
        } else if (ids.includes(null)) {
          console.warn('getRows: Found null row id in grid')
        } else if (ids.length !== new Set(ids).size) {
          console.warn('getRows: Found duplicate row ids in grid')
        }
      }

      // Ag-grid's callback
      successCallback(grid, count)
    },

    async getAggregates () {
      const cols = this.columns
          // only columns with aggregates
          .filter(col => col.aggregate)

          // make sure this aggregate is valid for this type
          .filter(col => !aggregates[col.aggregate].limitTo || aggregates[col.aggregate].limitTo.includes(col.type))

      const stats = cols.map(col => this.tableStoreDbTable.aggregate(col.id, [col.aggregate], this.querySpec))
      const vals = await Promise.all(stats)
      return vals.reduce((acc, res, index) => ({
        ...acc,
        [`c${cols[index].id}`]: Object.values(res)[0]
      }), {})
    },

    updateAggregates () {
      this.getAggregates().then(aggs => {
        this.$refs.grid.setAggregateRow(aggs)
      })
    },

    showCalcColumnEditor (colId, type, sourceCols) {
      const col = (!colId) ? undefined : this.getColumn(colId)
      this.$refs.calcColumnEditor.open(col, { type, sourceCols })
    },

    columnRightClick (e) {
      this.$refs.columnContextMenu.showMenu(e, this.appStoreSelectedColumnIds, this.viewId)
    },

    /**
     * Copies selected grid data to clipboard
     * Only processes if focus is within grid
     */
    async copyToClipboard (e, clearSelection = true) {
      // Ensure we're focused within the grid before processing
      if (!document.activeElement.className.startsWith('ag-')) {
        return
      }
      const data = await getGridDataFromSelection({
        columnIds: this.appStoreSelectedColumnIds,
        rowIds: this.appStoreSelectedRowIds,
        cellRange: this.appStoreSelectedCells,
        cell: this.$refs.grid.getFocusedCell(),
        columns: this.columns,
        dbTable: this.tableStoreDbTable,
        querySpec: this.querySpec,
        skipHeaders: true  // we skip headers so col copy/paste works as expected
      })
      if (data.length > 0) {
        e.preventDefault()
        const csv = data.map((row) => row.join('\t')).join('\n')
        await navigator.clipboard.writeText(csv)
        if (clearSelection) {
          this.clearSelection()
        }
      }
    },

    /**
     * Cuts selected grid data to clipboard
     * Combines copy operation with deletion
     */
    async cutToClipboard (e) {
      if (!document.activeElement.className.startsWith('ag-')) return

      if (this.viewId) {
        // Just copy if we're read-only
        await this.copyToClipboard(e)

      } else {
        await this.copyToClipboard(e, false)
        const operation = await clearCellsFromSelection({
          columnIds: this.appStoreSelectedColumnIds,
          rowIds: this.appStoreSelectedRowIds,
          cellRange: this.appStoreSelectedCells,
          cell: this.$refs.grid.getFocusedCell(),
          columns: this.columns,
          dbTable: this.tableStoreDbTable,
          querySpec: this.querySpec
        })

        if (operation && operation.cells.length) {
          await this.executeOperation(operation)
          this.notificationStoreInfo(`Cut ${operation.cells.length} ${pluralize('cell', operation.cells.length)} to clipboard`, 'success')
        }
      }
      this.clearSelection()
    },

    /**
     * Handles deletion of selected data
     * Routes to appropriate deletion method based on selection type
     */
    async deleteSelection () {
      // These first two are destructive and don't save to history
      if (this.appStoreSelectedColumnIds.length) {
        await this.deleteColumns(this.appStoreSelectedColumnIds)

      } else if (this.appStoreSelectedRowIds.length) {
        await this.deleteRows(this.appStoreSelectedRowIds)

      } else {
        // Range and cell selection run through clearCellsFromSelection and save to history
        // Clear values in cell range
        const operation = await clearCellsFromSelection({
          cellRange: this.appStoreSelectedCells,
          cell: this.$refs.grid.getFocusedCell(),
          columns: this.columns,
          dbTable: this.tableStoreDbTable,
          querySpec: this.querySpec
        })
        await this.executeOperation(operation)
      }
      this.clearSelection()
    },

    async changeCell ({ colId, rowData, rowId, oldValue: oldVal, newValue: newVal }) {
      if (cellValuesEqual(oldVal, newVal)) return
      rowId = rowId ?? rowData.id
      const cells = [{ rowId, rowIndex: rowData.pos, colId, oldVal, newVal }]
      await this.createAndExecuteOperation('cellEdit', cells)
    },

    /**
     * Executes a table operation and updates the store
     * @param operation - Operation object to use instead of creating a new one
     * @param isUndo - whether this is an undo operation
     * @param addToHistory - whether to add this operation to the history stack
     * @returns {Promise<void>}
     */
    async executeOperation (operation, isUndo = false, addToHistory = true) {
      if (!operation?.cells.length) return
      if (addToHistory) this.history.add(operation)
      this.tableStoreDiffRows(operation.toDiff(isUndo))

      // Gotcha, tableStoreColumns has any recent changes; this is important during column type changes; note that
      // as we are doing updates, this will always be the table cols, not the view cols
      await this.tableStoreDbTable.updateRows(operation.toDbUpdates(this.tableStoreColumns, isUndo))
      this.tableStoreLastUpdate = Date.now()
      await this.refetch()
    },

    async createAndExecuteOperation (type, cells) {
      const operation = new TableOperation(type, cells)
      await this.executeOperation(operation)
      return operation
    },

    async columnMoved (event) {
      if (event.finished) {
        // Only handles single column move - could iterate over all columns in event.columns or
        // do some sort of single operation if we enable multi-col selection and reorder. We minus
        // 2 because of our 2 pinned columns for row count and row drag
        this.tableStoreMoveColumn(event.column.colDef.colId, event.toIndex - 2)
        this.tableStoreLastUpdate = Date.now()

        // while the column is already been moved at ag-grid, we need to refresh because column
        // auto-header might have changed
        await this.setGridColumns(true)

        // Column move resets edit history
        this.history.clear()
      }
    },

    columnResized (event) {
      if (event.finished) {
        event.columns.forEach(col => {
          if (this.viewId) {
            this.tableStoreUpdateViewGroupOrColumn(this.viewId, col.colDef.colId, { width: col.actualWidth })
          } else {
            this.tableStoreResizeColumn(col.colDef.colId, col.actualWidth)
          }
        })
      }
    },

    async setColumnAggregate (colId, key) {
      if (this.viewId) {
        await this.tableStoreUpdateViewGroupOrColumn(this.viewId, colId, { aggregate: key })
      } else {
        if (key === undefined) {
          this.tableStoreClearColumnAggregate(colId)
        } else {
          await this.tableStoreSetAggregate(colId, key)
        }
      }
      await this.setGridColumns(true)
      this.updateAggregates()
    },

    showCellContextMenu (e) {

      // Even if we cancel, lets not show the default context menu
      e.preventDefault()

      // Check the event target. If it is a DIV with an attribute `data-ref` of value 'eViewport' then
      // we're clicking outside a cell and we abort
      if (e.target.getAttribute('data-ref') === 'eViewport') {
        return
      }

      const cell = this.$refs.grid.getFocusedCell()
      if (!cell) return
      if (cell.rowPinned === 'bottom') {
        // If we're on the totals row, select the col and show the aggregate context menu
        if (colIsAuto(cell.colId)) return
        const col = this.getColumn(cell.colId)
        this.$refs.aggregateContextMenu.showMenu(e, col)
        return
      }

      if (this.appStoreSelectedCells) {
        this.$refs.rangeContextMenu.showMenu(e, this.appStoreSelectedCells, this.viewId)
        return
      }

      if (this.appStoreSelectedColumnIds.includes(cell.colId)) {
        // If the column is selected, and we're in that column, show the col context menu
        this.$refs.columnContextMenu.showMenu(e, this.appStoreSelectedColumnIds, this.viewId)

      } else {

        // Switch to the row that was clicked if that row isn't already selected
        this.$refs.grid.clearSelection('columns')
        if (!this.appStoreSelectedRowIds.includes(cell.rowId)) {
          this.$refs.grid.selectRow(cell.rowId, true)
          this.appStoreSelectedRowIds = [cell.rowId]
        }
        if (this.appStoreSelectedRowIds.length) {
          this.$refs.rowContextMenu.showMenu(e, this.appStoreSelectedRowIds, this.viewId)
        }
      }
    },

    clearSelection () {
      this.$refs.grid.clearSelection('columns', 'rows', 'range')
    },

    async jumpToCell (colId, rowId) {
      this.clearSelection()
      if (rowId === undefined) {
        // If no row, then just select the column - occurs on scatter
        this.appStoreSelectedColumnIds = [colId]
      } else if (colId === undefined) {
        // If no column, then just select the row - also occurs on scatter
        this.$refs.grid.selectRow(rowId, true)
      } else {
        // select the cell - note that we use grid's row index - we use a chart queryset
        // to strip out table filtering
        const view = this.tableStoreViews.find(v => v.id === this.viewId)
        const qs = getChartQuerySpec(this.tableStoreColumns, view)
        const rowIndexes = await this.tableStoreDbTable.rowIndexes([rowId], qs)
        this.$refs.grid.setFocusedCell(rowIndexes[0], colId)
      }
    },

    async insertAndFocus (rowIndex, colId) {
      if (this.lockInsertion) return
      if (this.isFiltered) return
      this.lockInsertion = true
      await this.insertRows(rowIndex, 1)
      await this.refetch()
      this.$refs.grid.$el.focus()
      this.$refs.grid.setFocusedCell(rowIndex, colId)
      this.lockInsertion = false
    },

    async insertAndEdit (rowIndex, colId) {
      if (this.lockInsertion) return
      if (this.isFiltered) return
      this.lockInsertion = true
      await this.insertRows(rowIndex, 1)
      this.$refs.grid.$el.focus()
      this.$refs.grid.setFocusedCell(rowIndex, colId, true)
      this.lockInsertion = false
    },

    async editDown (cell) {
      if (cell.rowIndex !== this.tableStoreViewRowCount - 1) {
        this.$refs.grid.setFocusedCell(cell.rowIndex + 1, cell.colId, false)
      }
    },

    async moveRows (rowIds, startIndex) {
      // Move rows to a new index
      await this.tableStoreDbTable.moveRows(rowIds, startIndex)
      this.tableStoreMoveRows(rowIds, startIndex)
      this.history.clear()
      await this.refetch()
    },

    async refetch () {
      if (this.$refs.grid) {
        await this.$refs.grid.refetch()
        this.updateAggregates()
        this.tableStoreLastUpdate = Date.now()
      }
    },

    clearFilters () {
      // for each column in tableStoreColumns, set filters to []
      this.tableStoreColumns.forEach(col => {
        col.filters = []
      })
      this.setGridColumns()
      this.tableStoreSaveColumns()
    },

    async insertAtSelection (above, numRows) {
      if (this.isFiltered) {
        this.$refs.clearFiltersDialog.open()
        return
      }

      // Insert numRows blank rows either above the top of the selection, or below the bottom of it
      this.clearSelection()
      const rowIndexes = await this.tableStoreDbTable.rowIndexes(this.appStoreSelectedRowIds, this.querySpec)
      const insertIndex = above ? Math.min(...rowIndexes) : Math.max(...rowIndexes) + 1
      await this.insertRows(insertIndex, numRows)

    },

    async insertRows (insertIndex, numberOfRows) {
      // Insert a number of rows at a given index
      const nextId = await this.tableStoreDbTable.nextId()

      // Create an array of length numberOfRows of objects each with key { id: nextId + i }
      const newRows = range(nextId, nextId + numberOfRows - 1).map(id => ({ id }))
      this.history.clear()
      await this.tableStoreDbTable.insertRows(newRows, insertIndex)
      await this.refetch()
      this.tableStoreInsertRows(newRows, insertIndex)
    },

    async deleteSelectedRows () {
      // Delete selected rows
      await this.deleteRows(this.appStoreSelectedRowIds)
    },

    async deleteRows (rowIds) {
      this.history.clear()
      await this.tableStoreDbTable.deleteRows(rowIds)
      this.tableStoreDeleteRows(rowIds)
      if (this.tableStoreViewRowCount === 0) {
        await this.insertRows(0, 1)
      }
      await this.refetch()
    },

    async deleteColumns (colIds) {
      this.clearSelection()

      // Update tablestore columns by filtering them out. Make sure there's one blank
      this.history.clear()
      const promises = colIds
          .filter(id => this.columns.find(c => c.id === id).calc === undefined)
          .map(colId => this.tableStoreDbTable.removeColumn(colId))
      await Promise.all(promises)
      await this.tableStoreDeleteColumns(colIds)

      // If there are no columns, add a blank one; make sure deletions happen first
      // otherwise we get an id clash
      if (this.columns.length === 0) {
        const col = this.tableStoreInsertColumn(0, 'text')
        await this.tableStoreDbTable.addColumn(col.id, 'text')
      }
      await this.setGridColumns()
    },

    async insertColumn (insertIndex, colType) {
      this.history.clear()
      const col = this.tableStoreInsertColumn(insertIndex, colType || 'text')
      await this.tableStoreDbTable.addColumn(col.id, col.type)
      await this.setGridColumns()
      return col
    },

    // Update the column insertion after selection method
    async insertColumnAtSelection (insertAfterSelection, colType) {
      // get the max position of appStoreSelectedColumnIds
      const positions = this.appStoreSelectedColumnIds.map(id => this.tableStoreGetColumnPosition(id))
      const insertIndex = insertAfterSelection ? Math.max(...positions) + 1 : Math.min(...positions)
      const addColumnPromises = positions.map(() => {
        return this.insertColumn(insertIndex, colType || 'text')
      })
      await Promise.all(addColumnPromises)
    },

    changeColumnPin (colId, pin) {
      // This is a manual choice to pin; we have to pin it in the grid and set in the store
      const col = this.getColumn(colId)
      const colPosition = this.tableStoreGetColumnPosition(colId)

      if (col.pin !== pin) {
        if (pin === 'n') {
          // Move column inbetween the 'unpinned' section of the grid
          this.$refs.grid.pinColumn(colId, null)

          // minus or add one because at this point it is still itself taking up a pinned position
          if (colPosition < this.leftPinIndex) {
            this.tableStoreMoveColumn(colId, this.tableStoreLeftPinIndex - 1)
          } else if (colPosition > this.tableStoreRightPinIndex) {
            this.tableStoreMoveColumn(colId, this.tableStoreRightPinIndex + 1)
          }
          this.tableStoreUnpinColumn(colId)

        } else {
          this.$refs.grid.pinColumn(colId, pin === 'r' ? 'right' : 'left')

          // Move column inbetween the 'pinned' section of the grid on the edge of the left or right
          // do this first before pinning otherwise it checks itself!
          if (pin === 'l' && colPosition > this.tableStoreLeftPinIndex) {
            this.tableStoreMoveColumn(colId, this.tableStoreLeftPinIndex)
          } else if (pin === 'r' && colPosition < this.tableStoreRightPinIndex) {
            this.tableStoreMoveColumn(colId, this.tableStoreRightPinIndex)
          }
          this.tableStorePinColumn(colId, pin === 'r')
        }
        this.setGridColumns(true)
      }

    },

    columnPinned (event) {
      // This occurs when user pins a column through drag; it's already updated in the grid,
      // we just need to reflect in the store
      if (event.pinned === null) {
        this.tableStoreUnpinColumn(event.column.colDef.colId)
      } else {
        this.tableStorePinColumn(event.column.colDef.colId, event.pinned === 'right')
      }
    },

    async changeColumn (col, origCol) {
      if (this.viewId) {
        if (!col.isGroup && col.id === 'id') {
          col.summarize = 'count'
        }
        await this.tableStoreUpdateViewGroupOrColumn(this.viewId, origCol.id, col)
      } else {
        if (this.getColumn(col.id).type !== col.type) {
          col.props = getDefaultColProps(col.type)
          await this.changeColumnType(col.id, col.type, true)
        }
        this.tableStoreChangeColumn(col)
      }
      this.history.clear()
      await this.setGridColumns()
    },

    async changeColumnType (colId, newType, skipApi = false) {
      /*
      Changes a column from one type to another.

      This deals with changing all converting all values at the DB level, updating the column type
      in the database, and refreshing the table view. If !skipApi, then we also update the API.
       */
      const rowObjects = await this.tableStoreDbTable.rowObjects([colId], this.querySpec)
      const col = this.tableStoreColumns.find(c => c.id === colId)

      // If we're converting to at type with props, we run it through detect to sniff the best
      // props for conversion. We get an array of all repr values and pass that to detect.
      const detectProps = (newType) => {
        const colRepr = rowObjects.map(row => repr(row[`c${colId}`], col.type, col.props)).filter(Boolean)
        return detect[newType](colRepr)[0].props
      }

      // Try converting to our given value - if we're converting to a text, we take a repr
      // (for text). Otherwise, convert with our sniffed props. If either are null, we return
      // the original value, so we retain error values.
      const convertValue = (value, type, props, newType, newProps) => {
        const val = (newType === 'text')
                    ? repr(value, type, props)
                    : convert(value, newType, newProps)
        return val ?? value
      }

      // For those with a prop (datetime, currency) we sniff the best format to convert to
      const newProps = (columnTypes[newType].hasProps)
                       ? detectProps(newType)
                       : getDefaultColProps(newType)

      // Map data to cell change operations; filter out any nulls - column is deleted and
      // rebuilt, so we get null by default
      const cellOps = rowObjects.map(row => ({
        rowId: row.id,
        rowIndex: row.pos,
        colId: colId,
        oldVal: row[`c${colId}`],
        newVal: convertValue(row[`c${colId}`], col.type, col.props, newType, newProps)
      }))

      // Update the column type and props
      col.type = newType
      col.props = newProps

      // Change type, set default props and update the database. Manipulate table column directly.
      // TODO need to skip all the stuff above?!
      if (!col.calc) {
        await this.tableStoreDbTable.changeColumn(colId, newType)
      }
      await this.executeOperation(new TableOperation('changeColumn', cellOps), false, false)
      this.history.clear()

      if (!skipApi) {
        await this.tableStoreChangeColumn(col)
        await this.setGridColumns()
      }
    },

    async insertCalcColumn (calc, sourceCols, data, title) {
      if (typeof calc === 'string') {
        calc = calculatedColumns[calc]
      }
      // get max position of appStoreSelectedColumnIds
      const pos = Math.max(...this.appStoreSelectedColumnIds.map(id => this.tableStoreGetColumnPosition(id))) + 1

      // Generate an auto title if one not given
      title = title ?? generateAutoTitle(calc, sourceCols.map(colId => this.tableStoreGetColumnAutoHeader(colId)))

      // Insert the column
      this.tableStoreInsertCalcColumn(pos, calc, sourceCols, data, title)
      this.history.clear()
      await this.setGridColumns()
    },

    async changeCalcColumn (colId, calc, sourceCols, data, title) {
      if (typeof calc === 'string') {
        calc = calculatedColumns[calc]
      }
      await this.tableStoreChangeCalcColumn(colId, calc, sourceCols, data, title)
      await this.setGridColumns()
    },

    async setViewSort ({ colId, desc }) {
      this.tableStoreSetViewSortToTop(this.viewId, colId, desc)
      await this.setGridColumns()
    },

    async createChart (chartType, series, values, byRow = false, rows = undefined) {

      await this.$router.push({
        name: `app-${this.viewId ? 'view' : 'table'}-charts`,
        params: { tableId: this.tableStoreId }
      })

      // Create a simple chart using the table's labelColumnId
      const chartStore = useChartStore()
      chartStore.$reset()

      let axisCol = this.tableStoreLabelColumnId
      if (this.viewId) {
        axisCol = this.columns.find(c => c.isGroup)?.id
        if (axisCol === undefined) {
          axisCol = this.tableStoreLabelColumnId
        }

      } else if (series.length === 1 && series[0] === this.tableStoreLabelColumnId) {
        // if the only series is the label column, don't set it as the axis column
        axisCol = undefined
      }

      // remove labelColumnId from series/values if it exists
      if (!byRow && series.includes(axisCol)) {
        series.splice(series.indexOf(axisCol), 1)

      } else if (byRow && values.includes(axisCol)) {
        values.splice(values.indexOf(axisCol), 1)
      }

      chartStore.view = this.viewId
      chartStore.data.axis.column = axisCol
      chartStore.data.datasets = [createDataset(chartType, series, values)]

      // Add default tension for line charts
      if (chartType === 'line') {
        chartStore.data.datasets[0].tension = 0 // default moderate curve
      }

      if (!byRow && !rows) {
        rows = await this.tableStoreDbTable.autoRowLimits(series, this.querySpec)
      }

      chartStore.data.byRow = byRow
      chartStore.data.rows = rows

      this.$emit('save-chart')
      this.clearSelection()

    },

    async handlePaste (e) {
      if (!document.activeElement.className.startsWith('ag-')) return

      if (this.isFiltered) {
        this.$refs.clearFiltersDialog.open()
        return
      }

      // Skip if view-only or not interactive
      if (this.viewId) return

      // Process paste data using our utility from parse.js
      const pasteData = processPasteData(e, true)
      if (!pasteData) return

      // Get current focused cell and validate paste operation
      const focusedCell = this.$refs.grid.getFocusedCell()
      if (focusedCell) {
        focusedCell.colIndex = this.columns.findIndex(col => col.id === focusedCell.colId)
      }

      let selectedCells = this.appStoreSelectedCells
      if (selectedCells && focusedCell && (
          focusedCell.colIndex < selectedCells.cols[0] ||
          focusedCell.colIndex > selectedCells.cols[1] ||
          focusedCell.rowIndex < selectedCells.rows[0] ||
          focusedCell.rowIndex > selectedCells.rows[1])) {
        // If focused cell is outside selected range, clear the selected range
        selectedCells = null
        this.clearSelection('range')
      }

      const params = {
        pasteData,
        focusedCell,
        selectedCells,
        selectedColumnIndexes: this.appStoreSelectedColumnIds.map(id => this.tableStoreGetColumnPosition(id)),
        selectedRowIndexes: await this.tableStoreDbTable.rowIndexes(this.appStoreSelectedRowIds, this.querySpec)
      }
      const validationResult = validatePasteSelection(params)
      if (!validationResult.valid) {
        this.notificationStoreError('Invalid paste selection.', validationResult.message)
      }
      const pasteRange = validationResult.range

      // Calculate if we need new rows/columns
      const {
        newRows, newColumns
      } = calculatePasteRange(pasteRange, this.tableStoreViewRowCount, this.columns.length)

      // Insert new rows if needed
      if (newRows > 0) {
        await this.insertRows(pasteRange.rows[1] - newRows + 1, newRows, this.querySpec)
      }

      // Insert new columns if needed
      if (newColumns > 0) {
        const insertIndex = pasteRange.cols[1] - newColumns + 1
        const insertPromises = []
        for (let i = 0; i < newColumns; i++) {
          insertPromises.push(this.insertColumn(insertIndex + i, 'text'))
        }
        await Promise.all(insertPromises)
      }

      // Get the data from the target range
      const targetData = await getGridDataFromSelection({
        cellRange: pasteRange,
        columns: this.columns,
        dbTable: this.tableStoreDbTable,
        querySpec: this.querySpec,
        skipHeaders: true,
        asObjects: true
      })

      const cellOps = []
      for (let rowIndex = pasteRange.rows[0]; rowIndex <= pasteRange.rows[1]; rowIndex++) {
        const rowOffset = rowIndex - pasteRange.rows[0]
        for (let colIndex = pasteRange.cols[0]; colIndex <= pasteRange.cols[1]; colIndex++) {
          const column = this.columns[colIndex]
          if (column.calc) continue
          const rawValue = pasteData[rowOffset][colIndex - pasteRange.cols[0]]
          const oldValue = targetData[rowOffset][`c${column.id}`]

          if (!cellValuesEqual(oldValue, rawValue)) {
            const cleanValue = convert(rawValue, column.type, column.props)
            cellOps.push({
              rowIndex,
              rowId: targetData[rowOffset].id,
              colId: column.id,
              oldVal: oldValue,
              newVal: cleanValue ?? rawValue
            })
          }
        }
      }

      if (cellOps.length > 0) {
        await this.createAndExecuteOperation('paste', cellOps)
        this.notificationStoreInfo(`Pasted ${cellOps.length} ${pluralize('cell', cellOps.length)}`, 'success')
      }
    },

    async handleKeyDown (e) {
      // Ensure we're focused within the grid before processing
      if (!document.activeElement.className.startsWith('ag-')) {
        return
      }

      // Handle undo/redo: Ctrl+Z, Cmd+Z, Ctrl+Y, Cmd+Y, Ctrl+Shift+Z, Cmd+Shift+Z
      if ((e.ctrlKey || e.metaKey) && (
          (e.key === 'z' && !e.shiftKey) ||                 // Undo: Ctrl/Cmd + Z
          (e.key === 'y') ||                                // Redo: Ctrl/Cmd + Y
          (e.key === 'z' && e.shiftKey)                     // Redo: Ctrl/Cmd + Shift + Z
      )) {
        e.preventDefault()
        const isUndo = e.key === 'z' && !e.shiftKey
        const operation = await this.history[isUndo ? 'undo' : 'redo']()
        await this.executeOperation(operation, isUndo, false)
      }
    },

    setColumnFilters (id, filters) {
      // We set the column filter manually; we don't want to do a save yet.
      this.tableStoreColumns.find(c => c.id === id).filters = filters
      this.refetch()
    },

    saveColumn (id) {
      this.tableStoreSaveColumn(id)
      this.setGridColumns(true)
    }
  }
}
</script>
