<template>
  <div class="flex-1 flex h-full items-center overflow-hidden"
       @mouseleave="params.hookHover && params.hookHover(e, undefined)">

    <template v-if="auto">
      <div
          class="ag-datahero-header flex-1 flex h-full items-center text-center cursor-pointer"
          tabindex="0"
          @click.prevent="e => params.hookLeftClick && params.hookLeftClick(e, colId)">
      </div>
    </template>

    <template v-else-if="colType">
      <div v-if="params.showColumnType" class="flex-none mr-2 cursor-pointer" :title="colType.label">
        <component :is="getColumnIcon(colType.type)" class="size-4 text-zinc-400"/>
      </div>
      <div
          class="ag-datahero-header flex-1 flex h-full items-center text-center cursor-pointer"
          tabindex="0"
          @keydown="handleKeyDown"
          @mouseover="e => params.hookHover && params.hookHover(e, colId)"
          @click.prevent="e => params.hookLeftClick && params.hookLeftClick(e, colId)"
          @contextmenu.exact.stop.prevent="e => params.hookRightClick && params.hookRightClick(e, colId)">
        <span class="flex-1">
          {{ params.displayName }}
        </span>
      </div>
      <div v-if="params.showFilter" class="flex-none ml-2 cursor-pointer" title="Filter on this column">
        <component
            :is="filterIcon"
            class="size-4 cursor-pointer"
            :class="{
              'invisible': hideFilter,
              'text-fuchsia-600 hover:text-fuchsia-400': hasFilter,
              'text-zinc-400 hover:text-zinc-600': !hasFilter
            }"
            @click.prevent="e => params.hookFilter && params.hookFilter(e, colId)"
        />
      </div>
    </template>
  </div>
</template>

<script>

/*

TableEditorHeader

This is kept purposefully minimal, because hot reload doesn't work with this component due to how ag-grid handles it.
We can't capture $emit events so we use a hacky 'hook' method, where references to functions are passed in as params.

The ColId is captured from the params as well, which is passed into these hooks so we know what col we're referencing.
 */
import { getColumnIcon } from '@/utils/colTypeComponents.js'
import { colIsAuto } from '@/utils/grid/columns.js'
import { columnTypes } from '@/utils/formats.js'

import { ListFilter, ListFilterPlus } from 'lucide-vue-next'

export default {
  name: 'TableEditorHeader',
  components: {
    ListFilter,
    ListFilterPlus
  },

  props: {
    params: Object
  },

  computed: {
    colId () {
      const colId = this.params.column.colDef.colId
      if (colIsAuto(colId)) return colId
      return parseInt(colId)
    },

    colType () {
      if (colIsAuto(this.colId)) return
      return columnTypes[this.params.column.colDef.context.type]
    },

    hasFilter () {
      return this.params.column.colDef.context.hasFilter
    },

    hideFilter () {
      return this.params.column.colDef.context.hideFilter
    },

    auto () {
      return colIsAuto(this.colId)
    },

    filterIcon () {
      if (this.hasFilter) {
        return ListFilterPlus
      }
      return ListFilter
    }
  },

  methods: {
    getColumnIcon,

    handleKeyDown (e) {
      if (e.key === 'Delete') {
        e.preventDefault()
        this.params.hookDelete && this.params.hookDelete(e, this.colId)
      }
    }
  }
}
</script>

<style scoped>

</style>
