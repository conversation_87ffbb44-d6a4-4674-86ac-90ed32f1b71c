<template>
  <div class="space-y-4">
    <sortable
        :list="groups"
        item-key="id"
        :options="sortableOptions"
        @update="$emit('group-sort-update', $event)"
    >
      <template #item="{ element: group, index }">
        <div
            :key="index"
            class="border-x border-b border-zinc-200 shadow-xs flex cursor-pointer" :class="[
              index === 0 ? 'rounded-t-lg border-t' : '',
              index === groups.length - 1 ? 'rounded-b-lg' : ''
            ]">
          <div
              class="drag-handle p-2 flex items-center cursor-move text-zinc-400 bg-zinc-100 hover:text-zinc-600">
            <GripVertical class="size-4"/>
          </div>
          <div v-if="activeGroupId === group.id" class="p-2 flex-1 flex">
            <div class="flex-1">
              <form-grid-row class="pt-0!" label="Grouping column">
                <select
                    v-model.number="group.sourceColumnId"
                    class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                  <option
                      v-for="(col, index) in tableColumns"
                      :value="col.id"
                      :key="index">{{ getColumnAutoHeader(col.id) }}
                  </option>
                </select>
              </form-grid-row>
              <form-grid-row v-if="columnGroupBys(group.sourceColumnId).length" label="Group by">
                <select
                    v-model="group.by"
                    class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                  <option :value="undefined">Exact value</option>
                  <option
                      v-for="grouping in columnGroupBys(group.sourceColumnId)"
                      :value="grouping.id"
                      :key="grouping.id">{{ grouping.label }}
                  </option>
                </select>
              </form-grid-row>
              <form-grid-row v-if="group.sourceColumnId !== undefined" label="Customize header">
                <placeholder-input
                    v-model="group.header"
                    :placeholder="getColumnAutoHeader(group.sourceColumnId)"/>
              </form-grid-row>
            </div>
            <div class="shrink-0">
              <X
                  class="size-6 ml-4 mt-2 text-zinc-500 hover:text-red-500 cursor-pointer"
                  @click="$emit('remove-group', index)"/>
            </div>
            <div class="shrink-0">
              <CheckCircle
                  v-if="group.sourceColumnId !== undefined"
                  class="size-6 ml-2 mt-2 text-zinc-500 hover:text-green-500 cursor-pointer"
                  @click="$emit('toggle-active-group', undefined)"/>
            </div>
          </div>
          <div v-else
               class="p-2 grow text-sm hover:bg-zinc-50"
               @click="$emit('toggle-active-group', group.id)">
            {{ group.header || getColumnAutoHeader(group.sourceColumnId) }}
          </div>
        </div>
      </template>
    </sortable>
    <base-btn :icon-component="Plus" @click="$emit('add-group')">Add grouping column to the report...</base-btn>
  </div>
</template>

<script>
import { markRaw } from 'vue'
import { GripVertical, Plus, X, CheckCircle } from 'lucide-vue-next'
import { Sortable } from 'sortablejs-vue3'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import PlaceholderInput from '@/components/forms/input/PlaceholderInput.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { dateGrouping } from '@/db/grouping.js'

export default {
  name: 'GroupsTab',
  emits: ['group-sort-update', 'remove-group', 'toggle-active-group', 'add-group'],
  components: {
    Sortable,
    FormGridRow,
    PlaceholderInput,
    BaseBtn,
    GripVertical,
    X,
    CheckCircle
  },
  props: {
    groups: {
      type: Array,
      required: true
    },
    activeGroupId: {
      type: [String, Number],
      default: undefined
    },
    tableColumns: {
      type: Array,
      required: true
    },
    getColumnAutoHeader: {
      type: Function,
      required: true
    },
    sortableOptions: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      Plus: markRaw(Plus)
    }
  },
  methods: {
    columnGroupBys(sourceColumnId) {
      const col = this.tableColumns.find(col => col.id === sourceColumnId)
      return (col?.type === 'datetime') ? Object.values(dateGrouping) : []
    }
  }
}
</script>
