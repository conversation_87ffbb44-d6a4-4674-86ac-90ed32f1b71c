<template>
  <div>
    <template v-for="(type, key, index) in viewTypes">
      <div
          v-if="!type.disable"
          :key="index"
          class="p-6 border border-zinc-200 shadow-xs flex cursor-pointer hover:bg-zinc-50"
          :class="[
            viewType === key ? 'border-fuchsia-500' : 'bg-white',
            index === 0 ? 'rounded-t-lg' : '',
            index === Object.values(viewTypes).filter(t => !t.disable).length - 1 ? 'rounded-b-lg' : ''
          ]"
          @click="$emit('set-view-type', key)"
      >
        <div class="shrink-0">
          <component :is="type.icon" class="size-8 mt-2 text-fuchsia-500"/>
        </div>

        <div class="ml-4">
          <h3 class="font-semibold">{{ type.label }}</h3>
          <p class="text-sm text-zinc-600">{{ type.description }}</p>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { viewTypes } from '@/db/grouping.js'

export default {
  name: 'ViewTypeTab',
  emits: ['set-view-type'],
  props: {
    viewType: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      viewTypes
    }
  }
}
</script>
