<template>
  <base-modal ref="base" title="Export table">
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <a v-for="(format, key) in formats" :key="key" @click="selectedFormat = key"
           :class="['cursor-pointer', selectedFormat === key ? 'border-fuchsia-500 text-fuchsia-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm']">
          {{ format.name }}
        </a>
      </nav>
    </div>
    <base-toggle label="Raw data representations" v-model="rawOutput"/>
    <div class="mt-4">
      <textarea
          readonly
          class="w-full h-72 text-xs whitespace-pre overflow-x-auto break-normal border border-gray-300 rounded-md font-mono"
          :value="output"></textarea>
    </div>

    <template #buttons>
      <base-btn
          :disabled="copyStatus === 'copying'"
          is-submit
          type="primary"
          @click="copyToClipboard"
          :icon-component="copyStatus === 'copied' ? CheckCircle : Clipboard"
          :icon-classes="copyStatus === 'copied' ? 'text-green-300' : 'text-white'"
      >{{ copyStatus === 'copied' ? 'Copied' : 'Copy to Clipboard' }}
      </base-btn>
      <base-btn @click="downloadToFile" :icon-component="ArrowDownToLine">Download to File</base-btn>
      <base-btn ref="submit" @click="$refs.base.close">Close</base-btn>
    </template>
  </base-modal>
</template>
<script>
import { mapActions, mapState } from 'pinia'
import { toRaw } from 'vue'
import BaseModal from '@/components/modal/BaseModal.vue'
import { formats } from '@/utils/tableExport'
import { useTableStore } from '@/stores/table.js'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { ArrowDownToLine, CheckCircle, Clipboard } from 'lucide-vue-next'

export default {
  components: {
    BaseBtn,
    BaseToggle,
    BaseModal
  },
  data () {
    return {
      viewId: undefined,
      ArrowDownToLine,
      Clipboard,
      CheckCircle,
      formats,
      rawRows: undefined,
      rawOutput: false,
      copyStatus: '',
      selectedFormat: 'csv'
    }
  },

  watch: {
    rawOutput: 'setRawData'
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreDbTable: 'dbTable'
    }),

    format () {
      return this.formats[this.selectedFormat]
    },

    output () {
      if (!this.rawRows || this.rawRows.length === 0) {
        return
      }
      const header = toRaw(this.columns.map(c => c.header))
      const rows = this.rawRows
      const data = [header].concat(rows)
      return this.format.output({ header, rows, data })
    },

    columns () {
      return this.tableStoreGetViewColumns(this.viewId)
    }

  },
  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetViewColumns: 'getViewColumns',
      tableStoreGetQuerySpec: 'getQuerySpec'
    }),

    open (viewId) {
      this.viewId = viewId
      this.$refs.base.open()
      this.setRawData()
    },

    async setRawData () {
      this.rawRows = await this.tableStoreDbTable.rowArray(this.columns.map(col => col.id), this.tableStoreGetQuerySpec(this.viewId), !this.rawOutput)
    },

    copyToClipboard () {
      this.copyStatus = 'copying'
      navigator.clipboard.writeText(this.output).then(() => {
        this.copyStatus = 'copied'
        setTimeout(() => {
          this.copyStatus = ''
        }, 2000)
      })
    },
    downloadToFile () {
      const blob = new Blob([this.output], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `table.${this.format.extension}`
      a.click()
      URL.revokeObjectURL(url)
    }
  }
}
</script>


<style scoped>

</style>
