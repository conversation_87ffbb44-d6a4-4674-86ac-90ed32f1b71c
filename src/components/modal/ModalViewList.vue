<template>
  <base-modal ref="base" title="Your Views">
    <sortable
        :list="tableStoreViews"
        item-key="id"
        :options="sortableOptions"
        @end="handleSort"
        class="max-h-96"
    >
      <template #item="{ element: view, index }">
        <div
            class="flex items-center justify-between p-4 border border-zinc-200 -mt-[1px] hover:bg-zinc-50 group"
            :class="[
              index === 0 ? 'rounded-t-md' : '',
              index === tableStoreViews.length - 1 ? 'rounded-b-md' : ''
            ]">
          <div class="flex items-center">
            <div class="hidden drag-handle cursor-move text-zinc-400 hover:text-zinc-600 mr-2">
              <GripVertical class="size-4"/>
            </div>
            <component :is="getViewIcon(view)" class="size-5 mr-2 text-zinc-400"/>
            <router-link
                :to="{ name: 'app-view', params: { tableId: tableStoreId, viewId: view.id }}"
                @click="$refs.base.close"
                class="text-sm"
            >
              {{ view.title }}
            </router-link>
          </div>

          <div class="flex items-center">
            <button
                title="Edit this view"
                class="cursor-pointer text-zinc-400 hover:text-fuchsia-500 p-1"
                @click="editView(view)"
            >
              <Pencil class="size-5" aria-hidden="true"/>
            </button>
            <button
                title="Copy this view"
                class="cursor-pointer text-zinc-400 hover:text-fuchsia-500 p-1"
                @click="copyView(view)"
            >
              <Copy class="size-5" aria-hidden="true"/>
            </button>
            <button
                title="Delete this view"
                class="cursor-pointer text-zinc-400 hover:text-red-500 p-1"
                @click="selectedViewId = view.id; $refs.deleteViewDialog.open()"
            >
              <Trash class="size-5" aria-hidden="true"/>
            </button>
          </div>
        </div>
      </template>
    </sortable>

    <template #buttons>
      <base-btn is-submit type="primary" @click.prevent="$refs.base.close()">Close</base-btn>
      <div class="flex-1">
        <base-btn :icon-component="Plus" is-text @click="addView">Add New View</base-btn>
      </div>
    </template>
  </base-modal>

  <teleport to="body">
    <confirm-dialog
        title="Delete View"
        text="Are you sure you want to delete this view?"
        ref="deleteViewDialog"
        @confirm="$emit('delete-view', selectedViewId)"
    />
  </teleport>
</template>

<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { mapState } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import { Copy, GripVertical, Pencil, Plus, Trash } from 'lucide-vue-next'
import ConfirmDialog from '@/components/modal/ConfirmDialog.vue'
import { Sortable } from 'sortablejs-vue3'
import { getViewIcon } from '@/db/grouping.js'

export default {
  name: 'ModalViewList',
  components: {
    ConfirmDialog,
    Trash,
    Pencil,
    Copy,
    GripVertical,
    BaseModal,
    BaseBtn,
    Sortable
  },
  emits: ['delete-view', 'edit-view', 'add-new-view', 'add-view'],

  data () {
    return {
      selectedViewId: null,
      Plus,
      sortableOptions: {
        disabled: true,
        handle: '.drag-handle',
        animation: 150,
        ghostClass: 'sortable-ghost',
        dragClass: 'sortable-drag'
      }
    }
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreViews: 'views'
    })
  },

  methods: {
    getViewIcon,

    open () {
      this.$refs.base.open()
    },

    handleSort (event) {
      // Nothing actually to do as yet, because views on the API don't have a sort order!
      // const { oldIndex, newIndex } = event
    },

    addView () {
      this.$refs.base.close()
      this.$emit('add-new-view')
    },

    editView (view) {
      this.$refs.base.close()
      this.$emit('edit-view', view)
    },

    deleteView (viewId) {
      this.$refs.base.close()
      this.$emit('delete-view', viewId)
    },

    copyView (view) {
      const viewCopy = JSON.parse(JSON.stringify(view))
      delete viewCopy.id
      viewCopy.title = `${view.title} (Copy)`
      this.$emit('add-view', viewCopy)
    }
  }
}
</script>

<style scoped>
.drag-handle {
  touch-action: none;
}
</style>
