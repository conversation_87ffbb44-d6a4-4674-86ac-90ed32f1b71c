<template>
  <base-modal
      ref="base"
      small
      :title="title"
      @submit="submit">
    <template #body>
      <div
          class="mx-auto flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
        <TriangleAlert class="h-6 w-6 text-red-600" aria-hidden="true"/>
      </div>
      <div class="flex-1 mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
        <dialog-title as="h3" class="text-base font-semibold leading-6 text-gray-900">{{ title }}</dialog-title>
        <div class="mt-2">
          <p class="text-sm text-gray-500">{{ text }}</p>
        </div>
      </div>
    </template>

    <template #buttons>
      <base-btn ref="confirmButtonRef" is-submit type="warning" @click.prevent="submit">
        {{ buttonText }}
      </base-btn>
      <base-btn @click="$refs.base.close()">Cancel</base-btn>
    </template>
  </base-modal>
</template>


<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { TriangleAlert } from 'lucide-vue-next'
import { DialogTitle } from '@headlessui/vue'

export default {
  name: 'ConfirmDialog',
  components: { BaseBtn, DialogTitle, TriangleAlert, BaseModal },
  emits: ['confirm'],
  props: {
    title: {
      type: String,
      required: true
    },
    text: {
      type: String,
      required: true
    },
    buttonText: {
      type: String,
      default: 'Confirm'
    }
  },

  methods: {
    open () {
      this.$refs.base.open()
    },

    submit () {
      this.$emit('confirm')
      this.$refs.base.close()
    }
  }
}
</script>

<style scoped>

</style>
