<template>
  <transition-root as="template" :show="thisModalOpen">
    <Dialog as="div" class="fixed z-20 inset-0 overflow-y-auto" @close="close">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <transition-child
            as="template"
            :enter="enterClasses"
            enter-from="opacity-0"
            enter-to="opacity-100"
            leave="ease-in duration-100"
            leave-from="opacity-100"
            leave-to="opacity-0"
            @after-leave="$emit('closed')">
          <dialog-overlay class="fixed inset-0 bg-gray-500/75 transition-opacity"/>
        </transition-child>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <transition-child
            as="template"
            :enter="enterClasses"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-100"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            @after-enter="$emit('opened')">

          <dialog-panel
              class="inline-block bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all my-8 align-middle w-full"
              :class="[modalSize]">
            <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
              <button
                  type="button"
                  tabindex="-1"
                  class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-hidden focus:ring-2 focus:ring-fuchsia-500 focus:ring-offset-2 cursor-pointer"
                  @click="close">
                <span class="sr-only">Close</span>
                <X class="h-6 w-6" aria-hidden="true"/>
              </button>
            </div>
            <form @submit.prevent="submit">
              <div v-bind="$attrs" class="flex flex-col">
                <div class="bg-white px-4 pt-5 pb-4 p-6 flex-1">
                  <div class="flex items-start">
                    <slot name="body">
                      <div class="mt-0 text-left w-full space-y-4">
                        <dialog-title v-if="title" as="h3" class="text-base font-semibold leading-6 text-gray-900">
                          {{ title }}
                        </dialog-title>
                        <slot></slot>
                      </div>
                    </slot>
                  </div>
                </div>
                <div v-if="!hideButtons"
                     class="bg-gray-50 px-4 py-3 space-x-2 space-x-reverse sm:px-6 flex flex-row-reverse">
                  <slot name="buttons">
                    <base-btn
                        is-submit
                        :disabled="disableSubmit"
                        :loading="submitProcessing"
                        type="primary"
                        @click.prevent="submit">
                      {{ submitText }}
                    </base-btn>
                    <base-btn v-if="!hideCancel" @click="close" :disabled="!closable">Cancel</base-btn>
                  </slot>
                </div>
                <div v-else>
                  <button class="text-white"></button>
                </div>
              </div>
            </form>
          </dialog-panel>
        </transition-child>
      </div>
    </Dialog>
  </transition-root>
</template>

<script>

import { Dialog, DialogOverlay, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { X } from 'lucide-vue-next'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { mapWritableState } from 'pinia'
import { useAppStore } from '@/stores/app.js'

/*
Base Modal

Implementations can either pass in a submit method - which will be called and
can cancel the action (allowing for validation) if it returns false. Or this just
raises a submit event and closes the modal.
*/
export default {
  name: 'BaseModal',
  components: {
    DialogPanel,
    BaseBtn,
    DialogTitle,
    Dialog,
    TransitionRoot,
    TransitionChild,
    DialogOverlay,
    X
  },
  emits: ['opening', 'opened', 'closing', 'closed', 'submit'],
  props: {
    hideButtons: {
      type: Boolean,
      default: false
    },
    hideCancel: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: false
    },
    closable: {
      type: Boolean,
      default: true
    },
    submitText: {
      type: String,
      default: 'OK'
    },
    small: {
      type: Boolean,
      default: false
    },
    large: {
      type: Boolean,
      default: false
    },
    xl: {
      type: Boolean,
      default: false
    },
    disableSubmit: {
      type: Boolean,
      default: false
    },
    submitMethod: {
      type: Function,
      default: undefined
    }
  },
  data: () => ({
    thisModalOpen: false,
    afterMount: false,
    submitProcessing: false
  }),

  mounted () {
    setTimeout(() => {
      this.afterMount = true
    }, 300)
  },

  computed: {
    ...mapWritableState(useAppStore, {
      appStoreModalIsOpen: 'modalIsOpen'
    }),
    modalSize () {
      if (this.large) {
        return 'md:max-w-5xl'
      } else if (this.xl) {
        return 'md:max-w-7xl'
      } else if (this.small) {
        return 'md:max-w-sm'
      } else {
        return 'md:max-w-2xl'
      }
    },
    enterClasses () {
      return (!this.afterMount) ? 'duration-0' : 'ease-out duration-100'
    }
  },

  methods: {
    close (force = false) {
      if (this.closable || force) {
        this.thisModalOpen = false
        this.$emit('closing')
        this.appStoreModalIsOpen = false
      }
    },

    open () {
      // Pause a beat - this solves issue with not focus trap not working if
      // e.g. one is closing (say, a context menu dismissal). Doing nextTick
      // isn't enough for some reason.
      this.$emit('opening')
      setTimeout(() => {
        this.submitProcessing = false
        this.thisModalOpen = true
        this.appStoreModalIsOpen = true
      }, 0)
    },

    async submit () {
      if (this.disableSubmit) {
        return
      }
      if (this.submitMethod === undefined) {
        this.$emit('submit')
        this.close()
        return
      }

      // We have a submit method; call it, wait its response and only close if it returns true
      this.submitProcessing = true
      this.submitMethod().then((r) => {
        if (r) {
          this.$emit('submit')
          this.close()
        } else {
          this.submitProcessing = false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
