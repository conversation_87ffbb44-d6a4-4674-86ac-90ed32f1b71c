<template>
  <base-modal
      ref="base"
      :title="title"
      @submit="submit">
    <template #body>
      <div
          class="mx-auto flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-fuchsia-100 sm:mx-0 sm:h-10 sm:w-10">
        <Pencil class="h-6 w-6 text-fuchsia-600" aria-hidden="true"/>
      </div>
      <div class="flex-1 mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
        <dialog-title as="h3" class="text-base font-semibold leading-6 text-gray-900">{{ title }}</dialog-title>
        <div class="mt-4">
          <input
              name="value"
              id="value"
              autocomplete="off"
              v-model="value"
              class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full text-sm border-gray-300 rounded-md"
              :placeholder="placeholder"
          />
        </div>
      </div>
    </template>
  </base-modal>
</template>

<script>
/*
Generic component to show a dismissible modal with an input field.

This fully encapsulates the behavior of a dialog with an input field. This is done by
"exposing" a method, openDialog, which can be called from the parent through $refs.

This means parent doesn't need to keep track of open state - we just call it, and then
handle the emits if the value is submitted.
https://stackoverflow.com/a/55317353
 */
import BaseModal from '@/components/modal/BaseModal.vue'
import { DialogTitle } from '@headlessui/vue'
import { Pencil } from 'lucide-vue-next'

export default {
  name: 'BaseDialog',
  components: {
    DialogTitle,
    BaseModal,
    Pencil
  },
  emits: ['change'],
  props: {
    title: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      required: false
    }
  },
  data () {
    return {
      value: ''
    }
  },
  methods: {
    open (defaultValue) {
      this.value = defaultValue
      this.$refs.base.open()
    },

    close () {
      this.$refs.base.close()
    },

    submit () {
      this.$emit('change', this.value)
    }
  }
}
</script>


<style scoped>

</style>
