<!--suppress JSValidateTypes -->
<template>
  <base-modal ref="base" title="Export chart" hide-buttons xl>
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <router-link
            v-for="fmt in formats"
            :key="fmt.key"
            :to="{
              name: `app-${chartStoreViewId ? 'view' : 'table'}-chart-export`,
              params: { tableId: tableStoreId, chartId: chartStoreId, chartExportFormat: fmt.key }
            }"
            :class="['cursor-pointer', format === fmt.key ? 'border-fuchsia-500 text-fuchsia-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm']">
          {{ fmt.name }}
        </router-link>
      </nav>
    </div>
    <div class="mt-4 flex space-x-8">
      <div class="flex-none shrink-0 w-96 bg-gray-100">
        <div class="space-y-4 p-6">
          <div v-if="format === 'dynamic'">
            <h2 class="text-base font-semibold leading-7 text-gray-900">Embed Chart</h2>
            <p class="mt-1 text-sm leading-6 text-gray-600">Embed an interactive chart in a website or blog
              post.</p>
          </div>
          <div v-else>
            <h2 class="text-base font-semibold leading-7 text-gray-900">Save Image of Chart</h2>
            <p class="mt-1 text-sm leading-6 text-gray-600">Download your chart as a static, fixed-size image for
              embedding anywhere.</p>
          </div>

          <div>
            <label for="size" class="block text-sm font-bold leading-6 text-gray-900">Size</label>

            <div v-if="format === 'dynamic'">
              <base-toggle v-model="responsiveWidth" label="Responsive width?" left-label="Fixed width"/>
            </div>
            <div class="mt-2">
              <select id="size" name="size"
                      v-model="selectedSize"
                      class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-fuchsia-600 text-sm leading-6">
                <option v-for="(size, index) in sizes" :key="index" :value="index">{{ size }}</option>
              </select>
            </div>
          </div>

          <template v-if="format === 'image'">
            <div>
              <label for="bg-color" class="block text-sm font-bold leading-6 text-gray-900">Background Color</label>
              <div class="mt-2">
                <div class="flex flex-wrap mt-2 gap-1">
                  <color-box
                      v-for="(color, index) in bgColors"
                      :key="index"
                      :color="color"
                      :selected="bgColor === index"
                      @click="bgColor = index"
                  />
                </div>
              </div>
            </div>

            <a ref="downloadChartAnchor" class="hidden" :href="chartImage" :download="fileName"></a>
            <base-btn
                full-width
                type="primary"
                :icon-component="ArrowDownToLine"
                icon-classes="text-white"
                @click="$refs.downloadChartAnchor.click()">
              Download Image
            </base-btn>
            <base-toggle v-model="includeSizeInFileName" label="Include size in filename?"/>
          </template>

          <template v-else>
            <div>
              <label for="embed" class="block text-sm font-bold leading-6 text-gray-900">Code format</label>
              <div class="text-sm col-span-2 space-y-2 mt-2">
                <div v-for="fmt in dynamicFormats" :key="fmt.key">
                  <input
                      type="radio"
                      v-model="dynamicFormat"
                      class="h-4 w-4 border-gray-300 text-fuchsia-600 focus:ring-fuchsia-600"
                      :value="fmt.key"
                      :id="'dynamic-format-' + fmt.key">
                  <label class="font-medium text-sm text-gray-700 ml-2" :for="'dynamic-format-' + fmt.key">
                    {{ fmt.name }}
                  </label>
                </div>
              </div>

              <textarea
                  readonly
                  name="embed"
                  class="mt-2 w-full h-48 text-xs overflow-x-auto border border-gray-300 rounded-md font-mono"
                  v-model="dynamicOutput">
                  </textarea>
            </div>
            <div>
              <base-btn
                  full-width
                  :disabled="copyStatus === 'copying'"
                  is-submit
                  type="primary"
                  @click="copyToClipboard"
                  :icon-component="copyStatus === 'copied' ? ClipboardCheck : Clipboard"
                  :icon-classes="copyStatus === 'copied' ? 'text-green-300' : 'text-white'">
                {{ copyStatus === 'copied' ? 'Copied' : 'Copy to Clipboard' }}
              </base-btn>
            </div>
          </template>

        </div>
      </div>

      <div class="flex-1 max-w-(--breakpoint-xl) overflow-x-auto overflow-y-auto max-h-[70vh]">
        <div class="relative" :style="{ width: this.chartWidth, height: `${chartSize.height}px`}">
          <img v-if="format === 'image'" :src="chartImage" alt="Chart"/>
          <data-hero-chart
              v-if="chartConfig"
              no-events
              :key="`${selectedSize}${bgColor}`"
              v-show="format !== 'image'"
              ref="chart"
              :data="chartConfig.data"
              :options="chartConfig.options"
              :type="chartStoreType"
              :bg-color="bgColor"
              :create-image="format === 'image'"
              @image-data="chartImage = $event"
          />
        </div>
      </div>
    </div>
  </base-modal>
</template>

<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { mapState } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import { useChartStore } from '@/stores/chart.js'
import { ArrowDownToLine, Clipboard, ClipboardCheck } from 'lucide-vue-next'
import { getChartConfig } from '@/charts/chartConfig.js'
import DataHeroChart from '@/components/datahero/DataHeroChart.vue'
import { getChartData, getChartProps } from '@/charts/chartProps.js'
import filenamify from 'filenamify/browser'
import ColorBox from '@/components/ui/ColorBox.vue'
import { createColorObject } from '@/chartjs-plugins/plugin.themes.js'
import { merge } from 'chart.js/helpers'
import { baseTheme } from '@/chartjs-plugins/baseTheme.js'

const embedUrl = import.meta.env.VITE_EMBED_URL

const sizes = [
  [640, 480],
  [800, 600],
  [1024, 768],
  [1280, 1024],
  [1600, 1200]
]
export default {
  components: { ColorBox, DataHeroChart, BaseBtn, BaseToggle, BaseModal },

  props: {
    format: {
      type: String,
      required: false,
      default: 'image'
    }
  },
  data () {
    return {
      Clipboard,
      ClipboardCheck,
      ArrowDownToLine,
      selectedSize: 0,
      includeSizeInFileName: false,
      responsiveWidth: false,
      chartImage: undefined,

      chartData: undefined,
      chartConfig: undefined,

      formats: [
        { name: 'Image', key: 'image' },
        { name: 'Dynamic', key: 'dynamic' }
        // { name: 'Live', key: 'live', disabled: true }
      ],
      dynamicFormats: [
        { name: 'Full embed', key: 'full' },
        { name: 'Exclude the library', key: 'exlib' },
        { name: 'Just the data', key: 'json' }
      ],
      dynamicFormat: 'full',
      bgColor: -1,
      copyStatus: ''
    }
  },

  computed: {
    ...mapState(useChartStore, {
      chartStoreData: 'data',
      chartStoreTitle: 'title',
      chartStoreType: 'chartType',
      chartStoreId: 'id',
      chartStoreViewId: 'viewId'
    }),
    ...mapState(useTableStore, {
      tableStoreDbTable: 'dbTable',
      tableStoreId: 'id',
      tableStoreTitle: 'title'
    }),

    fileName () {
      // Run it through both filenamify and then a stricter strip, as some browser don't like some characters
      let s = this.chartStoreTitle || this.tableStoreTitle || 'chart'
      s = filenamify(s, { replacement: '-' }).replace(/[+()$~%.'":*?<>{}]/g, '-')
      if (this.includeSizeInFileName) {
        const split = sizes[this.selectedSize]
        s = `${s}-${split[0]}x${split[1]}`
      }
      return s + '.png'
    },

    isResponsive () {
      return (this.format === 'dynamic' && this.responsiveWidth)
    },

    sizes () {
      return sizes.map(([w, h]) => {
        if (this.isResponsive) {
          return `${w}px high`
        }
        return `${w}x${h}`
      })
    },

    theme () {
      // Theme takes this chart's theme, and merges with our base. Note that this will exclude modes
      const theme = this.chartStoreData.theme || {}
      merge(theme, baseTheme)
      return theme
    },

    bgColors () {
      /*
      Get all colors from the theme.

      Turns each into an object with the original index (for consistent list keys) and
      a color object created from the index and the theme.
       */
      return this.theme.backgroundColors.colors.map(hexCode => createColorObject({
        background: hexCode,
        border: '#888'   // border-gray-600
      }, this.theme))
    },

    chartSize () {
      return {
        width: sizes[this.selectedSize][0],
        height: sizes[this.selectedSize][1]
      }
    },

    chartWidth () {
      return this.isResponsive ? '100%' : `${this.chartSize.width}px`
    },

    dynamicOutput () {

      // we need it for the extra escape - see https://stackoverflow.com/a/36607971 - this jslint is too strict
      /* eslint-disable no-useless-escape */
      if (this.dynamicFormat === 'json') {
        return JSON.stringify(this.chartData, null, 1)
      }
      const json = JSON.stringify(this.chartData)
      let s = `
<div class="datahero" style="width: ${this.chartWidth}; height: ${this.chartSize.height}px" data-datahero-id="${this.chartStoreId}"></div>
<script id="datahero-${this.chartStoreId}" type="application/json">${json}<\/script>`.trim()
      if (this.dynamicFormat === 'full') {
        s += `\n<script src="${embedUrl}"><\/script>`
      }
      return s
    }
  },

  methods: {
    async open () {
      this.$refs.base.open()
      const chartProps = getChartProps(
          this.chartStoreTitle,
          this.chartStoreData,
          useTableStore().columns,
          useTableStore().views.find(v => v.id === this.chartStoreViewId)
      )
      this.chartData = await getChartData(chartProps, this.tableStoreDbTable)
      this.chartConfig = getChartConfig(this.chartData)
    },
    copyToClipboard () {
      this.copyStatus = 'copying'
      navigator.clipboard.writeText(this.dynamicOutput).then(() => {
        this.copyStatus = 'copied'
        setTimeout(() => {
          this.copyStatus = ''
        }, 2000)
      })
    }
  }
}
</script>


<style scoped>

</style>
