<template>
  <base-modal
      ref="modal"
      title="Formula Editor"
      large
      submit-text="Save Formula"
      @submit="saveFormula">
    <div class="flex gap-4 h-80">
      <!-- Left panel: Column list with dragable items -->
      <div class="w-1/4 bg-zinc-100 rounded-md p-2 overflow-y-auto">
        <h3 class="text-sm font-medium text-zinc-700 mb-2">Columns</h3>
        <sortable
            :list="draggableColumns"
            item-key="id"
            :options="{
            group: { name: 'columns', pull: 'clone', put: false },
            sort: false
          }"
            @end="handleDragEnd"
            class="space-y-1">
          <template #item="{ element: column }">
            <div
                class="px-2 py-1.5 bg-white rounded border border-zinc-200 text-sm flex items-center gap-2 cursor-move hover:border-fuchsia-400"
                :data-column-id="column.id"
                :data-column-header="column.header || `Column ${column.id}`"
                :data-type="'column'">
              <span class="w-2 h-2 rounded-full" :class="getColumnTypeColor(column.type)"></span>
              <span>{{ column.header || `Column ${column.id}` }}</span>
            </div>
          </template>
        </sortable>

        <h3 class="text-sm font-medium text-zinc-700 mt-4 mb-2">Functions</h3>
        <sortable
            :list="draggableFunctions"
            item-key="name"
            :options="{
            group: { name: 'functions', pull: 'clone', put: false },
            sort: false
          }"
            @end="handleDragEnd"
            class="space-y-1">
          <template #item="{ element: func }">
            <div
                class="px-2 py-1.5 bg-white rounded border border-zinc-200 text-sm flex items-center gap-2 cursor-move hover:border-fuchsia-400"
                :data-name="func.name"
                :data-template="func.template"
                :data-type="'function'">
              <span class="text-fuchsia-600 font-mono text-xs">ƒ</span>
              <span>{{ func.name }}</span>
            </div>
          </template>
        </sortable>
      </div>

      <!-- Right panel: Formula editor -->
      <div class="w-3/4 flex flex-col">
        <h3 class="text-sm font-medium text-zinc-700 mb-2">Formula</h3>
        <div class="border border-zinc-300 rounded-md flex-grow relative">
          <codemirror
              ref="cmEditor"
              v-model="formula"
              :extensions="extensions"
              :indent-with-tab="true"
              @drop="handleDrop"
              @dragover.prevent
              class="h-full"
              placeholder="Type or drag columns/functions here..."
          />
        </div>

        <div class="mt-4 text-sm text-zinc-600">
          <p>Drag columns or functions to the editor or type your formula directly.</p>
          <p>Use square brackets to reference columns: [Column Name]</p>
          <p>Press Ctrl+Space for autocompletion suggestions.</p>
          <p v-if="formulaError" class="text-red-600 mt-2">{{ formulaError }}</p>
        </div>
      </div>
    </div>
  </base-modal>
</template>

<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import { Codemirror } from 'vue-codemirror'
import { Sortable } from 'sortablejs-vue3'
import { sql } from '@codemirror/lang-sql'
import { EditorView, keymap } from '@codemirror/view'
import { HighlightStyle, syntaxHighlighting } from '@codemirror/language'
import { tags } from '@lezer/highlight'
import { autocompletion, completionKeymap } from '@codemirror/autocomplete'
import formulaService from '@/utils/formulas.js'

export default {
  name: 'FormulaEditorModal',
  components: {
    BaseModal,
    Codemirror,
    Sortable
  },
  props: {
    columns: {
      type: Array,
      required: true,
      default: () => []
    },
    initialFormula: {
      type: String,
      default: ''
    }
  },
  emits: ['save', 'update:formula'],

  watch: {
    formula (newFormula) {
      // Validate after a short delay to avoid validating while typing
      setTimeout(() => {
        const validation = formulaService.validateFormula(newFormula, this.columns)
        this.formulaError = validation.valid ? '' : validation.error
      }, 100)
    },
    // Watch for changes in columns prop to update draggableColumns
    columns: {
      handler (newColumns) {
        this.draggableColumns = [...newColumns]
      },
      deep: true
    }
  },

  data () {
    // List of common SQL functions that can be used in formulas
    const availableFunctions = [
      { name: 'SUM', template: 'SUM(${1:column})' },
      { name: 'AVG', template: 'AVG(${1:column})' },
      { name: 'MIN', template: 'MIN(${1:column})' },
      { name: 'MAX', template: 'MAX(${1:column})' },
      { name: 'COUNT', template: 'COUNT(${1:column})' },
      { name: 'CASE', template: 'CASE WHEN ${1:condition} THEN ${2:value} ELSE ${3:value} END' },
      { name: 'COALESCE', template: 'COALESCE(${1:column}, 0)' },
      { name: 'ROUND', template: 'ROUND(${1:column}, ${2:decimals})' }
    ]

    // Create a custom syntax highlighting for our formula editor
    const formulaHighlightStyle = HighlightStyle.define([
      { tag: tags.keyword, color: '#7c3aed' },
      { tag: tags.function(tags.variableName), color: '#2563eb' },
      { tag: tags.number, color: '#059669' },
      { tag: tags.string, color: '#d97706' },
      { tag: tags.comment, color: '#6b7280', fontStyle: 'italic' },
      { tag: tags.operator, color: '#374151' }
    ])

    return {
      formula: this.initialFormula || '',
      formulaError: '',
      draggableColumns: [...this.columns], // Initialize with columns from props
      draggableFunctions: [...availableFunctions],
      formulaHighlightStyle,
      extensions: [] // Will be initialized in created()
    }
  },

  created () {
    this.setupEditorExtensions()
  },

  methods: {
    setupEditorExtensions () {
      // Create autocompletion provider for column names and functions
      const myCompletions = (context) => {
        // The word pattern should match column references with brackets and function names
        const wordPattern = /\[[^\]]*$|[a-zA-Z0-9_]+$/
        const word = context.matchBefore(wordPattern)

        if (!word) return null

        const text = context.state.doc.toString()
        const position = context.pos

        // Get suggestions from our formula service
        const suggestions = formulaService.getSuggestions(
            text,
            position,
            this.draggableColumns,
            this.draggableFunctions
        )

        if (!suggestions.length) return null

        return {
          from: word.from,
          options: suggestions.map(s => ({
            label: s.label,
            type: s.type,
            apply: s.insertText
          }))
        }
      }

      // Set up editor extensions with explicit autocompletion keymap
      this.extensions = [
        EditorView.lineWrapping,
        sql(),
        syntaxHighlighting(this.formulaHighlightStyle),
        autocompletion({ override: [myCompletions] }),
        keymap.of(completionKeymap),
        EditorView.theme({
          '&': {
            height: '100%'
          },
          '.cm-content': {
            fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
            fontSize: '14px'
          }
        })
      ]
    },

    // Handle drag end from sortable
    handleDragEnd (evt) {
      if (evt.to !== evt.from && evt.to.className?.includes('cm-content')) {
        // Item was dropped into the CodeMirror editor
        const type = evt.item.dataset.type

        if (type === 'column') {
          const columnId = evt.item.dataset.columnId
          const columnHeader = evt.item.dataset.columnHeader
          if (columnHeader) {
            // Insert column reference at cursor position
            this.insertAtCursor(`[${columnHeader}]`)
          }
        } else if (type === 'function') {
          const funcName = evt.item.dataset.name
          if (funcName) {
            // Insert function with parentheses
            this.insertAtCursor(`${funcName}()`)
          }
        }
      }
    },

    insertAtCursor (text) {
      // Get the CodeMirror view from the Codemirror component
      const view = this.$refs.cmEditor?.view

      // Get the current selection
      const { from, to } = view.state.selection.main

      // Create a transaction that replaces the selection with the text
      const transaction = view.state.update({
        changes: { from, to, insert: text },
        selection: { anchor: from + text.length }
      })

      // Dispatch the transaction
      view.dispatch(transaction)

      // Focus the editor
      view.focus()
    },

    handleDrop (event) {
      // Prevent default to avoid browser navigation
      event.preventDefault()

      // Get dragged element data from the dataTransfer object
      const element = event.dataTransfer.getData('text')

      // If we don't have element data, look for data attributes on the dragged element itself
      if (!element && event.dataTransfer.items) {
        // Get drag data from the DOM element directly if available
        const draggedElement = event.target.closest('[data-type]')
        if (draggedElement) {
          const type = draggedElement.dataset.type

          if (type === 'column') {
            const columnHeader = draggedElement.dataset.columnHeader
            if (columnHeader) {
              this.formula += `[${columnHeader}]`
            }
          } else if (type === 'function') {
            const funcName = draggedElement.dataset.name
            if (funcName) {
              this.formula += `${funcName}()`
            }
          }
        }
      } else {
        // For drag operations from our sortable components, we need to look
        // at the event source target for the data attributes
        const dragSrcElement = event.currentTarget || event.target

        if (dragSrcElement && dragSrcElement.dataset) {
          const type = dragSrcElement.dataset.type

          if (type === 'column') {
            const columnHeader = dragSrcElement.dataset.columnHeader
            if (columnHeader) {
              this.formula += `[${columnHeader}]`
            }
          } else if (type === 'function') {
            const funcName = dragSrcElement.dataset.name
            if (funcName) {
              this.formula += `${funcName}()`
            }
          }
        }
      }

      // If we need to handle external drag sources like JSON text
      // we could try parsing as a fallback, but with proper error handling
      try {
        // Only try this if we haven't already processed the drop
        const dataTransferText = event.dataTransfer.getData('text/plain')
        if (dataTransferText && !this.formula.endsWith(dataTransferText)) {
          const data = JSON.parse(dataTransferText)
          if (data.type === 'column') {
            this.formula += `[${data.header || data.name}]`
          } else if (data.type === 'function') {
            this.formula += `${data.name}()`
          }
        }
      } catch (e) {
        // No need to log the error - this is an expected case when
        // the drag data isn't in JSON format
        console.debug('Not JSON data:', e.message)
      }
    },

    // Returns a CSS class based on column type
    getColumnTypeColor (type) {
      switch (type) {
        case 'number':
          return 'bg-blue-500'
        case 'text':
          return 'bg-green-500'
        case 'datetime':
          return 'bg-purple-500'
        case 'currency':
          return 'bg-yellow-500'
        case 'boolean':
          return 'bg-red-500'
        default:
          return 'bg-gray-500'
      }
    },

    // Methods to validate and save the formula
    validateFormula () {
      // Use the formula service for validation
      const validation = formulaService.validateFormula(this.formula, this.columns)

      if (!validation.valid) {
        this.formulaError = validation.error
        return false
      }

      // Clear any previous errors
      this.formulaError = ''
      return true
    },

    saveFormula () {
      if (this.validateFormula()) {
        // Generate SQL for this formula
        const sql = formulaService.generateSql(this.formula, this.columns)

        // Emit both the user-friendly formula and the SQL version
        this.$emit('save', {
          displayFormula: this.formula,
          sqlFormula: sql
        })
        this.$emit('update:formula', this.formula)
        return true
      }
      return false
    },

    // Public methods to open/close the modal
    open () {
      // Update draggable columns with the latest columns prop
      this.draggableColumns = [...this.columns]
      this.$refs.modal.open()
    },

    close () {
      this.$refs.modal.close()
    }
  }
}
</script>

<style>
/* Additional styles for CodeMirror */
.cm-editor {
  height: 100%;
  border-radius: 0.375rem;
}

.cm-scroller {
  overflow: auto;
  height: 100%;
}

/* Highlight column references in formulas */
.cm-columnRef {
  color: #4f46e5;
  font-weight: 500;
}
</style>
