<template>
  <base-modal ref="base" small title="An error has occurred." @closed="appStoreError = undefined">
    <p>Something went wrong. Please try again. Note that your last changes may not have been saved to the server.</p>
    <div v-if="errors.length && debug === true" class="divide-y space-y-4">
      <div class="mb-2">
        <strong>Errors: </strong>
        <ol class="list-decimal list-inside pt-4 max-h-72 overflow-y-auto">
          <li v-for="(msg, index) in errors" :key="index" class="text-sm">
            {{ msg }}
          </li>
        </ol>
      </div>
    </div>

    <template #buttons>
      <base-btn is-submit type="primary" @click.prevent="$refs.base.close()">Okay</base-btn>
    </template>
  </base-modal>
</template>

<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import { mapWritableState } from 'pinia'
import { useAuthStore } from '@/stores/auth.js'
import BaseBtn from '@/components/buttons/BaseBtn.vue'

export default {
  components: { BaseBtn, BaseModal },
  computed: {
    ...mapWritableState(useAuthStore, {
      appStoreError: 'error'
    }),

    errors () {
      // if error is an array, return that, otherwise wrap in an array
      if (this.appStoreError === undefined || this.appStoreError === null) {
        return []
      }
      return Array.isArray(this.appStoreError) ? this.appStoreError : [this.appStoreError]
    },

    debug () {
      return import.meta.env.VITE_DEBUG === 'true'
    }
  },

  watch: {
    appStoreError (val) {
      if (val) {
        this.$refs.base.open()
      } else {
        this.$refs.base.close()
      }
    }
  }
}
</script>


<style scoped>

</style>
