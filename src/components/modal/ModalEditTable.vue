<template>
  <base-modal
      ref="base"
      title="Edit Table"
      :disable-submit="!formValid"
      :submit-method="submit">
    <template #body>
      <div
          class="mx-auto flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-fuchsia-100 sm:mx-0 sm:h-10 sm:w-10">
        <Pencil class="h-6 w-6 text-fuchsia-600" aria-hidden="true"/>
      </div>
      <div class="flex-1 mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
        <dialog-title as="h3" class="text-base font-semibold leading-6 text-gray-900">Edit Table</dialog-title>
        <dialog-description v-if="project" class="text-sm mt-2">
          Rename your table or move the project it's in.
        </dialog-description>

        <div class="mt-4">
          <form-grid>
            <form-grid-section>
              <form-grid-row label="Table name">
                <input
                    name="value"
                    id="value"
                    autocomplete="off"
                    v-model="title"
                    class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full text-sm border-gray-300 rounded-md"
                    placeholder="Project name"
                />
              </form-grid-row>
              <form-grid-row label="Project">
                <select
                    v-model="project"
                    class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full text-sm border-gray-300 rounded-md">
                  <option :value="null">(No project)</option>
                  <option v-for="project in projectsStoreProjects" :key="project.id" :value="project.id">
                    {{ project.title }}
                  </option>
                </select>
              </form-grid-row>
            </form-grid-section>
          </form-grid>
        </div>
      </div>
    </template>

    <template #buttons>
      <base-btn is-submit type="primary" :disabled="!formValid">Save</base-btn>
      <base-btn @click="$refs.base.close()">Cancel</base-btn>
      <div class="flex-1">
        <base-btn type="warning" is-text @click="confirmDelete">Delete table</base-btn>
      </div>
    </template>
  </base-modal>

  <teleport to="body">
    <confirm-dialog
        title="Delete Table"
        text="Are you sure you want to delete this table?"
        ref="deleteTableDialog"
        @confirm="deleteTable"
    />
  </teleport>
</template>

<script>
import { Pencil } from 'lucide-vue-next'
import BaseModal from '@/components/modal/BaseModal.vue'
import { DialogDescription, DialogTitle } from '@headlessui/vue'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import { mapActions, mapState } from 'pinia'
import { useProjectsStore } from '@/stores/projects.js'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import ConfirmDialog from '@/components/modal/ConfirmDialog.vue'

export default {
  components: {
    FormGridSection,
    DialogDescription,
    FormGridRow,
    FormGrid,
    DialogTitle,
    BaseModal,
    Pencil,
    BaseBtn,
    ConfirmDialog
  },

  emits: ['delete-table'],

  data () {
    return {
      title: '',
      project: undefined,
      table: undefined
    }
  },

  computed: {
    ...mapState(useProjectsStore, {
      projectsStoreProjects: 'projects'
    }),
    formValid () {
      return this.title.length > 0
    }
  },

  methods: {
    ...mapActions(useProjectsStore, {
      projectsStoreEditTable: 'editTable',
      projectsStoreDeleteTable: 'deleteTable'
    }),

    open (table) {
      this.table = table
      this.title = table.title
      this.project = table.project
      this.$refs.base.open()
    },

    async submit () {
      if (!this.formValid) return false
      await this.projectsStoreEditTable(this.table.id, this.title, this.project)
      Object.assign(this.$data, this.$options.data.apply(this))
      return true
    },

    confirmDelete () {
      // Close the edit table modal first
      this.$refs.base.close()
      // Then open the confirmation dialog
      this.$refs.deleteTableDialog.open()
    },

    async deleteTable () {
      // Delete the table
      await this.projectsStoreDeleteTable(this.table.id)

      // Reset component data
      Object.assign(this.$data, this.$options.data.apply(this))

      // Emit an event to notify parent components
      this.$emit('delete-table')
    }
  }
}
</script>

<style scoped>

</style>
