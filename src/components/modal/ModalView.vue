<template>
  <base-modal ref="base" :title="modalTitle" class="min-h-144" :disable-submit="!formIsValid">
    <template #default>
      <div class="flex-1">
        <div class="rounded-md">
          <placeholder-input v-model="view.title" :placeholder="placeholderTitle"/>
        </div>
        <div>
          <div class="mt-4 border-b border-zinc-200">
            <nav class="-mb-px flex space-x-4" aria-label="Tabs">
              <a
                  v-for="(tab, index) in tabs"
                  :key="tab.id"
                  :tabindex="index + 2"
                  @click="activeTab = tab.id"
                  :class="[
                      activeTab === tab.id ? 'border-fuchsia-500 text-fuchsia-600' : 'border-transparent text-zinc-500 hover:border-zinc-300 hover:text-zinc-700',
                      'whitespace-nowrap border-b-2 px-2 py-4 text-sm font-medium focus:outline-hidden cursor-pointer']"
                  :aria-current="activeTab === tab.id ? 'page' : undefined">{{ tab.text }}</a>
            </nav>
          </div>
        </div>

        <div class="py-4">
          <div v-if="activeTab === 'type'">
            <template v-for="(type, key, index) in viewTypes">
              <div
                  v-if="!type.disable"
                  :key="index"
                  class="p-6 border border-zinc-200 shadow-xs flex cursor-pointer hover:bg-zinc-50"
                  :class="[
                    viewType === key ? 'border-fuchsia-500' : 'bg-white',
                    index === 0 ? 'rounded-t-lg' : '',
                    index === Object.values(viewTypes).filter(t => !t.disable).length - 1 ? 'rounded-b-lg' : ''
                ]"
                  @click="setViewType(key)"
              >
                <div class="shrink-0">
                  <component :is="type.icon" class="size-8 mt-2 text-fuchsia-500"/>
                </div>

                <div class="ml-4">
                  <h3 class="font-semibold">{{ type.label }}</h3>
                  <p class="text-sm text-zinc-600">{{ type.description }}</p>
                </div>
              </div>
            </template>
          </div>

          <div v-if="activeTab === 'groups'" class="space-y-4">
            <sortable
                :list="view.groups"
                item-key="id"
                :options="sortableOptions"
                @update="groupSortUpdate"
            >
              <template #item="{ element: group, index }">
                <div
                    :key="index"
                    class="border-x border-b border-zinc-200 shadow-xs flex cursor-pointer" :class="[
                      index === 0 ? 'rounded-t-lg border-t' : '',
                      index === view.groups.length - 1 ? 'rounded-b-lg' : ''
                    ]">
                  <div
                      class="drag-handle p-2 flex items-center cursor-move text-zinc-400 bg-zinc-100 hover:text-zinc-600">
                    <GripVertical class="size-4"/>
                  </div>
                  <div v-if="activeGroupId === group.id" class="p-2 flex-1 flex">
                    <div class="flex-1">
                      <form-grid-row class="pt-0!" label="Grouping column">
                        <select
                            v-model.number="group.sourceColumnId"
                            class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                          <option
                              v-for="(col, index) in tableStoreColumns"
                              :value="col.id"
                              :key="index">{{ tableStoreGetColumnAutoHeader(col.id) }}
                          </option>
                        </select>
                      </form-grid-row>
                      <form-grid-row v-if="columnGroupBys(group.sourceColumnId).length" label="Group by">
                        <select
                            v-model="group.by"
                            class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                          <option :value="undefined">Exact value</option>
                          <option
                              v-for="grouping in columnGroupBys(group.sourceColumnId)"
                              :value="grouping.id"
                              :key="grouping.id">{{ grouping.label }}
                          </option>
                        </select>
                      </form-grid-row>
                      <form-grid-row v-if="group.sourceColumnId !== undefined" label="Customize header">
                        <placeholder-input
                            v-model="group.header"
                            :placeholder="tableStoreGetColumnAutoHeader(group.sourceColumnId)"/>
                      </form-grid-row>
                    </div>
                    <div class="shrink-0">
                      <X
                          class="size-6 ml-4 mt-2 text-zinc-500 hover:text-red-500 cursor-pointer"
                          @click="removeGroup(index)"/>
                    </div>
                    <div class="shrink-0">
                      <CheckCircle
                          v-if="group.sourceColumnId !== undefined"
                          class="size-6 ml-2 mt-2 text-zinc-500 hover:text-green-500 cursor-pointer"
                          @click="activeGroupId = undefined"/>
                    </div>
                  </div>
                  <div v-else
                       class="p-2 grow text-sm hover:bg-zinc-50"
                       @click="toggleActiveGroup(group.id)">
                    {{ group.header || tableStoreGetColumnAutoHeader(group.sourceColumnId) }}
                  </div>
                </div>
              </template>
            </sortable>
            <base-btn :icon-component="Plus" @click="addGroup">Add grouping column to the report...</base-btn>
          </div>

          <div v-if="activeTab === 'columns'" class="space-y-4">
            <sortable
                :list="view.columns"
                item-key="id"
                :options="sortableOptions"
                @end="columnSortUpdate"
            >
              <template #item="{ element: column, index }">
                <div
                    :key="index"
                    class="border-x border-b border-zinc-200 shadow-xs flex cursor-pointer" :class="[
                      index === 0 ? 'rounded-t-lg border-t' : '',
                      index === view.columns.length - 1 ? 'rounded-b-lg' : ''
                    ]">
                  <div
                      class="drag-handle p-2 flex items-center cursor-move text-zinc-400 bg-zinc-100 hover:text-zinc-600">
                    <GripVertical class="size-4"/>
                  </div>
                  <div v-if="activeColumnId === column.id" class="p-2 flex-1 flex">
                    <div class="flex-1">
                      <form-grid-row class="pt-0!" label="Column">
                        <select
                            v-model.number="column.sourceColumnId"
                            class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">

                          <option
                              v-if="viewType === 'group'"
                              value="id"
                              key="id">Row count
                          </option>
                          <option
                              v-for="(col, index) in validTableColumns"
                              :value="col.id"
                              :key="index">{{ tableStoreGetColumnAutoHeader(col.id) }}
                          </option>
                        </select>
                      </form-grid-row>

                      <form-grid-row
                          v-if="viewType === 'group' && column.sourceColumnId !== 'id' && columnAggregates(column.sourceColumnId).length"
                          label="Summarize by">
                        <select
                            v-model="column.summarize"
                            class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                          <option
                              v-for="agg in columnAggregates(column.sourceColumnId)"
                              :value="agg.key"
                              :key="agg.key">{{ agg.label }}
                          </option>

                        </select>
                      </form-grid-row>

                      <form-grid-row v-if="column.sourceColumnId !== undefined" label="Column header">
                        <placeholder-input
                            v-model="column.header"
                            :placeholder="getSummarizeAutoHeader(index)"/>
                      </form-grid-row>
                    </div>
                    <div class="shrink-0">
                      <X
                          class="size-6 ml-4 mt-2 text-zinc-500 hover:text-red-500 cursor-pointer"
                          @click="removeColumn(index)"/>
                    </div>
                    <div class="shrink-0">
                      <CheckCircle
                          class="size-6 ml-2 mt-2 text-zinc-500 hover:text-green-500 cursor-pointer"
                          :class="{
                            invisible: !columnIsValid(column)
                          }"
                          @click="activeColumnId = undefined"/>
                    </div>
                  </div>
                  <div v-else
                       class="p-2 grow text-sm hover:bg-zinc-50"
                       @click="toggleActiveColumn(column.id)">
                    {{ column.header || getSummarizeAutoHeader(index) }}
                  </div>
                </div>
              </template>
            </sortable>
            <base-btn :icon-component="Plus" @click="addColumn">Add column to the report...</base-btn>
          </div>

          <div v-if="activeTab === 'filters'" class="space-y-4">
            <div v-if="!view.filters || Object.keys(view.filters).length === 0" class="text-center p-2 text-zinc-500">
              No filters defined yet. Add a filter to restrict which data appears in this view.
            </div>
            <div v-else>
              <div v-for="(filters, sourceColId) in view.filters" :key="sourceColId" class="mb-4">
                <div class="border border-zinc-200 shadow-xs rounded-lg">
                  <div class="bg-zinc-100 p-2 rounded-t-lg text-sm font-medium flex items-center">
                    <component :is="getColumnIcon(getColumnType(sourceColId))" class="size-4 mr-2 text-zinc-500"/>
                    <span class="flex-1">{{ getColumnHeader(sourceColId) }}</span>
                    <X
                        class="size-5 text-zinc-500 hover:text-red-500 cursor-pointer"
                        @click="removeFilterForColumn(sourceColId)"/>
                  </div>
                  <div class="p-2">
                    <div v-for="(filter, index) in filters" :key="index" class="mb-2 last:mb-0">
                      <div class="flex items-center">
                        <div class="flex-1">
                          <component
                              :is="getFilterComponent(sourceColId)"
                              :column="getColumnById(sourceColId)"
                              v-model="view.filters[sourceColId][index]"
                              :inline="true"
                          />
                        </div>
                        <div class="shrink-0 ml-2">
                          <X
                              class="size-4 text-zinc-500 hover:text-red-500 cursor-pointer"
                              @click="removeFilter(sourceColId, index)"/>
                        </div>
                      </div>
                    </div>
                    <div class="mt-2 flex items-center">
                      <div class="flex-1 border-t border-zinc-200"></div>
                      <base-btn is-text class="mx-2" @click="addFilterToColumn(sourceColId)">OR</base-btn>
                      <div class="flex-1 border-t border-zinc-200"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="showAddFilterButton" class="w-full">
              <select
                  v-model="selectedFilterColumnId"
                  @change="handleFilterColumnChange"
                  class="shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md">
                <option value="">Add filter on column...</option>
                <option
                    v-for="col in availableFilterColumns"
                    :key="col.id"
                    :value="col.id">
                  {{ tableStoreGetColumnAutoHeader(col.id) }}
                </option>
              </select>
            </div>
          </div>

          <div v-if="activeTab === 'sorting'" class="space-y-4">
            <div v-if="sortColumns.length === 0" class="text-center p-2 text-zinc-500">
              No sorting defined yet. Add columns to sort the data in this view.
            </div>
            <div v-else>
              <sortable
                  :list="sortColumns"
                  item-key="id"
                  :options="sortableOptions"
                  @end="sortColumnSortUpdate"
              >
                <template #item="{ element: sortCol, index }">
                  <div
                      :key="index"
                      class="border-x border-b border-zinc-200 shadow-xs flex cursor-pointer" :class="[
                        index === 0 ? 'rounded-t-lg border-t' : '',
                        index === sortColumns.length - 1 ? 'rounded-b-lg' : ''
                      ]">
                    <div
                        class="drag-handle p-2 flex items-center cursor-move text-zinc-400 bg-zinc-100 hover:text-zinc-600">
                      <GripVertical class="size-4"/>
                    </div>
                    <div class="p-2 flex-1 flex items-center">
                      <div class="flex-1">
                        <div class="text-sm font-medium">{{ sortCol.header }}</div>
                        <div class="text-xs text-zinc-500">{{ sortCol.type === 'group' ? 'Group' : 'Column' }}</div>
                      </div>
                      <div class="shrink-0 flex items-center space-x-2">
                        <base-toggle
                            v-model="sortCol.desc"
                            left-label="ASC"
                            label="DESC"
                        />
                        <X
                            class="size-5 ml-2 text-zinc-500 hover:text-red-500 cursor-pointer"
                            @click="removeSortColumn(index)"/>
                      </div>
                    </div>
                  </div>
                </template>
              </sortable>
            </div>
            <div v-if="availableSortColumns.length > 0" class="w-full">
              <select
                  @change="addSortColumn($event.target.value); $event.target.value = ''"
                  class="shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md">
                <option value="">Add column to sort...</option>
                <option
                    v-for="col in availableSortColumns"
                    :key="col.id"
                    :value="col.id">
                  {{ col.header }} ({{ col.type === 'group' ? 'Group' : 'Column' }})
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #buttons>
      <base-btn is-submit class="ml-2" type="primary" :disabled="!formIsValid" @click.prevent="insertAndClose">
        {{ this.view.id ? 'Edit View' : 'Add View' }}
      </base-btn>
      <base-btn @click="$refs.base.close">Cancel</base-btn>
      <div v-if="this.view.id" class="flex-1">
        <base-btn type="warning" is-text @click="confirmDelete">Delete view</base-btn>
      </div>
    </template>
  </base-modal>

  <teleport to="body">
    <confirm-dialog
        title="Delete View"
        text="Are you sure you want to delete this view?"
        ref="deleteViewDialog"
        @confirm="$emit('delete-view', this.view.id)"
    />
  </teleport>
</template>

<script>

import { markRaw } from 'vue'
import { moveInArray } from '@/utils/helpers.js'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import BaseModal from '@/components/modal/BaseModal.vue'
import { mapActions, mapState } from 'pinia'
import { createView, createViewColumn, createViewGroup, getNextViewColumnId, useTableStore } from '@/stores/table.js'
import { CheckCircle, GripVertical, Plus, X } from 'lucide-vue-next'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import PlaceholderInput from '@/components/forms/input/PlaceholderInput.vue'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import { Sortable } from 'sortablejs-vue3'
import { dateGrouping, viewTypes } from '@/db/grouping.js'
import { aggregates, getAggregateLabelsForColType } from '@/utils/aggregation.js'
import { getSortColumns } from '@/db/query/utils.js'
import ConfirmDialog from '@/components/modal/ConfirmDialog.vue'
import { isNumericType } from '@/utils/formats.js'
import { getColumnIcon } from '@/utils/colTypeComponents.js'
import BooleanFilter from '@/components/menus/filters/BooleanFilter.vue'
import TextFilter from '@/components/menus/filters/TextFilter.vue'
import NumericFilter from '@/components/menus/filters/NumericFilter.vue'
import DateTimeFilter from '@/components/menus/filters/DateTimeFilter.vue'

export default {
  emits: ['change-view', 'add-view', 'delete-view'],
  components: {
    ConfirmDialog,
    PlaceholderInput,
    FormGridRow,
    BaseModal,
    BaseBtn,
    BaseToggle,
    X,
    CheckCircle,
    GripVertical,
    Sortable,
    BooleanFilter,
    TextFilter,
    NumericFilter,
    DateTimeFilter
  },

  data () {
    return {
      Plus: markRaw(Plus),
      view: undefined,
      viewType: undefined,
      activeTab: 'type',
      activeColumnId: undefined,
      activeGroupId: undefined,
      selectedFilterColumnId: '',
      sortColumns: [],
      viewTypes,
      sortableOptions: {
        handle: '.drag-handle',
        animation: 150,
        ghostClass: 'sortable-ghost',
        dragClass: 'sortable-drag'
      }
    }
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreColumns: 'columns'
    }),

    modalTitle () {
      return this.view?.id ? 'Edit View' : 'Add View'
    },

    formIsValid () {
      if (this.viewType === 'filter') {
        // every view.column must have a valid source column; id is not valid
        return this.view.columns.every(col => this.validTableColumns.find(c => c.id === col.sourceColumnId))
      }
      if (this.viewType === 'group') {
        // every view.column must have summarize set or be 'id', and there must be at least one group
        return this.view.columns.every(
                // Every column must be 'id' or a valid column, and it all must have a summarize
                col =>
                    (col.sourceColumnId === 'id' || this.validTableColumns.find(c => c.id === col.sourceColumnId)) &&
                    (col.summarize !== undefined)
            ) &&
            // Every group must have a valid source column
            this.view.groups.every(group => this.validTableColumns.find(c => c.id === group.sourceColumnId))
      }
      return false
    },

    tabs () {
      const tabs = [
        { id: 'type', text: 'View Type' }
      ]
      if (this.viewType === 'group') {
        tabs.push({ id: 'groups', text: 'Groups' })
      }
      if (this.viewType === 'filter' || this.viewType === 'group') {
        tabs.push({ id: 'filters', text: 'Filters' })
        tabs.push({ id: 'columns', text: 'Columns' })
        tabs.push({ id: 'sorting', text: 'Sorting' })
      }
      return tabs
    },

    placeholderTitle () {
      return viewTypes[this.viewType]?.label || 'Enter a View Title'
    },

    validTableColumns () {
      return this.tableStoreColumns
    },

    availableFilterColumns () {
      // Return columns that don't already have filters
      const existingFilterColumns = this.view.filters ? Object.keys(this.view.filters).map(id => Number(id)) : []
      return this.tableStoreColumns.filter(col => !existingFilterColumns.includes(col.id))
    },

    showAddFilterButton () {
      // Only show the add filter button if there are columns available to filter
      return this.availableFilterColumns.length > 0
    },

    availableSortColumns () {
      const getHeaderForItem = (item, type) =>
          item.header || (
              type === 'group'
              ? this.tableStoreGetColumnAutoHeader(item.sourceColumnId)
              : this.getSummarizeAutoHeader(this.view.columns.indexOf(item))
          )

      const mapToSortableColumn = (type) => (item) => ({
        id: item.id,
        sourceColumnId: item.sourceColumnId,
        header: getHeaderForItem(item, type),
        type
      })

      const hasValidSourceColumn = (item) => item.sourceColumnId !== undefined
      const isNotAlreadySorted = (col) => !this.sortColumns.some(sc => sc.id === col.id)

      return [
        ...(this.view.groups || []).filter(hasValidSourceColumn).map(mapToSortableColumn('group')),
        ...(this.view.columns || []).filter(hasValidSourceColumn).map(mapToSortableColumn('column'))
      ].filter(isNotAlreadySorted)
    },

    cleanView () {
      /* Cleans up the view for submitting */

      const view = JSON.parse(JSON.stringify(this.view))

      // If filter view, remove any groups; this can happen if changing group to view
      if (this.viewType === 'filter') {
        view.groups = []
      }

      // For 'row counts', force summarize of count
      view.columns.forEach(col => {
        if (col.sourceColumnId === 'id') {
          col.summarize = 'count'
        }
      })

      // Give every column a header if it doesn't have one
      view.columns.forEach((col, index) => {
        if (col.header === undefined || col.header === '') {
          col.header = this.getSummarizeAutoHeader(index)
        }
      })

      // Always set a title
      if (view.title === undefined || view.title === '') {
        view.title = this.placeholderTitle
      }

      // Go through all filters and remove any that are null
      view.filters = Object.fromEntries(
          Object.entries(view.filters)
              .map(([colId, filters]) => [colId, filters.filter(Boolean)])
              .filter(([colId, filters]) => filters.length > 0)
      )

      // Clear existing sort properties from all groups and columns
      view.groups.forEach(group => {
        delete group.sort
      })
      view.columns.forEach(column => {
        delete column.sort
      })

      // Apply sorting from sortColumns array
      this.sortColumns.forEach((sortCol, index) => {
        const group = view.groups.find(g => g.id === sortCol.id)
        const column = view.columns.find(c => c.id === sortCol.id)

        if (group) {
          group.sort = { order: index, desc: sortCol.desc }
        } else if (column) {
          column.sort = { order: index, desc: sortCol.desc }
        }
      })

      return view
    }
  },

  watch: {
    'view.groups': {
      handler (groups) {
        if (!groups) return

        // Check each group and reset 'by' property if column type doesn't support it
        groups.forEach(group => {
          if (group.sourceColumnId !== undefined && group.by !== undefined) {
            const col = this.tableStoreColumns.find(col => col.id === group.sourceColumnId)
            // If column is not datetime but has a groupBy value, reset it
            if (col && col.type !== 'datetime') {
              group.by = undefined
            }
          }
        })

        // Clean up sort columns that reference deleted groups
        this.cleanupInvalidSortColumns()
      },
      deep: true
    },

    'view.columns': {
      handler () {
        // Clean up sort columns that reference deleted columns
        this.cleanupInvalidSortColumns()
      },
      deep: true
    }
  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetColumnAutoHeader: 'getColumnAutoHeader'
    }),

    columnIsValid (column) {
      // Allow 'id' as a source column only if grouped view
      if (this.viewType === 'group' && column.sourceColumnId === 'id') {
        return true
      }
      return this.validTableColumns.find(col => col.id === column.sourceColumnId) &&
          (column.summarize !== undefined || column.sourceColumnId === 'id')
    },

    getSummarizeAutoHeader (colIndex) {
      const col = this.view.columns[colIndex]
      if (col.sourceColumnId === 'id') {
        return 'Row count'
      }
      const colAutoHeader = this.tableStoreGetColumnAutoHeader(col.sourceColumnId)
      if (col.summarize) {
        const aggLabel = aggregates[col.summarize].label
        return `${colAutoHeader} (${aggLabel})`
      }
      return colAutoHeader
    },

    open (view) {
      this.activeColumnId = undefined
      this.activeGroupId = undefined
      this.selectedFilterColumnId = ''
      if (view) {
        this.view = JSON.parse(JSON.stringify(view))
        if (!this.view.filters) {
          this.view.filters = {}
        }
        this.viewType = this.sniffViewType(view)
        this.activeTab = this.tabs[1].id
        // Hydrate sort columns from the view
        this.hydrateSortColumns()
      } else {
        this.view = createView(this.tableStoreId)
        this.view.filters = {}
        this.viewType = undefined
        this.activeTab = this.tabs[0].id
        this.sortColumns = []
      }
      this.$refs.base.open()
    },

    hydrateSortColumns () {
      // Use getSortColumns to extract sorting information from groups and columns
      const allColumns = [...(this.view.groups || []), ...(this.view.columns || [])]
      const sortedColumns = getSortColumns(allColumns)

      this.sortColumns = sortedColumns.map(sortCol => {
        const group = this.view.groups?.find(g => g.id === sortCol.id)
        const column = this.view.columns?.find(c => c.id === sortCol.id)
        const item = group || column

        return {
          id: sortCol.id,
          desc: sortCol.desc,
          header: item?.header || (group ? this.tableStoreGetColumnAutoHeader(item.sourceColumnId) : this.getSummarizeAutoHeader(this.view.columns.indexOf(item))),
          type: group ? 'group' : 'column'
        }
      })
    },

    cleanupInvalidSortColumns () {
      // Remove sort columns that no longer exist in groups or columns
      const validIds = [
        ...(this.view.groups || []).map(g => g.id),
        ...(this.view.columns || []).map(c => c.id)
      ]

      this.sortColumns = this.sortColumns.filter(sortCol => validIds.includes(sortCol.id))
    },

    confirmDelete () {
      this.$refs.base.close()
      this.$refs.deleteViewDialog.open()
    },

    insertAndClose () {
      this.$refs.base.close()
      if (this.view.id) {
        this.$emit('change-view', this.cleanView)
      } else {
        this.$emit('add-view', this.cleanView)
      }
    },

    sniffViewType (view) {
      if (view) {
        if (view.groups.length) {
          return 'group'
        } else {
          return 'filter'
        }
      }
      return undefined
    },

    setViewType (type) {
      this.viewType = type
      this.activeTab = this.tabs[1].id
    },

    columnGroupBys (sourceColumnId) {
      const col = this.tableStoreColumns.find(col => col.id === sourceColumnId)
      return (col?.type === 'datetime') ? Object.values(dateGrouping) : []
    },

    columnAggregates (sourceColumnId) {
      const col = this.tableStoreColumns.find(col => col.id === sourceColumnId)
      return (col?.type) ? getAggregateLabelsForColType(col.type) : []
    },

    columnSortUpdate (event) {
      moveInArray(this.view.columns, event.oldIndex, event.newIndex)
    },

    addColumn () {
      const column = createViewColumn(undefined)
      column.id = getNextViewColumnId(this.view)
      this.view.columns.push(column)
      this.toggleActiveColumn(column.id)
    },

    addGroup () {
      const group = createViewGroup(undefined)
      group.id = getNextViewColumnId(this.view)
      this.view.groups.push(group)
      this.toggleActiveGroup(group.id)
    },

    removeColumn (index) {
      this.view.columns.splice(index, 1)
    },

    toggleActiveColumn (id) {
      const index = this.view.columns.findIndex(column => column.id === this.activeColumnId)
      if (index > -1 && this.view.columns[index].id === undefined) {
        this.view.columns.splice(index, 1)
      }
      if (this.activeColumnId === id) {
        this.activeColumnId = undefined
      } else {
        this.activeColumnId = id
      }
    },

    toggleActiveGroup (id) {
      // If current active group doesn't have a column set, remove it
      const index = this.view.groups.findIndex(group => group.id === this.activeGroupId)
      if (index !== -1 && this.view.groups[index].id === undefined) {
        this.view.groups.splice(index, 1)
      }
      if (this.activeGroupId === id) {
        this.activeGroupId = undefined
      } else {
        this.activeGroupId = id
      }
    },

    groupSortUpdate (event) {
      moveInArray(this.view.groups, event.oldIndex, event.newIndex)
    },

    removeGroup (index) {
      this.view.groups.splice(index, 1)
    },

    // Filter-related methods
    getColumnById (colId) {
      return this.tableStoreColumns.find(col => col.id === Number(colId))
    },

    getColumnType (colId) {
      const col = this.getColumnById(colId)
      return col ? col.type : 'text'
    },

    getColumnHeader (colId) {
      return this.tableStoreGetColumnAutoHeader(Number(colId))
    },

    getColumnIcon (type) {
      return getColumnIcon(type)
    },

    getFilterComponent (colId) {
      const type = this.getColumnType(colId)

      if (type === 'boolean') {
        return BooleanFilter
      } else if (type === 'text') {
        return TextFilter
      } else if (isNumericType(type)) {
        return NumericFilter
      } else if (type === 'datetime') {
        return DateTimeFilter
      }

      return TextFilter
    },

    handleFilterColumnChange () {
      if (this.selectedFilterColumnId !== undefined) {
        this.addFilterToColumn(this.selectedFilterColumnId)
        this.selectedFilterColumnId = '' // Reset the dropdown
      }
    },

    addFilterToColumn (colId) {

      if (!this.view.filters) {
        this.view.filters = {}
      }
      if (!this.view.filters[colId]) {
        this.view.filters[colId] = []
      }

      const type = this.getColumnType(colId)
      let defaultFilter = { operator: 'equals' }
      this.view.filters[colId].push(defaultFilter)
    },

    removeFilter (colId, index) {
      if (this.view.filters && this.view.filters[colId]) {
        this.view.filters[colId].splice(index, 1)

        // If no more filters for this column, remove the column entry
        if (this.view.filters[colId].length === 0) {
          delete this.view.filters[colId]
        }
      }
    },

    removeFilterForColumn (colId) {
      if (this.view.filters) {
        delete this.view.filters[colId]
      }
    },

    // Sorting-related methods
    addSortColumn (columnId) {
      if (!columnId) return
      const numericId = Number(columnId)
      const availableColumn = this.availableSortColumns.find(col => col.id === numericId)
      if (availableColumn) {
        this.sortColumns.push({
          id: availableColumn.id,
          desc: false,
          header: availableColumn.header,
          type: availableColumn.type
        })
      }
    },

    removeSortColumn (index) {
      this.sortColumns.splice(index, 1)
    },

    sortColumnSortUpdate (event) {
      moveInArray(this.sortColumns, event.oldIndex, event.newIndex)
    }

  }
}
</script>

<style scoped>
/*noinspection CssUnusedSymbol*/
.overflow-y-auto {
  scrollbar-width: thin;
}

/*noinspection CssUnusedSymbol*/
.sortable-ghost {
  opacity: 0.5;
  background: #f3f4f6;
}

/*noinspection CssUnusedSymbol*/
.sortable-drag {
  background: white;
}

.drag-handle {
  touch-action: none;
}
</style>
