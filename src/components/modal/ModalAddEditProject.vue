<template>
  <base-modal
      ref="base"
      :title="project ? 'Edit Project' : 'Add New Project'"
      :disable-submit="!formValid"
      @submit="submit">
    <template #body>
      <div
          class="mx-auto flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-fuchsia-100 sm:mx-0 sm:h-10 sm:w-10">
        <Pencil class="h-6 w-6 text-fuchsia-600" aria-hidden="true"/>
      </div>
      <div class="flex-1 mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
        <template v-if="project">
          <dialog-title as="h3" class="text-base font-semibold leading-6 text-gray-900">Edit Project</dialog-title>
          <dialog-description v-if="project" class="text-sm mt-2">
            Rename your project or change project-level settings.
          </dialog-description>
        </template>
        <template v-else>
          <dialog-title as="h3" class="text-base font-semibold leading-6 text-gray-900">Add New Project</dialog-title>
          <dialog-description class="text-sm mt-2">
            Projects are top-level containers for your tables.
          </dialog-description>
        </template>
        <div class="mt-4">
          <form-grid>
            <form-grid-section>
              <form-grid-row label="Give your project a name">
                <input
                    name="value"
                    id="value"
                    autocomplete="off"
                    v-model="title"
                    class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full text-sm border-gray-300 rounded-md"
                    placeholder="Project name"
                />
              </form-grid-row>
            </form-grid-section>
          </form-grid>
        </div>
      </div>
    </template>
  </base-modal>
</template>

<script>
import { Pencil } from 'lucide-vue-next'
import BaseModal from '@/components/modal/BaseModal.vue'
import { DialogDescription, DialogTitle } from '@headlessui/vue'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import { mapActions } from 'pinia'
import { useProjectsStore } from '@/stores/projects.js'

export default {
  components: { FormGridSection, DialogDescription, FormGridRow, FormGrid, DialogTitle, BaseModal, Pencil },

  data () {
    return {
      title: '',
      project: undefined
    }
  },

  computed: {
    formValid () {
      return this.title.length > 0
    }
  },

  methods: {
    ...mapActions(useProjectsStore, {
      projectsStoreCreateNew: 'createNew',
      projectsStoreUpdate: 'update'
    }),

    open (project) {
      if (project) {
        this.project = project
        this.title = project.title
      } else {
        this.project = undefined
        this.title = ''
      }
      this.$refs.base.open()
    },

    async submit () {
      if (!this.formValid) return
      if (this.project) {
        await this.projectsStoreUpdate(this.project.id, { title: this.title })
      } else {
        await this.projectsStoreCreateNew({ title: this.title })
      }
      Object.assign(this.$data, this.$options.data.apply(this))
    }
  }
}
</script>

<style scoped>

</style>
