<template>
  <base-modal
      ref="base"
      large
      @opened="opened = true"
      :title="modalTitle">
    <template #buttons>
      <base-btn class="ml-2" type="primary" :disabled="calcError !== undefined" @click="insertAndClose">
        {{ this.column ? 'Edit Column' : 'Insert Column' }}
      </base-btn>
      <base-btn @click="() => $refs.base.close()">Cancel</base-btn>
      <div v-if="calcError" class="text-red-500 text-sm p-2">{{ calcError }}</div>
    </template>
    <div class="flex gap-y-5 overflow-y-auto max-h-128">
      <div class="shrink-0 w-64 overflow-y-auto">
        <div class="flex flex-col space-y-4">
          <nav class="flex-1 h-full overflow-y-auto">
            <div v-for="cat in Object.keys(calcs)" :key="cat" class="relative">
              <div
                  class="sticky top-0 text-xs z-10 uppercase border-y border-b-zinc-200 border-t-zinc-100 bg-zinc-50 px-3 py-1.5 leading-6 text-zinc-900">
                <h3>{{ cat }}</h3>
              </div>
              <ul role="list" class="divide-y divide-zinc-100">
                <li
                    v-for="calc in calcs[cat]" :key="calc.type"
                    :ref="'calc-' + calc.type"
                    class="flex gap-x-4 px-3 py-2 my-0 cursor-pointer select-none"
                    :class="[selectedCalc === calc.type ? 'bg-fuchsia-100' : 'hover:bg-fuchsia-50']"
                    @click="selectedCalc === calc.type ? selectedCalc = undefined : selectedCalc = calc.type">
                  <div class="min-w-0">
                    <p class="text-sm font-semibold"
                       :class="[selectedCalc === calc.type ? 'text-zinc-900' : 'text-zinc-800']">{{ calc.label }}</p>
                    <p class="mt-1 text-sm"
                       :class="[selectedCalc === calc.type ? 'text-zinc-600' : 'text-zinc-500']">{{ calc.desc }}</p>
                  </div>
                </li>
              </ul>
            </div>
          </nav>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto px-4">
        <form-grid vclass="h-full">
          <form-grid-section title="Configure this calculation">
            <template v-if="calc">
              <form-grid-row label="Column header" stacked>
                <input
                    id="column-header"
                    type="text"
                    name="title"
                    v-model="title"
                    :placeholder="autoTitle"
                    class="block w-full shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-zinc-300 rounded-md"/>
              </form-grid-row>

              <form-grid-row
                  stacked
                  :label="sourceType === 'single' ? 'Select a source column' : 'Select 2 or more source columns'">
                <div class="flex-1 max-h-48 overflow-y-auto">
                  <form-field-columns
                      v-slot="slotProps"
                      :column-ids="sourceColumnOptions">
                    <template v-if="sourceType === 'single'">
                      <input
                          type="radio"
                          :value="slotProps.key"
                          v-model.number="sourceColId"
                          class="border-zinc-300 text-fuchsia-600 focus:ring-fuchsia-600"/>
                    </template>
                    <template v-else-if="sourceType === 'multi'">
                      <input
                          type="checkbox"
                          :value="slotProps.key"
                          v-model.number="sourceColIds"
                          class="focus:ring-fuchsia-500 h-4 w-4 text-fuchsia-600 border-zinc-300 rounded-sm"/>
                    </template>
                  </form-field-columns>
                </div>
              </form-grid-row>

              <form-grid-row stacked v-for="prop in calc.props" :key="prop.key" :label="prop.label">
                <input
                    type="number"
                    v-model="calcData[prop.key]"
                    class="block w-full shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-zinc-300 rounded-md"/>
              </form-grid-row>
            </template>
            <template v-else>
              <p class="text-sm text-zinc-500">Select a calculation on the left to get started.</p>
            </template>
          </form-grid-section>
        </form-grid>
      </div>

      <div v-if="calc && null" class="flex-none flex w-80 pl-4 overflow-y-auto">
        <div class="flex-1 flex flex-col space-y-4">
          <h4 class="pt-4 text-base font-bold">Preview</h4>
          <div class="flex-1 h-full overflow-auto" v-if="opened && sourcesGood">
            <data-hero-grid
                v-if="sourcesGood"
                :rows="previewRows"
                :columns="previewCols"
            />
          </div>
          <div v-else>
            <p class="text-sm text-zinc-500">Select source columns to see a preview of the calculated column.</p>
          </div>
        </div>
      </div>
    </div>
  </base-modal>
</template>

<script>
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import BaseModal from '@/components/modal/BaseModal.vue'
import { calculatedColumns, calculatedColumnsByCategory, cantCalcOnTypes, generateAutoTitle } from '@/utils/calcs.js'
import FormFieldColumns from '@/components/forms/charts/FormFieldColumns.vue'
import { mapActions, mapState } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import DataHeroGrid from '@/components/datahero/DataHeroGrid.vue'
import { hasCircularDependencies } from '@/utils/columngraph.js'

export default {
  emits: ['insert-column', 'change-column'],
  components: {
    DataHeroGrid,
    FormGridRow,
    FormGridSection,
    FormGrid,
    FormFieldColumns,
    BaseModal,
    BaseBtn
  },
  data () {
    return {
      // loading ag-grid before modal has opened causes weird flickering, so we track this
      // with a prop and only show the component once it has opened
      opened: false,

      calcs: calculatedColumnsByCategory,
      selectedCalc: undefined,
      sourceColId: undefined,
      sourceColIds: [],
      calcData: {},
      title: '',
      maxPreviews: 50,
      previewRows: [],
      previewCols: [],
      column: undefined,
      defaultCalc: undefined
    }
  },
  watch: {
    opened (newVal) {
      if (newVal && this.selectedCalc) {
        this.$refs['calc-' + this.selectedCalc][0].scrollIntoView({ block: 'center' })
      }
    },
    selectedCalc (newVal, oldVal) {
      // make sure selected element is scrolled into view
      if (newVal !== undefined) {
        // guard against modal still opening
        const el = this.$refs['calc-' + newVal]
        if (el && el.length) {
          this.$refs['calc-' + newVal][0].scrollIntoView({
            behavior: 'smooth', block: 'center'
          })
        }
      }

      // when we change calc, rebuild the calc data based on defaults
      if (newVal !== undefined && newVal !== oldVal) {
        if (!this.calc.props) {
          this.calcData = undefined
        } else {
          if (this.column && this.selectedCalc === this.column.calc.type) {
            // we have selectedCalc the same calc as before, so keep the existing data
            this.calcData = this.column.calc.data
          } else {
            this.calcData = this.calc.props.reduce((acc, prop) => {
              acc[prop.key] = prop.default
              return acc
            }, {})
          }
        }
      }
    },

    // Changes here to rebuild the preview from the DB
    checkedSourceColumns: 'generatePreview',
    calcData: {
      handler: 'generatePreview',
      deep: true
    }
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreColumns: 'columns'
    }),

    allSourceColIds () {
      if (this.sourceType === 'single') {
        return this.sourceColId === undefined ? [] : [this.sourceColId]
      } else if (this.sourceType === 'multi') {
        return this.sourceColIds
      }
      return []
    },

    modalTitle () {
      if (this.column) {
        return 'Edit calculated column'
      } else {
        return 'Add calculated column'
      }
    },

    calc () {
      if (!this.selectedCalc) return undefined
      return this.calcList[this.selectedCalc]
    },

    sourceType () {
      return this.calc ? this.calc.sources : undefined
    },

    calcList () {
      return calculatedColumns
    },

    sourceColumnHeaders () {
      return this.checkedSourceColumnIds.map(colId => this.tableStoreGetColumnAutoHeader(colId))
    },

    autoTitle () {
      return generateAutoTitle(this.calc, this.sourceColumnHeaders)
    },

    sourceColumnOptions () {
      // get all column Ids except the one we are editing; also remove text and boolean columns
      return this.tableStoreColumns
          .filter(col => col.id !== this.column?.id)
          .filter(col => !cantCalcOnTypes.includes(col.type))
          .map(col => col.id)
    },

    checkedSourceColumns () {
      // Returns column objects of sources selectedCalc, filtering out undefined. This can happen with columns
      // since deleted
      return this.allSourceColIds.map(c => this.tableStoreColumns.find(col => col.id === c)).filter(c => c)
    },

    checkedSourceColumnIds () {
      return this.checkedSourceColumns.map(c => c.id)
    },

    calcError () {
      if (this.sourceType === 'single' && this.checkedSourceColumns.length !== 1) {
        return 'Select a source column.'
      } else if (this.sourceType === 'multi' && this.checkedSourceColumns.length < 2) {
        return 'Select at least two source columns.'
      }

      if (this.column) {
        // If we are editing, then check for circular dependencies; build up a new cols
        const cols = this.checkedSourceColumns.map(c => ({ id: c.id, calc: c.calc }))
        cols.push({
          id: this.column.id || 'newCol',
          calc: {
            type: this.selectedCalc,
            sourceCols: this.checkedSourceColumnIds
          }
        })
        if (hasCircularDependencies(cols)) {
          return 'This calculation would create a circular dependency.'
        }
      }
      return undefined
    }
  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetColumnAutoHeader: 'getColumnAutoHeader'
    }),

    open (editingColumn, defaultCalc) {
      this.opened = false
      this.column = editingColumn
      this.defaultCalc = defaultCalc
      this.$refs.base.open()
      this.loadSettings()
    },

    loadSettings () {
      if (this.column) {
        // If we are editing a column, copy these over
        this.selectedCalc = this.column.calc.type
        this.sourceColIds = this.column.calc.sourceCols
        this.calcData = this.column.calc.data
        this.title = this.column.header
      } else {
        this.selectedCalc = this.defaultCalc.type
        this.sourceColIds = this.defaultCalc.sourceCols || []
        this.calcData = {}
        this.title = ''
      }
      this.sourceColId = this.sourceColIds[0]
    },

    insertAndClose () {
      if (this.calcError) return
      if (this.column) {
        this.$emit('change-column', this.column.id, this.selectedCalc, this.checkedSourceColumnIds, this.calcData, this.title || this.autoTitle)
      } else {
        this.$emit('insert-column', this.selectedCalc, this.checkedSourceColumnIds, this.calcData, this.title || this.autoTitle)
      }
      this.$refs.base.close()
    },

    async generatePreview () {
      return {}
    }
  }
}
</script>

<style scoped>
.overflow-y-auto {
  scrollbar-width: thin;
}
</style>
