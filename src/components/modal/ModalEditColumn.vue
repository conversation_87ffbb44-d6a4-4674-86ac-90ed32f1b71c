<template>
  <base-modal
      ref="base"
      title="Change this column"
      @submit="$emit('edit-column', this.newColumn, this.column)">
    <form-grid>
      <form-grid-section>
        <form-grid-row label="Header">
          <input type="text" v-model="newColumn.header"
                 class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-fuchsia-600 sm:text-sm sm:leading-6">
        </form-grid-row>

        <template v-if="!column.inView">
          <form-grid-row label="Type">
            <select
                v-model.number="newColumn.type"
                @change="setDefaultColProps"
                class="max-w-lg block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md">
              <option
                  v-for="ct in columnTypes"
                  :value="ct.type"
                  :key="ct.type">{{ ct.label }}
              </option>
            </select>
          </form-grid-row>
        </template>

        <template v-else-if="column.isGroup">
          <form-grid-row label="Group on">
            <span class="block pt-1.5 text-sm font-semibold">{{ targetName }}</span>
          </form-grid-row>
          <form-grid-row v-if="targetColumn.type === 'datetime'" label="Group by">
            <select
                v-model="newColumn.by"
                class="max-w-lg block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md">
              <option
                  v-for="[key, { label }] in Object.entries(dateGrouping)"
                  :value="key"
                  :key="key">{{ label }}
              </option>
            </select>
          </form-grid-row>
        </template>

        <template v-else-if="!column.inGroupedView">
          <form-grid-row label="Column">
            <span class="block pt-1.5 text-sm font-semibold">{{ targetName }}</span>
          </form-grid-row>
        </template>

        <template v-else>
          <form-grid-row label="Summary of">
            <span class="block pt-1.5 text-sm font-semibold">{{ targetName }}</span>
          </form-grid-row>
          <form-grid-row label="Summarize by" v-if="newColumn.sourceColumnId !== 'id'">
            <select
                v-model.number="newColumn.summarize"
                class="max-w-lg block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md">
              <option
                  v-for="agg in columnAggregates"
                  :value="agg.key"
                  :key="agg.key">{{ agg.label }}
              </option>
            </select>
          </form-grid-row>
        </template>

        <form-grid-row label="Aggregate by">
          <select
              v-model="newColumn.aggregate"
              class="max-w-lg block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md">
            <option :value="undefined">(No aggregation)</option>
            <option
                v-for="agg in columnAggregates"
                :value="agg.key"
                :key="agg.key">{{ agg.label }}
            </option>
          </select>
        </form-grid-row>
      </form-grid-section>

      <template v-if="!newColumn.inView">
        <form-grid-section v-if="newColumn.type === 'number' || newColumn.type === 'percent'" title="Number formatting">
          <form-grid-row label="Decimal places">
            <input
                type="number"
                v-model.number="newColumn.props.decimals"
                class="block rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-fuchsia-600 sm:text-sm sm:leading-6">
          </form-grid-row>
          <form-grid-row label="Pad decimals?">
            <input
                type="checkbox"
                v-model="newColumn.props.decimalsPad"
                class="mt-2 focus:ring-fuchsia-500 h-4 w-4 text-fuchsia-600 border-gray-300 rounded-sm">
          </form-grid-row>
          <form-grid-row label="Thousands separator">
            <input
                type="checkbox"
                v-model="newColumn.props.thousands"
                class="mt-2 focus:ring-fuchsia-500 h-4 w-4 text-fuchsia-600 border-gray-300 rounded-sm">
          </form-grid-row>
          <form-grid-row label="Compact display?">
            <input
                type="checkbox"
                v-model="newColumn.props.compact"
                class="mt-2 focus:ring-fuchsia-500 h-4 w-4 text-fuchsia-600 border-gray-300 rounded-sm">
          </form-grid-row>

        </form-grid-section>
        <form-grid-section v-if="newColumn.type === 'currency'" title="Currency formatting">
          <form-grid-row label="Currency">
            <base-combo
                @change="setCurrencyToLocale"
                v-model="newColumn.props.locale"
                :filter="filterCurrencies"
                key-attr="locale"
                value-attr="locale">
              <template v-slot="{ item, active, selected }">
                <div class="flex">
                      <span :class="['truncate', selected && 'font-semibold']">
                        {{ item.name }}
                      </span>
                  <span :class="['ml-2 truncate text-gray-500', active ? 'text-fuchsia-200' : 'text-gray-500']">
                        {{ item.currency }}
                      </span>
                </div>
              </template>
            </base-combo>
          </form-grid-row>
          <form-grid-row label="Accounting?">
            <input type="checkbox" v-model="newColumn.props.accounting"
                   class="mt-2 focus:ring-fuchsia-500 h-4 w-4 text-fuchsia-600 border-gray-300 rounded-sm">
          </form-grid-row>
          <form-grid-row label="Currency display">
            <select
                v-model="newColumn.props.display"
                class="max-w-lg block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md">
              <option
                  v-for="cd in currencyDisplayOptions"
                  :value="cd.value"
                  :key="cd.value">{{ cd.label }}
              </option>
            </select>
          </form-grid-row>
        </form-grid-section>
        <form-grid-section v-if="newColumn.type === 'datetime'" title="Date formatting">
          <form-grid-row label="Date type">
            <select
                v-model="newColumn.props.dateType"
                class="max-w-lg block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:max-w-xs sm:text-sm border-gray-300 rounded-md"
                @change="setDateDefaultFormat">
              <option v-for="dt in dateTypes" :value="dt.key" :key="dt.key">{{ dt.label }}</option>
            </select>
          </form-grid-row>
          <form-grid-row label="Date format" class="mb-16">
            <base-combo
                v-model="newColumn.props.format"
                :filter="filterDateFormats"
                key-attr="format"
                value-attr="format">
              <template v-slot="{ item, active, selected }">
                <div class="flex">
                      <span :class="['truncate', selected && 'font-semibold']">
                        {{ item.example }}
                      </span>
                  <span :class="['ml-2 truncate text-gray-500', active ? 'text-fuchsia-200' : 'text-gray-500']">
                        {{ item.format }}
                      </span>
                </div>
              </template>
            </base-combo>
          </form-grid-row>
        </form-grid-section>
      </template>
    </form-grid>
  </base-modal>
</template>

<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import { format } from 'date-fns'
import { mapActions } from 'pinia'
import { createColumn, createViewColumn, createViewGroup, useTableStore } from '@/stores/table.js'
import { columnTypes, getDefaultColProps } from '@/utils/formats.js'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import { currencies, currencyDisplayOptions } from '@/utils/currencies.js'
import BaseCombo from '@/components/forms/input/BaseCombo.vue'
import { dateFormats, dateTypes } from '@/utils/dates.js'
import { getAggregateLabelsForColType } from '@/utils/aggregation.js'
import { dateGrouping } from '@/db/grouping.js'

export default {
  name: 'ModalEditColumn',
  components: {
    BaseCombo,
    FormGridSection,
    FormGridRow,
    FormGrid,
    BaseModal
  },
  emits: ['edit-column'],

  data () {
    return {
      column: Object,
      currencyDisplayOptions,
      columnTypes,
      dateTypes,
      newColumn: Object
    }
  },

  computed: {
    dateGrouping () {
      return dateGrouping
    },

    targetColumn () {
      // In a view, either a group or column will have an `id` that refers back to the og table as the 'target'
      if (!this.column.inView) return
      return this.tableStoreGetColumn(this.newColumn.sourceColumnId)
    },

    targetName () {
      if (!this.column.inView) return
      if (this.newColumn.sourceColumnId === 'id') {
        return 'Row count'
      } else {
        return this.tableStoreGetColumnAutoHeader(this.newColumn.sourceColumnId)
      }

    },

    columnAggregates () {
      const col = (this.targetColumn || this.newColumn)
      const colType = col.sourceColumnId === 'id' ? 'number' : col.type
      return getAggregateLabelsForColType(colType)
    }
  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetColumn: 'getColumn',
      tableStoreGetColumnAutoHeader: 'getColumnAutoHeader'
    }),

    open (column) {
      this.column = column
      this.setDefaults()
      this.$refs.base.open()
    },

    setDefaults () {
      // Even if editing, we create a new column so we're not editing the original that
      // will be immediately reflected in the store. That way if we cancel, changes are discarded
      if (this.column.inView && this.column.isGroup) {
        this.newColumn = createViewGroup(this.column.sourceColumnId, this.column.id)
      } else if (this.column.inView) {
        this.newColumn = createViewColumn(this.column.sourceColumnId, this.column.id)
      } else {
        this.newColumn = createColumn(this.column.id)
      }

      // Clone all values from the column passed in, skipping id (so we don't overwrite the above)
      Object.keys(this.newColumn).filter(k => k !== 'id').forEach(k => {
        this.newColumn[k] = this.column[k]
      })

      // If not a view column, clone anything in props - if empty, set defaults
      if (!this.column.inView) {
        this.newColumn.props = Object.assign({}, this.column.props)
        if (!this.newColumn.props) {
          this.newColumn.props = getDefaultColProps(this.newColumn.type)
        }
      }
    },

    /*
    These 3 methods are called when the user changes the column type, date type or currency locale.

    We do this instead of with watchers because we don't want to trigger if we set this
    programmatically.
    */
    setDefaultColProps () {
      // When changing the column, if we're changing it back to what we were editing, bring back those options
      // Otherwise get the default.
      if (this.newColumn.type === this.column.type) {
        this.newColumn.props = Object.assign({}, this.column.props)
      } else {
        this.newColumn.props = getDefaultColProps(this.newColumn.type)
      }
    },

    setCurrencyToLocale () {
      this.newColumn.props.currency = currencies.find(cdo => cdo.locale === this.newColumn.props.locale).currency
    },

    setDateDefaultFormat () {
      this.newColumn.props.format = dateTypes[this.newColumn.props.dateType].defaultFormat
    },

    filterCurrencies (q) {
      return currencies.filter((curr) => {
        return (
            curr.locale.toLowerCase().includes(q) ||
            curr.currency.toLowerCase().includes(q) ||
            curr.name.toLowerCase().includes(q)
        )
      })
    },

    filterDateFormats (q) {
      if (!this.newColumn.props.dateType) return []
      const formats = dateFormats[this.newColumn.props.dateType]
      return formats.filter(df => df.toLowerCase().includes(q)).map(format => {
        return {
          format,
          example: this.demoFormat(format)
        }
      })
    },

    demoFormat (fmt) {
      // use date-fns to try out the format with today's date
      return format(new Date(), fmt)
    }

  }
}
</script>

<style scoped>

</style>
