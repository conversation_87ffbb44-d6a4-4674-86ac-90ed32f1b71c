<template>
  <base-modal
      ref="base"
      :title="title"
      :disable-submit="!formValid"
      :submit-method="submit">
    <template #body>
      <div
          class="mx-auto flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-fuchsia-100 sm:mx-0 sm:h-10 sm:w-10">
        <Pencil class="h-6 w-6 text-fuchsia-600" aria-hidden="true"/>
      </div>
      <div class="flex-1 mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
        <dialog-title as="h3" class="text-base font-semibold leading-6 text-gray-900">Add New Table</dialog-title>
        <dialog-description v-if="project" class="text-sm mt-2">Create a new blank table for project
          <em>{{ project.title }}</em>.
        </dialog-description>
        <dialog-description v-else class="text-sm mt-2">Create a new blank table.</dialog-description>
        <div class="mt-4">
          <form-grid>
            <form-grid-section>
              <form-grid-row label="Give your table a title">
                <input
                    name="value"
                    id="value"
                    autocomplete="off"
                    v-model="title"
                    class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full text-sm border-gray-300 rounded-md"
                    placeholder="Table name"
                />
              </form-grid-row>
              <form-grid-row label="Columns">
                <input
                    type="number"
                    name="value"
                    id="value"
                    autocomplete="off"
                    v-model.number="cols"
                    class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full text-sm border-gray-300 rounded-md"
                    placeholder="3"
                />
              </form-grid-row>
              <form-grid-row label="Rows">
                <input
                    type="number"
                    name="value"
                    id="value"
                    autocomplete="off"
                    v-model.number="rows"
                    class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full text-sm border-gray-300 rounded-md"
                    placeholder="3"
                />
              </form-grid-row>
            </form-grid-section>
          </form-grid>
        </div>
      </div>
    </template>
  </base-modal>
</template>

<script>
import { Pencil } from 'lucide-vue-next'
import BaseModal from '@/components/modal/BaseModal.vue'
import { DialogDescription, DialogTitle } from '@headlessui/vue'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import { mapActions } from 'pinia'
import { useProjectsStore } from '@/stores/projects.js'

export default {
  name: 'ModalAddNewTable',
  components: { FormGridSection, DialogDescription, FormGridRow, FormGrid, DialogTitle, BaseModal, Pencil },

  data () {
    return {
      title: '',
      project: undefined,
      rows: 3,
      cols: 3
    }
  },

  computed: {
    formValid () {
      return this.rows > 0 && this.cols > 0
    }
  },

  methods: {
    ...mapActions(useProjectsStore, {
      projectsStoreCreateNewBlankTable: 'createNewBlankTable'
    }),

    open (project) {
      this.project = project
      this.$refs.base.open()
    },

    async submit () {
      if (!this.formValid) return
      const tableId = await this.projectsStoreCreateNewBlankTable(this.title, this.project?.id, this.cols, this.rows)
      await this.$router.push({ name: 'app-table', params: { tableId } })

      // reset component - do this after waiting for router re-route; gives time for modal to close so user doesn't see
      // a flash of data changing
      Object.assign(this.$data, this.$options.data.apply(this))
      return true
    }
  }
}
</script>

<style scoped>

</style>
