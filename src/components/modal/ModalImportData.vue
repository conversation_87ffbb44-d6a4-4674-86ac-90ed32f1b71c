<template>
  <base-modal ref="base" :xl="step === 'preview'">
    <teleport to="body">
      <base-dropzone v-if="step !== 'parsing'" @file-drop="fileDrop"/>
    </teleport>

    <template #buttons v-if="step === 'preview'">
      <base-btn class="ml-2" type="primary" :disabled="!rows" @click="submit">Create table</base-btn>
    </template>
    <template #buttons v-else>
      <base-btn
          class="ml-2"
          type="primary"
          :loading="step === 'parsing'"
          :disabled="pastedData === ''"
          @click="importData">Preview &raquo;
      </base-btn>
    </template>

    <div v-if="step !== 'preview'">
      <div v-if="step === 'parsing' && importFile" class="space-y-3">
        <dialog-title as="h3" class="text-lg text-gray-900">
          <strong class="font-semibold">Importing a new file...</strong>
        </dialog-title>
        <div class="flex flex-row space-x-2 items-center">
          <div class="flex-none flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
            <ArrowUpFromDot class="w-6 h-6 text-blue-600"/>
          </div>
          <div class="flex-1 text-base font-mono">{{ importFile.name }}</div>
        </div>
      </div>
      <div v-else class="space-y-3">
        <dialog-title as="h3" class="text-lg text-gray-900">
          <strong class="font-semibold">Paste in new data here</strong>
        </dialog-title>
        <div class="flex flex-row space-x-2">
          <div class="flex-none flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
            <Lightbulb class="w-6 h-6 text-green-600"/>
          </div>
          <div class="flex-1 text-sm">
            Try pasting in data from a spreadsheet like Excel, or a table from a website.<br>
            <label for="file-upload"
                   class="relative cursor-pointer rounded-md bg-white font-semibold text-fuchsia-600 focus-within:outline-hidden focus-within:ring-2 focus-within:ring-fuchsia-600 focus-within:ring-offset-2 hover:text-fuchsia-500">
              <span>Upload a file by clicking here</span>
              <input id="file-upload" name="file-upload" type="file" class="sr-only"
                     @change="e => fileDrop(e.target.files)"/>
            </label> or dragging it onto the page.
          </div>
        </div>
        <div class="mt-2">
          <textarea
              tabindex="1"
              v-model="pastedData"
              class="block whitespace-pre w-full h-72 shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-300 disabled:ring-gray-200"
              :disabled="step === 'parsing'"
          ></textarea>
        </div>
      </div>
    </div>

    <template v-else>
      <div class="flex space-x-4 h-[70vh]">
        <div class="max-w-2xl overflow-auto bg-gray-50 p-2">
          <form-grid class="divide-none gap-y-0">

            <form-grid-section title="Customize the columns">
              <div class="py-4 px-2">
                <base-toggle v-model="firstRowIsHeader" label="Use the first row as a table header"/>
              </div>

              <div
                  class="pt-4 flex items-stretch text-center text-xs uppercase font-semibold text-gray-600 space-x-2 p-2">
                <div class="flex-none w-12">Show?</div>
                <div class="flex-1">Column Name</div>
                <div class="flex-1">Type</div>
                <div class="flex-none w-12">Label</div>
              </div>

              <div v-for="(col, index) in cols" :key="col.id"
                   class="pt-2 flex items-stretch text-center text-sm space-x-2 p-2">
                <div class="flex-none w-12">
                  <base-toggle v-model="colsShown[index]" @click="setShowAllColumns"/>
                </div>
                <div class="flex-1">
                  <input type="text"
                         v-model="col.header"
                         :placeholder="getColumnAutoHeaderFromIndex(index)"
                         :disabled="!colsShown[index]"
                         class="block w-full shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-300 disabled:ring-gray-200"/>
                </div>
                <div class="shrink-0">
                  <select
                      :disabled="!colsShown[index]"
                      v-model="col.type"
                      @change="detectColProps(col)"
                      class="block w-full shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-300 disabled:ring-gray-200">
                    <option v-for="type in columnTypes"
                            :key="type.type"
                            :value="type.type">{{ type.label }}
                    </option>
                  </select>
                </div>

                <div class="flex-none w-12">
                  <input v-model.number="labelCol"
                         :disabled="!colsShown[index]"
                         :id="index" :value="index" name="labelCol" type="radio"
                         class="mt-3 h-4 w-4 border-gray-300 text-fuchsia-600 focus:ring-fuchsia-600 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-300 disabled:ring-gray-200"/>
                </div>

              </div>

              <div class="pt-4 flex items-stretch text-center text-sm space-x-2 p-2">
                <div class="flex-1 text-left">
                  <base-toggle @click="toggleShowAll" v-model="showAllColumns" label="Show/hide all"/>
                </div>
                <div class="mt-2 flex-1 text-right"><label for="-1">No default chart label</label></div>
                <div class="mt-2 flex-none w-12">
                  <input
                      type="radio"
                      name="labelCol"
                      v-model="labelCol"
                      :value="-1"
                      class="h-4 w-4 border-gray-300 text-fuchsia-600 focus:ring-fuchsia-600"/>
                </div>
              </div>

            </form-grid-section>
          </form-grid>

        </div>
        <div class="flex-1 flex flex-col space-y-4 overflow-auto">
          <div class="flex-none flex-row flex items-end">
            <div class="flex-1">
              <input type="text"
                     v-model="title"
                     placeholder="Give your table a title"
                     class="h-full w-full border-transparent p-0 text-base text-gray-900 focus:outline-hidden focus:ring-0 focus:border-transparent focus:placeholder-gray-400">
            </div>
            <div class="flex-none text-xs mt-4">
              <strong>previewing </strong>
              <template v-if="maxPreview >= totalRows">all {{ totalRows }} rows</template>
              <template v-else>{{ maxPreview }} rows of {{ totalRows }}</template>
            </div>
          </div>
          <div class="flex-1 h-full overflow-auto">
            <data-hero-grid
                :rows="previewRows"
                :columns="shownCols"
                :label-column-id="labelCol"
            />
          </div>
        </div>
      </div>

    </template>
  </base-modal>
</template>

<script>
import { createColumn } from '@/stores/table'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { mapActions } from 'pinia'
import BaseModal from '@/components/modal/BaseModal.vue'
import { parseData, parseFile } from '@/utils/parse.js'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import FormGrid from '@/components/forms/FormGrid.vue'
import FormGridSection from '@/components/forms/FormGridSection.vue'
import { columnTypes, convert, isNumericType } from '@/utils/formats.js'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import { DialogOverlay, DialogTitle } from '@headlessui/vue'
import { ArrowUpFromDot, Lightbulb } from 'lucide-vue-next'
import BaseDropzone from '@/components/ui/BaseDropzone.vue'
import { getExcelColumnName } from '@/utils/helpers.js'
import { useProjectsStore } from '@/stores/projects.js'
import { detect } from '@/utils/detect.js'
import DataHeroGrid from '@/components/datahero/DataHeroGrid.vue'

export default {
  name: 'ModalImportData',
  components: {
    DataHeroGrid,
    DialogTitle,
    BaseDropzone,
    DialogOverlay,
    BaseToggle,
    FormGridSection,
    FormGrid,
    FormGridRow,
    BaseModal,
    BaseBtn,
    Lightbulb,
    ArrowUpFromDot
  },

  data () {
    return {

      // these are set by parent calling "open"
      project: undefined,

      // Internal state for importing
      columnTypes,
      pastedData: '',
      step: 'entry',
      dropTarget: false,
      importFile: undefined,

      // User customizable properties
      title: '',
      firstRowIsHeader: false,
      showAllColumns: true,

      // raw rows and columns from the parser
      rows: [],
      cols: [],

      // converted, but only the top X
      maxPreview: 50,

      // filtered list of cols that are shown
      shownCols: [],

      // array of true/false for each col
      colsShown: [],

      // user-selected column designated as the label
      labelCol: -1

    }
  },

  computed: {
    previewRows () {
      return this.convertRows(this.rows, this.firstRowIsHeader, this.maxPreview)
    },

    totalRows () {
      return this.rows.length - (this.firstRowIsHeader ? 1 : 0)
    }
  },

  mounted () {
    // Quick way to load in some data when testing this modal
    /*
    this.open(undefined, `String,Number,Perc,Currency
Stuff,0.4,40%,$1.23
Things,0.6,60%,$0.5
Other,0.824,82.4%,$0.99
Cheese?,0.234,23.4%,$1.23
One more for threshold,0.999,99.9%,$0.99
Lol boolean!,1,1,$102.22
`) */
  },

  watch: {
    firstRowIsHeader () {
      this.cols.forEach((col, index) => {
        col.header = this.getRowHeader(index)
      })
    },

    colsShown: {
      handler: 'setShownCols',
      deep: true
    },
    cols: {
      handler: 'setShownCols',
      deep: true
    },
    labelCol: 'setShownCols'
  },

  methods: {
    ...mapActions(useProjectsStore, {
      projectsStoreCreateNewTable: 'createNewTable'
    }),

    getRowHeader (index) {
      if (!this.firstRowIsHeader) {
        return ''
      } else {
        return this.rows[0][index].trim()
      }
    },

    detectColProps (col) {
      // Given the user-selected col type, do our best to detect the props
      const stringData = this.rows.map(row => row[col.id])
      const detection = detect[col.type](stringData)
      col.props = detection[0].props
      if (isNumericType(col.type)) {
        col.props.force = true
      }
    },

    open (project, dataToImport) {
      Object.assign(this.$data, this.$options.data.apply(this))
      this.$refs.base.open()
      this.project = project

      // if dataToImport is string, run thru importdata; otherwise assume file drop
      if (typeof dataToImport === 'string') {
        this.pastedData = dataToImport
        this.importData()
      } else if (dataToImport) {
        this.fileDrop(dataToImport)
      }
    },

    importData () {
      this.step = 'parsing'
      this.processParser(parseData(this.pastedData))
    },

    async fileDrop (files) {
      this.step = 'parsing'
      this.importFile = files[0]
      const parser = await parseFile(this.importFile)
      this.processParser(parser)
    },

    getColumnAutoHeaderFromIndex (index) {
      return getExcelColumnName(this.shownCols.findIndex(col => col.id === index))
    },

    processParser (parser) {
      if (!parser) {
        this.step = 'entry'
        this.rows = []
        this.cols = []
        return
      }
      this.step = 'preview'
      this.rows = parser.rowData
      this.firstRowIsHeader = parser.hasHeader
      this.cols = parser.colDefs.map((colDef, index) => {
        const col = createColumn(index, undefined, colDef.type, index === this.labelCol, colDef.props)
        col.header = this.getRowHeader(index)
        return col
      })
      this.title = parser.title
      this.colsShown = parser.colDefs.map(colDef => !colDef.hide) // hide all cols recommended by parser
      this.setShownCols()
    },

    setShowAllColumns () {
      // This is called when a column is shown or hidden. We do this manually because we set this button's
      // state dynamically based on the state of the other buttons.
      this.showAllColumns = this.colsShown.every(col => col)
    },

    /* Take the rows and convert it by the column type  */
    convertRows (rowData, firstRowIsHeader, limit = 0) {
      const sliced = () => {
        if (limit > 0) {
          return rowData.slice(firstRowIsHeader ? 1 : 0, limit)
        } else if (firstRowIsHeader) {
          return rowData.slice(1)
        } else {
          return rowData
        }
      }
      return sliced().map((rowData, rowIndex) => {
        const row = { id: rowIndex }
        rowData.forEach((cell, colIndex) => {
          if (cell !== undefined && this.colsShown[colIndex]) {
            row[`c${colIndex}`] = convert(cell, this.cols[colIndex].type, this.cols[colIndex].props) ?? cell
          }
        })
        return row
      })
    },

    toggleShowAll () {
      // This is called when the show/hide all switch is clicked. We do this manually because we set this button's
      // state dynamically based on the state of the other buttons.
      if (this.showAllColumns) {
        this.colsShown = this.colsShown.map(() => true)
      } else {
        this.colsShown = this.colsShown.map(() => false)
      }
    },

    setShownCols () {
      // first go thru and set isLabelCol on the right col
      this.cols.forEach((col, index) => {
        col.isLabel = index === this.labelCol
      })
      this.shownCols = this.cols.filter((_, i) => this.colsShown[i])
    },

    async submit () {
      // We create rows from calculated data to get all the rows, then filter out the ones that aren't shown
      const rows = this.convertRows(this.rows, this.firstRowIsHeader, 0)

      const tableId = await this.projectsStoreCreateNewTable(this.title, this.project?.id, this.shownCols, rows)
      if (tableId) {
        this.$router.push({
          name: 'app-table',
          params: { tableId }
        })
        this.$refs.base.close()
      }
    }
  }
}
</script>
