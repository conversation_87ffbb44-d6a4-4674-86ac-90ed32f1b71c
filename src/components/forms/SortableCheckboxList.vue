<template>
  <div class="max-w-lg space-y-2 py-1 pl-1 max-h-64 overflow-y-auto">
    <transition-group name="list" tag="div">
      <template v-if="modelValue.length">
        <sortable
            key="checked"
            :list="checkedItems"
            :options="sortableOptions"
            @end="handleSort"
            item-key="item.uuid"
            class="flex flex-col -space-y-[1px]">
          <template #item="{ element: opt }">
            <div
                :key="opt.uuid"
                class="relative flex items-center py-1.5 px-2 border border-fuchsia-200 bg-white  border-l-fuchsia-500 border-l-4 shadow-xs first:rounded-t-md last:rounded-b-md">
              <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 mr-2">
                <svg class="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M4 8h16M4 16h16"/>
                </svg>
              </div>
              <div class="text-sm flex-1">
                <label class="font-medium text-gray-700">
                  <input
                      type="checkbox"
                      :value="opt.id"
                      :checked="true"
                      @change="toggleItem(opt)"
                      class="focus:ring-fuchsia-500 h-4 w-4 text-fuchsia-600 border-gray-300 rounded-sm"
                  />
                  <span class="ml-2">{{ opt.label }}</span>
                </label>
              </div>
            </div>
          </template>
        </sortable>
      </template>

      <!-- Unchecked Items -->
      <div class="flex flex-col -space-y-[1px] mt-1" key="unchecked">
        <div
            v-for="opt in uncheckedItems"
            :key="opt.uuid"
            class="relative flex items-center py-1.5 px-2 border border-fuchsia-200 bg-white shadow-xs first:rounded-t-md last:rounded-b-md">
          <div class="text-gray-400 hover:text-gray-600 mr-2">
            <div class="size-4"/>
          </div>
          <div class="text-sm flex-1">
            <label class="font-medium text-gray-700">
              <input
                  type="checkbox"
                  :value="opt.id"
                  :checked="false"
                  @change="toggleItem(opt)"
                  class="focus:ring-fuchsia-500 h-4 w-4 text-fuchsia-600 border-gray-300 rounded-sm"
              />
              <span class="ml-2">{{ opt.label }}</span>
            </label>
          </div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { Sortable } from 'sortablejs-vue3'
import { moveInArray } from '@/utils/helpers.js'

export default {
  emits: ['update:modelValue'],
  name: 'SortableCheckboxList',
  components: { Sortable },
  props: {
    options: {
      type: Array,
      required: true
    },
    modelValue: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      sortableOptions: {
        handle: '.drag-handle',
        animation: 150,
        ghostClass: 'sortable-ghost',
        dragClass: 'sortable-drag'
      },
      internalOptions: []
    }
  },
  watch: {
    options: {
      handler: 'updateInternalOptions',
      deep: true,
      immediate: true
    }
  },
  methods: {
    updateInternalOptions () {
      this.internalOptions = this.options.map(opt => ({
        ...opt,
        uuid: self.crypto.randomUUID()
      }))
    },
    toggleItem (item) {
      const newValue = [...this.modelValue]
      const index = newValue.indexOf(item.id)
      if (index === -1) {
        newValue.push(item.id)
      } else {
        newValue.splice(index, 1)
      }
      this.$emit('update:modelValue', newValue)
    },
    handleSort (event) {
      const { oldIndex, newIndex } = event
      const newValue = [...this.modelValue]
      moveInArray(newValue, oldIndex, newIndex)
      this.$emit('update:modelValue', newValue)
    },
    getItemKey (item) {
      return item.uuid
    }
  },
  computed: {
    checkedItems () {
      return this.internalOptions
          .filter(opt => this.modelValue.includes(opt.id))
          .sort((a, b) => this.modelValue.indexOf(a.id) - this.modelValue.indexOf(b.id))
    },
    uncheckedItems () {
      return this.internalOptions.filter(opt => !this.modelValue.includes(opt.id))
    }
  }
}
</script>

<style scoped>

.list-move {
  transition: all .2s cubic-bezier(0.55, 0, 0.1, 1);
}

.drag-handle {
  touch-action: none;
}

</style>
