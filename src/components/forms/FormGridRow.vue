<template>
  <div class="py-2 grid grid-cols-3 items-start gap-x-2">
    <label
        :for="autoInputId"
        class="block text-sm font-medium leading-6 text-gray-900"
        :class="stacked ? 'col-span-3' : ''">
      <span v-if="label" class="block pt-1.5">{{ label }}</span>
      <slot v-else name="label"></slot>
    </label>
    <div class="mt-2" :class="stacked ? 'col-span-3' : 'sm:col-span-2 sm:mt-0'">
      <slot :input-id="autoInputId"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormGridRow',
  props: {
    /* If stacked, we do label on top of control rather than in columns */
    stacked: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    inputId: {
      type: String,
      default: ''
    }
  },
  computed: {
    autoInputId () {
      if (this.inputId) {
        return this.inputId
      }
      if (this.label) {
        return this.label.toLowerCase().replace(/ /g, '-').replace(/[()]/g, '')
      }
      // get a random 16-char lowercase string
      return Math.random().toString(36).substring(2, 15)
    }
  }
}
</script>

<style scoped>

label {
}
</style>
