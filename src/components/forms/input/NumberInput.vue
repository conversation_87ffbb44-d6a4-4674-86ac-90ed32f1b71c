<template>
  <div class="mt-1 relative rounded-md shadow-xs">
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <component :is="icon" class="size-4 text-gray-400" aria-hidden="true"/>
    </div>

    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
      <X
          v-if="input"
          class="size-4 text-gray-400 hover:cursor-pointer"
          aria-hidden="true"
          @click="clear"
      />
    </div>

    <input
        ref="input"
        type="text"
        :value="input"
        @input="updateValue($event.target.value)"
        :placeholder="placeholder"
        :class="[
      'focus:ring-fuchsia-500 focus:border-fuchsia-500  block w-full px-8 sm:text-sm border-gray-300 rounded-md',
      { 'border-red-500 text-red-500': input !== '' && parseNumber(input) === null }
    ]"
    />
  </div>
</template>

<script>

/**
 * NumberInput
 *
 * This is a wrapper around an input field that handles parsing numbers.
 *
 * Parsed can be guaranteed either null (empty or invalid input) or a Big object.
 */
import { X } from 'lucide-vue-next'
import { getColumnIcon } from '@/utils/colTypeComponents.js'
import { columnTypes } from '@/utils/formats.js'

export default {
  name: 'NumberInput',
  emits: ['update:input', 'update:parsed'],
  components: { X },
  props: {
    // Maintains the raw input value, always as string, may not be a valid number
    input: {
      type: String,
      default: ''
    },

    // Is always either null or a Big object (if successfully parsed)
    parsed: {
      default: null
    },

    placeholder: {
      type: String,
      default: ''
    },

    // add a 'type' prop which can be percent or currency or number; check on one of those values
    type: {
      type: String,
      default: 'number',
      validator: (value) => ['number', 'percent', 'currency'].includes(value)
    }
  },

  computed: {
    icon () {
      return getColumnIcon(this.type)
    }
  },

  watch: {
    input: {
      immediate: true,
      handler (value) {
        this.$emit('update:parsed', this.parseNumber(value))
      }
    }
  },

  methods: {
    clear () {
      this.updateValue('')
      this.$refs.input.focus()
    },

    parseNumber (value) {
      return columnTypes[this.type].convert(value)
    },

    updateValue (value) {
      this.$emit('update:input', value)
    }
  }
}
</script>
