<template>
  <VueDatePicker

      ref="editor"
      model-type="iso"
      menu-class="datepicker"

      v-model="internalValue"
      :month-picker="!formatHasDay && !formatHasTime"
      :time-picker="formatHasTime && !formatHasDay"
      :enable-time-picker="formatHasTime"
      :enable-seconds="formatHasSeconds"
      :enable-input="true"
      :text-input="true"
      :utc="true"
      :range="range"
      :format="format"
      :teleport="true"
      :teleport-center="false"
      :auto-apply="autoApply"
      :ui="{ input: inputClasses }"
      :config="{
        closeOnAutoApply,
        closeOnClearValue: true,
        setDateOnMenuClose: true,
        keepActionRow: false
       }"
  >
    <template #input-icon>
      <Calendar class="size-4 ml-2"/>
    </template>
    <template #clear-icon>
      <X class="size-4 mx-2" @click.prevent="clear"/>
    </template>
    <template #clock-icon>
      <Clock class="size-4"/>
    </template>
    <template #calendar-icon>
      <Calendar class="size-4"/>
    </template>

  </VueDatePicker>

</template>

<script>

import VueDatePicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'
import { columnTypes } from '@/utils/formats.js'
import { formatHasDay, formatHasSeconds, formatHasTime } from '@/utils/dates.js'
import { Calendar, Clock, X } from 'lucide-vue-next'
import equal from 'fast-deep-equal'

export default {
  name: 'InputDateTime',
  emits: ['update:modelValue'],
  components: {
    VueDatePicker,
    Calendar,
    X,
    Clock
  },

  props: {
    openOnMount: {
      type: Boolean,
      default: false
    },
    format: {
      type: String,
      required: true
    },
    modelValue: {
      type: [Number, String, Array],
      default: null
    },
    range: {
      type: Boolean,
      default: false
    },
    autoApply: {
      type: Boolean,
      default: false
    },
    closeOnAutoApply: {
      type: Boolean,
      default: false
    },
    inputClasses: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      internalValue: null
    }
  },

  computed: {
    formatHasDay () {
      return formatHasDay(this.format)
    },
    formatHasTime () {
      return formatHasTime(this.format)
    },
    formatHasSeconds () {
      return formatHasSeconds(this.format)
    }
  },

  watch: {
    modelValue: {
      immediate: true,
      handler (newVal, oldVal) {
        // handle modelValue as an array of 2 values; set internal value to array of ISO strings
        if (equal(newVal, oldVal)) return
        if (Array.isArray(this.modelValue)) {
          this.internalValue = this.modelValue.map(v => columnTypes.datetime.toDate(v)?.toISOString())
          return
        }
        this.internalValue = columnTypes.datetime.toDate(this.modelValue)?.toISOString()
      }
    },

    internalValue: {
      handler (newVal, oldVal) {
        if (equal(newVal, oldVal)) return
        if (Array.isArray(newVal)) {
          const vals = newVal.map(v => v ? new Date(v).getTime() : null)
          if (vals.some(v => v === null)) {
            this.$emit('update:modelValue', null)
            return
          }
          this.$emit('update:modelValue', vals)
          return
        }
        this.$emit('update:modelValue', newVal ? new Date(newVal).getTime() : null)
      }
    }
  },

  mounted () {
    if (this.openOnMount) {
      this.$nextTick(() => {
        this.open()
      })
    }
  },

  methods: {
    clear () {
      this.$refs.editor.clearValue()
    },

    open () {
      this.$refs.editor.openMenu()
    },

    close () {
      this.$refs.editor.closeMenu()
    }
  }
}
</script>
