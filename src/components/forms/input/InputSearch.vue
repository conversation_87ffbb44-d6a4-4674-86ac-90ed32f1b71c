<template>
  <div class="mt-1 relative rounded-md shadow-xs">
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <Search class="size-4 text-gray-400" aria-hidden="true"/>
    </div>
    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
      <X
          v-if="modelValue"
          class="size-4 text-gray-400 hover:cursor-pointer"
          aria-hidden="true"
          @click="clear"
      />
    </div>
    <input
        ref="search"
        type="search"
        name="search"
        autocomplete="off"
        class="focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full px-8 sm:text-sm border-gray-300 rounded-md"
        :placeholder="placeholder"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
    />
  </div>
</template>

<script>
import { Search, X } from 'lucide-vue-next'

export default {
  props: {
    placeholder: String,
    modelValue: String
  },
  components: {
    Search,
    X
  },
  methods: {
    clear () {
      this.$emit('update:modelValue', '')
      this.$refs.search.focus()
    },
    focus () {
      this.$refs.search.focus()
    }
  }
}
</script>

<style scoped>

</style>
