<template>
  <div class="mt-1 relative rounded-md shadow-xs">
    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
      <X
          v-if="modelValue"
          class="h-5 w-5 text-gray-400 hover:cursor-pointer"
          aria-hidden="true"
          @click="localValue = ''"
      />
    </div>
    <input
        type="text"
        v-model="localValue"
        :placeholder="placeholder"
        class="text-sm placeholder-zinc-300 focus:ring-fuchsia-500 focus:border-fuchsia-500 block w-full pr-10 border-gray-300 rounded-md"
        @focus="handleFocus"
        ref="input"
    />
  </div>
</template>

<script>
import { X } from 'lucide-vue-next'

export default {
  name: 'PlaceholderInput',
  components: { X },
  props: {
    modelValue: String,
    placeholder: String
  },
  emits: ['update:modelValue'],
  data () {
    return {
      localValue: ''
    }
  },
  watch: {
    modelValue: {
      immediate: true,
      handler (newValue) {
        this.localValue = newValue || ''
      }
    },
    localValue (newValue) {
      this.$emit('update:modelValue', newValue)
    }
  },
  methods: {
    handleFocus () {
      if (this.localValue.trim() === '') {
        this.localValue = this.placeholder
        this.$nextTick(() => {
          const input = this.$refs.input
          input.select()
        })
      } else {
        this.$nextTick(() => {
          const input = this.$refs.input
          input.select()
        })
      }
    }
  }
}
</script>
