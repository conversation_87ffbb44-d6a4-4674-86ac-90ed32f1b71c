<template>
  <combobox as="div" v-model="value" class="relative">
    <combobox-input
        class="w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-12 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-fuchsia-600 sm:text-sm sm:leading-6"
        @change="query = $event.target.value"/>
    <combobox-button class="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-hidden">
      <ChevronsUpDown class="h-5 w-5 text-gray-400" aria-hidden="true"/>
    </combobox-button>

    <combobox-options
        v-if="filtered.length > 0"
        class="absolute z-10 mt-1 max-h-32 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden sm:text-sm">
      <combobox-option
          v-for="c in filtered" :key="c.locale" :value="c.locale" as="template"
          v-slot="{ active, selected }">
        <li
            :class="['relative cursor-default select-none py-2 pl-3 pr-9', active ? 'bg-fuchsia-600 text-white' : 'text-gray-900']">
          <div class="flex">
              <span :class="['truncate', selected && 'font-semibold']">
                {{ c.name }}
              </span>
            <span :class="['ml-2 truncate text-gray-500', active ? 'text-fuchsia-200' : 'text-gray-500']">
                {{ c.currency }}
              </span>
          </div>
          <span
              v-if="selected"
              :class="['absolute inset-y-0 right-0 flex items-center pr-4', active ? 'text-white' : 'text-fuchsia-600']">
            <check class="h-5 w-5" aria-hidden="true"/>
          </span>
        </li>
      </combobox-option>
    </combobox-options>
  </combobox>
</template>

<script>
import { Combobox, ComboboxButton, ComboboxInput, ComboboxOption, ComboboxOptions } from '@headlessui/vue'
import { Check, ChevronsUpDown } from 'lucide-vue-next'

import { currencies } from '@/utils/currencies'

export default {
  name: 'ComboCurrency',
  components: {
    ComboboxOption,
    Combobox,
    ComboboxOptions,
    ComboboxButton,
    ComboboxInput,
    ChevronsUpDown,
    Check
  },
  emits: ['update:modelValue'],
  props: ['modelValue'],
  data () {
    return {
      query: '',
      currencies
    }
  },
  computed: {
    value: {
      get () {
        return this.modelValue
      },
      set (val) {
        this.$emit('update:modelValue', val)
      }
    },

    filtered () {
      return this.currencies.filter((curr) => {
        const q = this.query.toLowerCase()
        return (
            curr.locale.toLowerCase().includes(q) ||
            curr.currency.toLowerCase().includes(q) ||
            curr.name.toLowerCase().includes(q)
        )
      })
    }
  }
}
</script>

<style scoped>

</style>
