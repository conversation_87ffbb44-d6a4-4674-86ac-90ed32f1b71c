<template>
  <switch-group as="div" class="flex items-center sm:pt-1.5">
    <switch-label v-if="leftLabel" as="span" class="mr-3 text-sm cursor-pointer transition-colors"
                  :class="[value ? 'text-gray-300' : 'text-gray-900']" @click="value = !value">
      {{ leftLabel }}
    </switch-label>
    <headless-switch v-model="value"
                     :class="[value ? 'bg-fuchsia-600' : 'bg-gray-200', 'relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-fuchsia-600 focus:ring-offset-2']">
      <span aria-hidden="true"
            :class="[value ? 'translate-x-5' : 'translate-x-0', 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out']"/>
    </headless-switch>
    <switch-label as="span" class="ml-3 text-sm cursor-pointer transition-colors"
                  :class="[value ? 'text-gray-900' : (leftLabel ? 'text-gray-300' : 'text-gray-500')]">
      <slot>{{ label }}</slot>
    </switch-label>
  </switch-group>
</template>

<script>
import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue'

export default {
  name: 'BaseToggle',
  components: { SwitchLabel, SwitchGroup, HeadlessSwitch: Switch },
  emits: ['update:modelValue'],
  props: {
    modelValue: Boolean,
    label: {
      type: String,
      required: false
    },
    leftLabel: {
      type: String,
      required: false
    }
  },
  computed: {
    value: {
      get () {
        return this.modelValue
      },
      set (value) {
        this.$emit('update:modelValue', value)
      }
    }
  }
}
</script>

<style scoped>

</style>
