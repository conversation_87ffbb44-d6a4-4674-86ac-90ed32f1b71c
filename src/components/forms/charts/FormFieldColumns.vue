<template>
  <div v-for="id in columnIds" :key="id" class="mr-2">
    <div class="relative flex items-start truncate">
      <label class="font-medium text-sm text-gray-700">
        <span class="inline-block m-1"><slot :key="id"></slot></span>
        <slot name="label" :text="tableStoreGetColumn(id).header">
          <span class="ml-0.5 inline-block">{{ tableStoreGetColumnAutoHeader(id) }}</span>
        </slot>
      </label>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'pinia'
import { useTableStore } from '@/stores/table.js'
import { getExcelColumnName } from '@/utils/helpers.js'

export default {
  name: 'FormFieldColumns',
  props: {
    columnIds: {
      type: Array,
      required: true
    }
  },
  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetColumnAutoHeader: 'getColumnAutoHeader',
      tableStoreGetColumn: 'getColumn'
    }),
    getExcelColumnName
  }
}
</script>
