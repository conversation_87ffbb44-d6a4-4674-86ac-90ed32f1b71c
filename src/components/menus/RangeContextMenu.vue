<template>
  <context-menu ref="base">
    <menu-item-group>
      <create-chart-sub-menu
          :series="cells.cols[1] - cells.cols[0] + 1"
          label="Chart by column"
          @create-chart="createChart($event, false)"/>

      <create-chart-sub-menu
          :series="cells.rows[1] - cells.rows[0] + 1"
          label="Chart by row"
          @create-chart="createChart($event, true)"/>

    </menu-item-group>
  </context-menu>
</template>


<script>
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import ContextSubMenu from '@/components/ui/menu/ContextSubMenu.vue'
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import { useTableStore } from '@/stores/table.js'
import { range } from '@/utils/helpers.js'
import CreateChartSubMenu from '@/components/menus/CreateChartSubMenu.vue'
import { mapActions, mapState } from 'pinia'

export default {
  emits: ['insert', 'delete', 'create-chart'],
  components: { CreateChartSubMenu, ContextMenu, ContextSubMenu, MenuItemButton, MenuItemGroup },

  data () {
    return {
      cells: null,
      viewId: undefined
    }
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreDbTable: 'dbTable'
    }),

    querySpec () {
      return this.tableStoreGetQuerySpec(this.viewId)
    }
  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetViewColumns: 'getViewColumns',
      tableStoreGetQuerySpec: 'getQuerySpec'
    }),

    showMenu (e, cells, viewId) {
      this.cells = cells
      this.viewId = viewId
      this.$refs.base.showMenu(e)
    },

    async createChart (chartType, byRow) {
      // For all chart types, set values to all columns, skipping the label column id
      const viewCols = this.tableStoreGetViewColumns(this.viewId)

      // Map columns to an array of column ids, using the viewCols
      const colIds = range(...this.cells.cols).map(col => viewCols[col].id)

      // Map the range to an array of row ids; in a view, that's the same as the index
      const rowIndexes = range(...this.cells.rows)
      const rowIds = await this.tableStoreDbTable.rowIds(rowIndexes, this.querySpec)

      if (byRow) {
        this.$emit('create-chart', chartType, rowIds, colIds, true)

      } else {
        if (chartType === 'pie') {
          this.$emit('create-chart', 'pie', colIds, rowIds)

        } else {
          this.$emit('create-chart', chartType, colIds, undefined, false, {
            from: this.cells.rows[0],
            to: this.cells.rows[1]
          })
        }

      }

    }

  }
}
</script>

<style scoped>

</style>
