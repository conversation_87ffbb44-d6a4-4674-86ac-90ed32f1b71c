<template>
  <form-menu ref="base" @close="close">
    <form @submit.prevent="submit">
      <!-- dark header with text "'Filters for ' + column.header" -->
      <div class="bg-zinc-800 text-white p-2 rounded-t-md text-xs uppercase flex items-center">
        <span class="flex-1">{{ columnType.label }} Filters</span>
        <component :is="columnTypeIcon" class="size-4 text-zinc-400"/>
      </div>

      <div class="p-2">
        <component
            :is="filterComponent"
            :column="column"
            v-model="filter"
            ref="filterComponent"
            @update:modelValue="setFilter($event)"
        />
      </div>

      <div class="bg-zinc-100 border-t border-zinc-200 p-2 flex mt-2">
        <div class="flex-1">
          <base-btn is-text @click="clear">Clear all</base-btn>
        </div>
        <div>
          <base-btn is-submit type="primary">OK</base-btn>
        </div>
      </div>


    </form>

  </form-menu>
</template>

<script>
import { columnTypes, isNumericType } from '@/utils/formats.js'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { getColumnIcon } from '@/utils/colTypeComponents.js'
import FormMenu from '@/components/ui/menu/FormMenu.vue'
import BooleanFilter from '@/components/menus/filters/BooleanFilter.vue'
import TextFilter from '@/components/menus/filters/TextFilter.vue'
import NumericFilter from '@/components/menus/filters/NumericFilter.vue'
import DateTimeFilter from '@/components/menus/filters/DateTimeFilter.vue'
import equal from 'fast-deep-equal'

export default {
  name: 'FilterContextMenu',
  emits: ['set-filters', 'save-filters'],
  components: {
    FormMenu, FilterMenu: FormMenu, BaseBtn,
    BooleanFilter, TextFilter, NumericFilter, DateTimeFilter
  },

  data () {
    return {
      column: null,
      filter: null,
      initialFilter: null
    }
  },

  computed: {
    columnType () {
      return columnTypes[this.column.type]
    },

    columnTypeIcon () {
      return getColumnIcon(this.column.type)
    },

    filterComponent () {
      if (this.column.type === 'boolean') {
        return BooleanFilter
      } else if (this.column.type === 'text') {
        return TextFilter
      } else if (this.isNumericType(this.column.type)) {
        return NumericFilter
      } else if (this.column.type === 'datetime') {
        return DateTimeFilter
      }
      console.warn('No filter component for type', this.column.type)
      return null
    }
  },

  methods: {
    isNumericType,

    submit () {
      this.$refs.base.closeMenu()
    },

    showMenu (e, column) {
      this.column = column
      if (column.filters) {
        this.filter = column.filters[0] ?? null
      } else {
        this.filter = null
      }

      this.initialFilter = JSON.parse(JSON.stringify(this.filter))

      this.$refs.base.showMenu(e)
      this.$nextTick(() => {
        this.$refs.filterComponent?.focus()
      })
    },

    setFilter (filter) {
      if (filter === null) {
        this.$emit('set-filters', this.column.id, [])
      } else {
        this.$emit('set-filters', this.column.id, [filter])
      }
    },

    clear () {
      this.$emit('set-filters', this.column.id, [])
      if (this.initialFilter !== null) {
        this.$emit('save-filters', this.column.id)
      }
      this.$refs.base.closeMenu()

    },

    close () {
      // On close, save the filters if they've changed
      if (equal(this.filter, this.initialFilter)) return
      this.$emit('save-filters', this.column.id)
    }
  }
}
</script>

<style scoped>

</style>
