<template>
  <context-sub-menu :label="label">
    <menu-item-group>
      <menu-item-button @click="$emit('create-chart','line')">Line</menu-item-button>
      <menu-item-button @click="$emit('create-chart', 'bar')">Bar</menu-item-button>
      <menu-item-button
          v-if="series === 1"
          @click="$emit('create-chart', 'pie')">Pie
      </menu-item-button>
      <menu-item-button
          v-else-if="series === 2"
          @click="$emit('create-chart', 'scatter')">Scatter
      </menu-item-button>
    </menu-item-group>
  </context-sub-menu>
</template>

<script>
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import ContextSubMenu from '@/components/ui/menu/ContextSubMenu.vue'

export default {
  name: 'CreateChartSubMenu',
  components: { ContextSubMenu, MenuItemButton, MenuItemGroup },
  emits: ['create-chart'],
  props: {
    label: {
      type: String,
      required: true
    },
    series: {
      type: Number,
      required: true
    }
  }
}

</script>

<style scoped>

</style>
