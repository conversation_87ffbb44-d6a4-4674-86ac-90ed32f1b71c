<template>
  <context-menu ref="base">
    <menu-item-group :title="title">
      <menu-item-button
          v-for="agg in columnAggregates"
          :key="agg.key"
          :selected="agg.key === column.aggregate"
          @click="setAggregate(agg.key)">{{ agg.label }}
      </menu-item-button>
    </menu-item-group>
    <menu-item-group>
      <menu-item-button @click="$emit('more', this.column)">More...</menu-item-button>
      <menu-item-button
          :key="''"
          :selected="!column.aggregate"
          @click="setAggregate(undefined)">Disable aggregation
      </menu-item-button>
    </menu-item-group>
  </context-menu>
</template>

<script>
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import { getAggregateLabelsForColType } from '@/utils/aggregation.js'

export default {
  emits: ['set-aggregate', 'more'],
  components: { MenuItemButton, MenuItemGroup, ContextMenu },
  data () {
    return {
      column: null
    }
  },

  computed: {
    title () {
      return this.column.header + ' Aggregates'
    },

    columnAggregates () {
      return getAggregateLabelsForColType(this.column.type, true)
    }
  },

  methods: {
    setAggregate (key) {
      this.$emit('set-aggregate', this.column.id, key)
    },

    showMenu (e, column) {
      this.column = column
      this.$refs.base.showMenu(e)
    }
  }
}
</script>


<style scoped>

</style>
