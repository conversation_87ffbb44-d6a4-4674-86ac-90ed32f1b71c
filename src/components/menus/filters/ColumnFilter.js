export default {
  
  emits: ['update:modelValue'],
  
  props: {
    modelValue: {
      type: Object,
      default: null
    },
    column: {
      type: Object,
      required: true
    },
    inline: {
      type: Boolean,
      default: false
    }
  },
  
  data () {
    return {
      operator: 'equals',
      value: null
    }
  },
  
  mounted () {
    this.initialize()
    this.emitUpdate()
  },
  
  computed: {
    inputType () {
      return this.operators.find(op => op.value === this.operator)?.inputType ?? 'single'
    }
  },
  
  methods: {
    focus () {
      // By default, we wait one tick and focus on the first 'input' in the filter
      this.$nextTick(() => {
        this.$el.querySelector('input')?.focus()
      })
    }
    
  }
  
}
