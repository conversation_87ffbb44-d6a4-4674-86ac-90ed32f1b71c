<template>
  <select
      v-model="operator"
      :class="[
        'block shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md mt-1',
        inline ? 'w-full' : 'w-full mb-2'
      ]">
    <option v-for="op in operators" :key="op.value" :value="op.value">
      {{ op.label }}
    </option>
  </select>
</template>


<script>
export default {
  name: 'FilterOperator',
  props: {
    operators: Array,
    modelValue: String,
    inline: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue'],
  computed: {
    operator: {
      get () {
        return this.modelValue
      },
      set (val) {
        this.$emit('update:modelValue', val)
      }
    }
  }
}
</script>

<style scoped>

</style>
