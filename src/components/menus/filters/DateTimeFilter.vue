<template>
  <div :class="{ 'flex items-center': inline }">
    <filter-operator
        v-model="operator"
        :operators="operators"
        :inline="inline"
        :class="inline ? 'w-1/3 mr-2' : ''"
    />
    <input-date-time
        v-if="inputType !== 'none'"
        ref="firstFilter"
        v-model="value"
        :format="format"
        :auto-apply="true"
        :close-on-auto-apply="true"
        :range="inputType === 'double'"
        :class="inline ? 'w-2/3' : ''"
        :input-classes="'shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md! text-left!'"
    />
  </div>
</template>

<script>
import InputDateTime from '@/components/forms/input/DateTimeInput.vue'
import equal from 'fast-deep-equal'
import { columnTypes } from '@/utils/formats.js'
import ColumnFilter from '@/components/menus/filters/ColumnFilter.js'
import FilterOperator from '@/components/menus/filters/FilterOperator.vue'

export default {
  name: 'DateTimeFilter',
  extends: ColumnFilter,
  components: {
    FilterOperator,
    InputDateTime
  },

  data () {
    return {
      // Extend the base component
      ...ColumnFilter.data(),

      format: this.column.props.format,
      baseInputStyles: 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-fuchsia-500 focus:border-fuchsia-500 sm:text-sm',
      operators: [
        { value: 'equals', label: 'Equals', inputType: 'single' },
        { value: 'not_equals', label: 'Does not equal', inputType: 'single' },
        { value: 'greater_than', label: 'After', inputType: 'single' },
        { value: 'greater_than_or_equals', label: 'After or on', inputType: 'single' },
        { value: 'less_than', label: 'Before', inputType: 'single' },
        { value: 'less_than_or_equals', label: 'Before or on', inputType: 'single' },
        { value: 'between', label: 'Between', inputType: 'double' },
        { value: 'not_between', label: 'Not between', inputType: 'double' },
        { value: 'blank', label: 'Is blank', inputType: 'none' },
        { value: 'not_blank', label: 'Is not blank', inputType: 'none' },
        { value: 'error', label: 'Has error', inputType: 'none' },
        { value: 'not_error', label: 'Has no error', inputType: 'none' }
      ]
    }
  },

  watch: {
    operator () {
      // Change the value type if switching between single and double.
      // This will trigger the watcher again and do emit update.
      const inputType = this.operators.find(op => op.value === this.operator).inputType

      if (inputType === 'single' && Array.isArray(this.value)) {
        this.value = this.value[0]

      } else if (inputType === 'double' && !Array.isArray(this.value)) {
        this.value = []

      } else if (inputType === 'none' && this.value) {
        this.value = null

      } else {
        this.emitUpdate()
      }
    },
    value (newValue, oldValue) {
      if (equal(newValue, oldValue)) return
      this.emitUpdate()
    }
  },

  methods: {
    initialize () {
      const filter = this.modelValue
      if (!filter) return
      this.operator = filter.operator || 'equals'

      if (this.inputType === 'single' && filter.value) {
        // Convert timestamp to ISO string for the datepicker
        const date = columnTypes.datetime.toDate(filter.value)
        this.value = date ? date.getTime() : null

      } else if (this.inputType === 'double') {
        // For range values, convert both timestamps to ISO strings
        this.value = filter.value.map(v => columnTypes.datetime.toDate(v).getTime())

      } else {
        // Reset values for 'none' input type or when switching operators
        this.value = null
      }
    },

    emitUpdate () {
      let filter = null
      if (this.inputType === 'none') {
        filter = { operator: this.operator }

      } else if (this.inputType === 'single' && this.value) {
        filter = {
          operator: this.operator,
          value: this.value
        }

      } else if (this.inputType === 'double' && Array.isArray(this.value) && this.value.filter(v => v).length === 2) {
        filter = {
          operator: this.operator,
          value: this.value
        }
      }
      this.$emit('update:modelValue', filter)
    },

    focus () {
      // focusing wipes the 'between' range value for some reason so we disable
    }
  }
}
</script>
