<template>
  <div :class="{ 'flex items-center': inline }">
    <filter-operator
        v-model="operator"
        :operators="operators"
        :inline="inline"
        :class="inline ? 'w-1/3 mr-2' : ''"
    />

    <div v-if="inputType === 'single'" :class="inline ? 'w-2/3' : 'space-y-2'">
      <radio-group v-model="value" as="div" class="flex flex-row w-full">
        <radio-group-option
            v-slot="{ checked, active }"
            v-for="option in options"
            :key="option.value"
            :value="option.value"
            as="template">
          <div
              :class="[
                active? 'ring-2 ring-white/60 ring-offset-2 ring-offset-fuchsia-300' : '',
                checked ? 'bg-fuchsia-600 text-white' : 'bg-white '
               ]"
              class="w-1/2 relative items-center flex mx-1 text-sm cursor-pointer rounded-lg p-2 shadow-md focus:outline-none">
            <component
                :is="option.component"
                class="size-5 mr-3"
                :class="[checked ? 'text-' + option.color + '-200' : 'text-' + option.color + '-400']"
            />
            <span>{{ option.label }}</span>
          </div>
        </radio-group-option>
      </radio-group>
    </div>
  </div>
</template>

<script>
import { CheckCircle, XCircle } from 'lucide-vue-next'
import ColumnFilter from '@/components/menus/filters/ColumnFilter.js'
import FilterOperator from '@/components/menus/filters/FilterOperator.vue'
import { RadioGroup, RadioGroupOption } from '@headlessui/vue'

export default {
  name: 'BooleanFilter',
  extends: ColumnFilter,
  components: {
    RadioGroupOption,
    RadioGroup,
    FilterOperator,
    CheckCircle, XCircle
  },

  data () {
    return {
      // Extend the base component
      ...ColumnFilter.data(),

      operators: [
        { value: 'equals', label: 'Equals', inputType: 'single' },
        { value: 'not_equals', label: 'Does not equal', inputType: 'single' },
        { value: 'blank', label: 'Is blank', inputType: 'none' },
        { value: 'not_blank', label: 'Is not blank', inputType: 'none' },
        { value: 'error', label: 'Has error', inputType: 'none' },
        { value: 'not_error', label: 'Has no error', inputType: 'none' }
      ],
      options: [
        {
          value: true,
          label: 'True',
          component: CheckCircle,
          color: 'green'
        },
        {
          value: false,
          label: 'False',
          component: XCircle,
          color: 'red'
        }
      ]
    }
  },

  watch: {
    operator: 'emitUpdate',
    value: 'emitUpdate'
  },

  methods: {
    initialize () {
      const filter = this.modelValue
      if (!filter) return
      this.operator = filter.operator || 'equals'
      if (this.inputType === 'single') {
        this.value = filter.value === true ? true :
                     filter.value === false ? false : null
      } else {
        this.value = null
      }
    },

    emitUpdate () {
      let filter = null
      if (this.inputType === 'none') {
        filter = { operator: this.operator }
      } else if (this.inputType === 'single' && this.value !== null) {
        filter = {
          operator: this.operator,
          value: this.value
        }
      }
      this.$emit('update:modelValue', filter)
    }
  }
}
</script>
