<template>
  <div :class="{ 'flex items-center': inline }">
    <filter-operator
        v-model="operator"
        :operators="operators"
        :inline="inline"
        :class="inline ? 'w-1/3 mr-2' : ''"
    />
    <input-search
        v-if="inputType === 'single'"
        ref="firstFilter"
        placeholder="Filter"
        v-model="value"
        :class="inline ? 'w-2/3' : ''"
    />
  </div>
</template>

<script>
import InputSearch from '@/components/forms/input/InputSearch.vue'
import ColumnFilter from '@/components/menus/filters/ColumnFilter.js'
import FilterOperator from '@/components/menus/filters/FilterOperator.vue'

export default {
  name: 'TextFilter',
  extends: ColumnFilter,
  components: {
    FilterOperator,
    InputSearch
  },

  data () {
    return {
      // Extend the base component
      ...ColumnFilter.data(),

      operators: [
        { value: 'contains', label: 'Contains', inputType: 'single' },
        { value: 'not_contains', label: 'Does not contain', inputType: 'single' },
        { value: 'begins_with', label: 'Begins with', inputType: 'single' },
        { value: 'ends_with', label: 'Ends with', inputType: 'single' },
        { value: 'equals', label: 'Equals', inputType: 'single' },
        { value: 'not_equals', label: 'Does not equal', inputType: 'single' },
        { value: 'blank', label: 'Is blank', inputType: 'none' },
        { value: 'not_blank', label: 'Is not blank', inputType: 'none' },
        { value: 'error', label: 'Has error', inputType: 'none' },
        { value: 'not_error', label: 'Has no error', inputType: 'none' }
      ]
    }
  },

  watch: {
    operator: 'emitUpdate',
    value: 'emitUpdate'
  },

  methods: {
    initialize () {
      const filter = this.modelValue
      if (!filter) return
      this.operator = filter.operator || 'contains'
      if (this.inputType === 'single') {
        this.value = filter.value || null
      } else {
        this.value = null
      }
    },

    emitUpdate () {
      let filter = null
      if (this.inputType === 'none') {
        filter = { operator: this.operator }

      } else if (this.inputType === 'single' && this.value?.trim()) {
        filter = {
          operator: this.operator,
          value: this.value
        }
      }
      this.$emit('update:modelValue', filter)
    }
  }
}
</script>
