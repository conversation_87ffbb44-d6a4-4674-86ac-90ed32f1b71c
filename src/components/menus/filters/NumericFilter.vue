<template>
  <div :class="{ 'flex items-center': inline }">
    <filter-operator
        v-model="operator"
        :operators="operators"
        :inline="inline"
        :class="inline ? 'w-1/3 mr-2' : ''"
    />

    <!-- Single input with validation -->
    <number-input
        v-if="inputType === 'single'"
        :type="column.type"
        v-model:input="valueInput"
        v-model:parsed="value"
        placeholder="Enter a number"
        :class="inline ? 'w-2/3' : ''"
    />

    <!-- Double input with validation -->
    <div v-if="inputType === 'double'" :class="inline ? 'flex space-x-2 w-2/3' : 'space-y-2'">
      <number-input
          :type="column.type"
          v-model:input="valueStartInput"
          v-model:parsed="valueStart"
          placeholder="From"
          :class="inline ? 'w-1/2' : ''"
      />
      <number-input
          :type="column.type"
          v-model:input="valueEndInput"
          v-model:parsed="valueEnd"
          placeholder="To"
          :class="inline ? 'w-1/2' : ''"
      />
    </div>
  </div>
</template>

<script>
import NumberInput from '@/components/forms/input/NumberInput.vue'
import ColumnFilter from '@/components/menus/filters/ColumnFilter.js'
import FilterOperator from '@/components/menus/filters/FilterOperator.vue'
import { columnTypes } from '@/utils/formats.js'

export default {
  name: 'NumericFilter',
  extends: ColumnFilter,

  components: {
    FilterOperator,
    NumberInput
  },

  data () {
    return {
      // Extend the base component
      ...ColumnFilter.data(),

      // Numeric filter values (parsed numbers)
      valueStart: null,
      valueEnd: null,

      // Input string values for numeric filters
      valueInput: '',
      valueStartInput: '',
      valueEndInput: '',

      operators: [
        { value: 'equals', label: 'Equals', inputType: 'single' },
        { value: 'not_equals', label: 'Does not equal', inputType: 'single' },
        { value: 'greater_than', label: 'Greater than', inputType: 'single' },
        { value: 'greater_than_or_equals', label: 'Greater than or equals', inputType: 'single' },
        { value: 'less_than', label: 'Less than', inputType: 'single' },
        { value: 'less_than_or_equals', label: 'Less than or equals', inputType: 'single' },
        { value: 'between', label: 'Between', inputType: 'double' },
        { value: 'not_between', label: 'Not between', inputType: 'double' },
        { value: 'blank', label: 'Is blank', inputType: 'none' },
        { value: 'not_blank', label: 'Is not blank', inputType: 'none' },
        { value: 'error', label: 'Has error', inputType: 'none' },
        { value: 'not_error', label: 'Has no error', inputType: 'none' }
      ]
    }
  },

  watch: {
    operator: 'emitUpdate',
    value: 'emitUpdate',
    valueStart: 'emitUpdate',
    valueEnd: 'emitUpdate'
  },

  methods: {

    prepNumberIn (val) {
      // Incoming numbers we should x100 for percentages
      val = columnTypes[this.column.type].convert(val, this.column.props)
      if (val === null) return ''
      if (this.column.type === 'percent') return String(val.times(100))
      return String(val)
    },

    prepNumberOut (val) {
      // Going back out, /100 for percentages
      if (this.column.type === 'percent') return String(val.div(100))
      return String(val)
    },

    initialize () {
      const filter = this.modelValue
      if (!filter) return
      this.operator = filter.operator || 'equals'
      if (this.inputType === 'single' && filter.value) {
        this.valueInput = this.prepNumberIn(filter.value)

      } else if (this.inputType === 'double' && Array.isArray(filter.value)) {
        this.valueStartInput = this.prepNumberIn(filter.value[0])
        this.valueEndInput = this.prepNumberIn(filter.value[1])

      } else {
        // Reset values for 'none' input type or when switching operators
        this.valueInput = ''
        this.valueStartInput = ''
        this.valueEndInput = ''
        this.value = null
        this.valueStart = null
        this.valueEnd = null
      }
    },

    emitUpdate () {
      let filter = null
      if (this.inputType === 'none') {
        filter = { operator: this.operator }

      } else if (this.inputType === 'single' && this.value !== null) {
        filter = {
          operator: this.operator,
          value: this.prepNumberOut(this.value)
        }

      } else if (this.inputType === 'double' &&
          this.valueStart !== null &&
          this.valueEnd !== null) {
        filter = {
          operator: this.operator,
          value: [this.prepNumberOut(this.valueStart), this.prepNumberOut(this.valueEnd)]
        }
      }
      this.$emit('update:modelValue', filter)
    }
  }
}
</script>
