<template>
  <context-menu ref="base">
    <menu-item-group v-if="!viewId">
      <menu-item-button @click="insertRows(true)">{{ getInsertLabel(true) }}</menu-item-button>
      <menu-item-button @click="insertRows(false)">{{ getInsertLabel() }}</menu-item-button>
      <menu-item-button @click="$emit('delete')">{{ getDeleteLabel() }}</menu-item-button>
    </menu-item-group>
    <menu-item-group>
      <create-chart-sub-menu :series="rowIds.length" label="Create chart" @create-chart="createChart"/>
    </menu-item-group>
  </context-menu>
</template>


<script>
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import ContextSubMenu from '@/components/ui/menu/ContextSubMenu.vue'
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import { useTableStore } from '@/stores/table.js'
import CreateChartSubMenu from '@/components/menus/CreateChartSubMenu.vue'

export default {
  emits: ['insert', 'delete', 'create-chart'],
  components: { CreateChartSubMenu, ContextMenu, ContextSubMenu, MenuItemButton, MenuItemGroup },

  data () {
    return {
      rowIds: null,
      viewId: null
    }
  },

  methods: {
    showMenu (e, rowIds, viewId) {
      this.rowIds = rowIds
      this.viewId = viewId
      this.$refs.base.showMenu(e)
    },

    insertRows (above) {
      this.$emit('insert', above, this.rowIds.length)
    },

    getDeleteLabel () {
      if (this.rowIds.length === 1) {
        return 'Delete row'
      } else {
        return `Delete ${this.rowIds.length} rows`
      }
    },

    getInsertLabel (above) {
      const aboveOrBelow = above ? 'above' : 'below'
      if (this.rowIds.length === 1) {
        return `Insert row ${aboveOrBelow}`
      } else {
        return `Insert ${this.rowIds.length} rows ${aboveOrBelow}`
      }
    },

    async createChart (chartType) {
      // For all chart types, set values to all columns, skipping the label column id
      const values = useTableStore().getViewColumns(this.viewId).map(col => col.id)
      this.$emit('create-chart', chartType, this.rowIds, values, true)
    }

  }
}
</script>

<style scoped>

</style>
