<template>
  <context-menu ref="base">
    <template v-if="colId !== undefined">
      <template v-if="viewId">
        <menu-item-group v-if="columnIds.length === 1">
          <menu-item-button @click="$emit('edit', colId)">Edit column...</menu-item-button>
        </menu-item-group>
        <menu-item-group>
          <menu-item-button
              v-for="sortOpt in sortOptions"
              :key="sortOpt.label"
              :icon="sortOpt.icon"
              :selected="sortOpt.selected"
              @click="$emit('change-sort', { colId, desc: sortOpt.desc })">
            {{ sortOpt.label }}
          </menu-item-button>
        </menu-item-group>
        <menu-item-group>
          <create-chart-sub-menu :series="columnIds.length" label="Create chart" @create-chart="createChart"/>
        </menu-item-group>

      </template>

      <template v-else>
        <menu-item-group v-if="columnIds.length === 1">
          <menu-item-button @click="$emit('edit', colId)">Edit column...</menu-item-button>
          <menu-item-button v-if="column?.calc" @click="$emit('edit-calc', colId)">Change calculation...
          </menu-item-button>
          <menu-item-button @click="toggleLabel" :selected="column?.isLabel">
            Set as label column
          </menu-item-button>
          <context-sub-menu label="Pin column">
            <menu-item-group>
              <menu-item-button
                  v-for="pinOpt in pinColumnMenu"
                  :key="pinOpt.type"
                  :selected="column?.pin === pinOpt.type"
                  @click="$emit('pin', colId, pinOpt.type)">
                {{ pinOpt.label }}
              </menu-item-button>
            </menu-item-group>
          </context-sub-menu>
          <context-sub-menu label="Change column type">
            <menu-item-group v-for="(submenu, index) in columnTypeMenu" :key="index">
              <!--suppress JSValidateTypes -->
              <menu-item-button
                  v-for="type in submenu"
                  :key="type.type"
                  :icon="getColumnIcon(type.type)"
                  :selected="column?.type === type.type"
                  @click="$emit('change-type', colId, type.type)">
                {{ type.label }}
              </menu-item-button>
            </menu-item-group>
          </context-sub-menu>
        </menu-item-group>
        <menu-item-group>
          <create-chart-sub-menu :series="columnIds.length" label="Create chart" @create-chart="createChart"/>
        </menu-item-group>
        <menu-item-group>
          <context-sub-menu
              :label="'Insert ' + (columnIds.length === 1 ? 'column ' : columnIds.length + ' columns') + ' to the left'">
            <menu-item-group v-for="(submenu, index) in columnTypeMenu" :key="index">
              <!--suppress JSValidateTypes -->
              <menu-item-button
                  v-for="type in submenu"
                  :key="type.type"
                  :icon="getColumnIcon(type.type)"
                  @click="$emit('insert', false, type.type)">
                {{ type.label }}
              </menu-item-button>
            </menu-item-group>
          </context-sub-menu>
          <context-sub-menu
              :label="'Insert ' + (columnIds.length === 1 ? 'column ' : columnIds.length + ' columns') + ' to the right'">
            <menu-item-group v-for="(submenu, index) in columnTypeMenu" :key="index">
              <!--suppress JSValidateTypes -->
              <menu-item-button
                  v-for="type in submenu"
                  :key="type.type"
                  :icon="getColumnIcon(type.type)"
                  @click="$emit('insert', true, type.type)">
                {{ type.label }}
              </menu-item-button>
            </menu-item-group>
          </context-sub-menu>
          <menu-item-button @click="$emit('edit-calc')">Insert calculated column...</menu-item-button>
          <context-sub-menu
              v-if="canCalculateOnColumns"
              :label="columnIds.length > 1 ? 'Calculate on these columns' : 'Calculate on this column'">
            <menu-item-group>
              <template :key="calc.type"
                        v-for="calc in columnIds.length > 1 ? multiSourceCalcs : oneSourceCalcs">
                <context-sub-menu v-if="calc.presets" :label="calc.label">
                  <menu-item-group>
                    <menu-item-button
                        v-for="preset in calc.presets"
                        :key="preset.key"
                        @click="$emit('insert-calc', calc, columnIds, preset.data)">
                      <div class="flex flex-col">
                        <div>{{ preset.label }}</div>
                        <div class="text-xs" v-if="preset.help">{{ preset.help }}</div>
                      </div>
                    </menu-item-button>
                    <menu-item-button @click="$emit('edit-calc', undefined, calc.type, columnIds)">Custom...
                    </menu-item-button>
                  </menu-item-group>
                </context-sub-menu>
                <menu-item-button
                    v-else-if="calc.props"
                    @click="$emit('edit-calc', undefined, calc.type, columnIds)">{{ calc.label }}...
                </menu-item-button>
                <menu-item-button v-else @click="$emit('insert-calc', calc, columnIds)">{{
                    calc.label
                  }}
                </menu-item-button>
              </template>
            </menu-item-group>
          </context-sub-menu>
        </menu-item-group>
        <menu-item-group>
          <menu-item-button @click="$emit('delete', columnIds)">
            {{ columnIds.length === 1 ? 'Delete this column' : `Delete ${columnIds.length} columns` }}
          </menu-item-button>
        </menu-item-group>
      </template>
    </template>
  </context-menu>
</template>

<script>
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import ContextSubMenu from '@/components/ui/menu/ContextSubMenu.vue'
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import { mapActions, mapState } from 'pinia'
import { columnTypes } from '@/utils/formats.js'
import { getColumnIcon } from '@/utils/colTypeComponents.js'
import { useTableStore } from '@/stores/table.js'
import { cantCalcOnTypes, multiSourceCalcs, oneSourceCalcs } from '@/utils/calcs.js'
import CreateChartSubMenu from '@/components/menus/CreateChartSubMenu.vue'
import { ArrowDownNarrowWide, ArrowUpNarrowWide } from 'lucide-vue-next'

export const pinColumnMenu = [
  { type: 'l', label: 'Left' },
  { type: 'r', label: 'Right' },
  { type: 'n', label: 'Unpin' }
]

const ct = columnTypes
export const columnTypeMenu = [
  [ct.text, ct.boolean],
  [ct.number, ct.percent, ct.currency],
  [ct.datetime]
]

export default {
  name: 'ColumnContextMenu',
  emits: ['edit', 'edit-calc', 'pin', 'change-type', 'create-chart', 'insert',
    'insert-calc', 'delete', 'change-sort'],
  components: {
    ArrowUpNarrowWide,
    ArrowDownNarrowWide,
    CreateChartSubMenu,
    ContextMenu,
    ContextSubMenu,
    MenuItemButton,
    MenuItemGroup
  },
  data () {
    return {
      columnIds: undefined,
      viewId: undefined,
      pinColumnMenu,
      columnTypeMenu,
      multiSourceCalcs,
      oneSourceCalcs
    }
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreDbTable: 'dbTable'
    }),

    colId () {
      return this.columnIds?.length ? this.columnIds[0] : undefined
    },

    columns () {
      return this.tableStoreGetViewColumns(this.viewId)
    },

    querySpec () {
      return this.tableStoreGetQuerySpec(this.viewId)
    },

    column () {
      return this.columns.find(col => col.id === this.colId)
    },

    canCalculateOnColumns () {
      return this.columnIds.every(colId => !cantCalcOnTypes.includes(this.columns.find(col => col.id === colId).type))
    },

    sortOptions () {
      const colType = this.column.type
      const colFormat = columnTypes[colType]
      const colSort = this.column.sort
      return [
        {
          label: `Sort ${colFormat.sortLabels.asc} (asc)`,
          desc: false,
          selected: colSort !== undefined && colSort.order === 0 && !colSort.desc,
          icon: ArrowUpNarrowWide
        },
        {
          label: `Sort ${colFormat.sortLabels.desc} (desc)`,
          desc: true,
          selected: colSort !== undefined && colSort.order === 0 && colSort.desc,
          icon: ArrowDownNarrowWide
        }
      ]
    }

  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreToggleLabelColumn: 'toggleLabelColumn',
      tableStoreGetViewColumns: 'getViewColumns',
      tableStoreGetQuerySpec: 'getQuerySpec'
    }),
    getColumnIcon,
    showMenu (e, columnIds, viewId) {
      this.columnIds = columnIds
      this.viewId = viewId
      this.$refs.base.showMenu(e)
    },

    toggleLabel () {
      this.tableStoreToggleLabelColumn(this.colId)
    },

    async createChart (type) {
      const series = this.columnIds.filter(col => col !== this.labelColumnId)
      const values = (type === 'pie') ? await this.tableStoreDbTable.rowIds(undefined, this.querySpec) : []
      this.$emit('create-chart', type, series, values)
    }
  }
}

</script>

<style scoped>

</style>
