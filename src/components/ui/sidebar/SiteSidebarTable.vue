<template>
  <li :key="table.id" class="flex items-center justify-between group/table">
    <router-link
        v-if="selected"
        class="block flex-1 border-l pl-4 -ml-px border-gray-500 text-gray-100"
        :to="{ name: 'app' }">
      <template v-if="table.title">{{ table.title }}</template>
      <template v-else><em>Untitled table</em></template>
    </router-link>
    <router-link
        v-else
        class="block border-l text-gray-400 pl-4 -ml-px border-transparent hover:border-gray-500 hover:text-gray-100"
        :to="{ name: 'app-table', params: {tableId: table.id }}">
      <template v-if="table.title">{{ table.title }}</template>
      <template v-else><em>Untitled table</em></template>
    </router-link>
    <div
        class="flex-none flex space-x-2 ml-1 transition text-transparent group-hover/table:text-gray-400 cursor-pointer">
      <div class="hover:group-hover/table:text-gray-100" title="Edit table" @click="$emit('edit-table', table)">
        <Pencil class="size-4"/>
      </div>
    </div>
  </li>
</template>
<script>
import { Pencil, Table2, UploadCloud } from 'lucide-vue-next'

export default {
  name: 'site-sidebar-table',
  emits: ['edit-table'],
  components: { UploadCloud, Table2, Pencil },
  props: {
    table: {},
    selected: {
      type: Boolean,
      default: false
    }
  }
}
</script>
