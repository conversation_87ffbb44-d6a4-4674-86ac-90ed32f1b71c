<template>
  <nav class="transition-all flex-1 p-2 overflow-y-auto">
    <ul>
      <site-sidebar-project
          class="p-2"
          @select-project="selectedProjectId = $event"
          @show-context-menu="$emit('show-context-menu', $event)"
          @edit-project="$emit('edit-project', $event)"
          @edit-table="$emit('edit-table', $event)"
          @add-table="$emit('add-table', $event)"
          @import-data="$emit('import', $event)"
          v-for="proj in filteredProjects"
          :key="proj.id"
          :current="selectedProjectId === proj.id"
          :project="proj"/>

      <site-sidebar-project
          class="p-2"
          v-if="orphanProject.tables.length > 0"
          :project="orphanProject"
          :orphan-project="true"
          @select-project="selectedProjectId = $event"
          @edit-table="$emit('edit-table', $event)"
          @add-table="$emit('add-table', null)"
          @import-data="$emit('import')"
          @show-context-menu="$emit('show-context-menu', $event)"
          :current="selectedProjectId === orphanProject.id"/>

    </ul>


  </nav>
</template>
<script>

import SiteSidebarProject from '@/components/ui/sidebar/SiteSidebarProject.vue'
import { mapState } from 'pinia'
import { useTableStore } from '@/stores/table.js'

export default {
  name: 'site-sidebar-project-list',
  emits: ['show-context-menu', 'import', 'edit-project', 'edit-table', 'add-table'],
  components: {
    SiteSidebarProject
  },
  props: {
    filteredProjects: {},
    orphanProject: {}
  },
  data () {
    return {
      selectedProjectId: undefined  // nb - null=orphaned, undefined=nothing selected
    }
  },
  watch: {
    tableId: 'updateSelectedProject',
    tableProjectId: 'updateSelectedProject'
  },
  computed: {
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreProjectId: 'project'
    })
  },
  methods: {
    updateSelectedProject () {
      if (this.tableStoreId && this.tableStoreProjectId) {
        this.selectedProjectId = this.tableStoreProjectId
      } else if (this.tableStoreId) {
        this.selectedProjectId = this.orphanProject.id
      }
    }
  }
}
</script>
<style scoped>

nav {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

nav:hover {
  scrollbar-color: inherit;
}

</style>
