<template>
  <transition-fixed-slide
      fix-on-transition
      v-model="prefsStoreSiteSidebarStatus"
      slide-from="left"
      class="shrink-0 bg-gray-900 w-64 flex">
    <div class="flex-1 flex flex-col min-h-0">
      <div class="flex-1 flex flex-col overflow-y-auto">
        <base-dropzone
            v-if="!appStoreModalIsOpen"
            @file-drop="e => $emit('file-drop', e)" absolute small
            label="Drop file here to create new table"
        />
        <div class="flex items-center shrink-0 px-4 py-2.5 border-b border-gray-600 bg-gray-800 text-gray-200">
          <router-link :to="{ name: 'app' }">
            <div class="flex items-center space-x-2">
              <brand-image/>
            </div>
          </router-link>
          <X
              class="h-5 w-5 text-gray-500 hover:text-gray-100 ml-auto cursor-pointer"
              @click="prefsStoreSiteSidebarStatus = 'closing'"/>
        </div>
        <div class="m-2">
          <div class="flex space-x-1">
            <v-menu as="div" class="w-full">
              <menu-button
                  class="w-full flex text-center rounded-md shadow-xs px-4 py-2 text-sm text-white bg-fuchsia-600 hover:bg-fuchsia-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-fuchsia-500 cursor-pointer">
                <div class="flex-1 block">Add new or import...</div>
                <div class="shrink-0">
                  <ChevronDown class="w-5 h-5 ml-2 -mr-1" aria-hidden="true"/>
                </div>
              </menu-button>
              <transition-dropdown>
                <menu-items
                    class="absolute z-10 w-72 mt-2 ml-2 origin-top-left bg-white divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black/5 focus:outline-hidden">
                  <menu-item-group>
                    <menu-item-button @click="$emit('add-project')">
                      <FolderPlus class="w-5 h-5 mr-2" aria-hidden="true"/>
                      Add new project...
                    </menu-item-button>
                    <menu-item-button @click="$emit('add-table')">
                      <Grid2x2Plus class="w-5 h-5 mr-2" aria-hidden="true"/>
                      Add blank table...
                    </menu-item-button>
                    <menu-item-button @click="$emit('import')">
                      <HardDriveUpload class="w-5 h-5 mr-2" aria-hidden="true"/>
                      Import data...
                    </menu-item-button>
                  </menu-item-group>
                </menu-items>
              </transition-dropdown>
            </v-menu>
          </div>
        </div>
        <div class="mb-2 mx-2">
          <input-search
              placeholder="Search your tables"
              v-model="searchText"
          />
        </div>
        <site-sidebar-project-list
            :filtered-projects="filteredProjects"
            :orphan-project="orphanProject"
            @import="$emit('import', $event)"
            @add-table="$emit('add-table', $event)"
            @edit-table="$emit('edit-table', $event)"
            @edit-project="$emit('edit-project', $event)"
            @show-context-menu="showContextMenu"/>
      </div>
      <the-user-profile/>
    </div>
  </transition-fixed-slide>
  <div v-if="prefsStoreSiteSidebarStatus !== 'opened'"
       @click="prefsStoreSiteSidebarStatus = 'opening'"
       class="bg-gray-900 z-20 w-6 cursor-pointer hover:scale-x-150 hover:bg-gray-800 transition-all shrink-0"></div>
  <teleport to="body">
    <context-menu ref="contextMenu">
      <template v-if="context.table">
        <menu-item-group :title="context.table.title">
          <menu-item-button @click="$emit('edit-table', context.table)">Edit table...</menu-item-button>
          <menu-item-button @click="$refs.deleteTableDialog.open()" class="text-red-600">Delete table...
          </menu-item-button>
        </menu-item-group>
      </template>

      <template v-else-if="context.project.id === orphanProject.id">
        <menu-item-group>
          <menu-item-button @click="$emit('add-table')">Add blank table...</menu-item-button>
          <menu-item-button @click="$emit('import')">Import data to project...</menu-item-button>
        </menu-item-group>
      </template>

      <template v-else-if="context.project">
        <menu-item-group :title="context.project.title">
          <menu-item-button @click="$emit('add-table', context.project)">Add blank table to project...
          </menu-item-button>
          <menu-item-button @click="$emit('import', context.project)">Import data to project...</menu-item-button>
        </menu-item-group>
        <menu-item-group>
          <menu-item-button @click="$emit('edit-project', context.project)">Edit project...</menu-item-button>
          <menu-item-button class="text-red-600" @click="$refs.deleteProjectDialog.open()">Delete project...
          </menu-item-button>
        </menu-item-group>
      </template>
    </context-menu>

    <confirm-dialog
        title="Delete Table"
        text="Are you sure you want to delete this table?"
        ref="deleteTableDialog"
        @confirm="deleteTable(context.table.id)"
    />

    <confirm-dialog
        title="Delete Project"
        text="Are you sure you want to delete this project? Tables in this project will not be deleted."
        ref="deleteProjectDialog"
        @confirm="deleteProject(context.project.id)"
    />
  </teleport>
</template>

<script>

import { mapActions, mapState, mapWritableState } from 'pinia'
import { ChevronDown, FolderPlus, Grid2x2Plus, HardDriveUpload, X } from 'lucide-vue-next'

import BrandImage from '@/components/ui/BrandImage.vue'
import { usePrefsStore } from '@/stores/prefs.js'
import TransitionFixedSlide from '@/components/transitions/TransitionFixedSlide.vue'
import TheUserProfile from '@/components/ui/TheUserProfile.vue'
import BaseDropzone from '@/components/ui/BaseDropzone.vue'
import InputSearch from '@/components/forms/input/InputSearch.vue'
import { useProjectsStore } from '@/stores/projects.js'
import { useTableStore } from '@/stores/table.js'
import ContextMenu from '@/components/ui/menu/ContextMenu.vue'
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import ConfirmDialog from '@/components/modal/ConfirmDialog.vue'
import router from '@/router/index.js'
import ModalAddNewTable from '@/components/modal/ModalAddNewTable.vue'
import TransitionDropdown from '@/components/transitions/TransitionDropdown.vue'
import { Menu as VMenu, MenuButton, MenuItems } from '@headlessui/vue'
import ModalAddEditProject from '@/components/modal/ModalAddEditProject.vue'
import { useAppStore } from '@/stores/app.js'
import SiteSidebarProjectList from '@/components/ui/sidebar/SiteSidebarProjectList.vue'
import SiteSidebarProject from '@/components/ui/sidebar/SiteSidebarProject.vue'

export default {
  components: {
    SiteSidebarProject,
    SiteSidebarProjectList,
    ModalAddEditProject,
    VMenu,
    MenuItems,
    MenuButton,
    TransitionDropdown,
    ChevronDown, FolderPlus, Grid2x2Plus, HardDriveUpload,
    X,
    ModalAddNewTable,
    ConfirmDialog,
    MenuItemButton,
    MenuItemGroup,
    ContextMenu,
    BrandImage,
    TransitionFixedSlide,
    TheUserProfile,
    BaseDropzone,
    InputSearch
  },
  emits: ['file-drop', 'add-table', 'import', 'show-context-menu', 'edit-table', 'edit-project', 'add-project'],

  data () {
    return {
      searchText: '',

      // holds the context when we right-click for a context menu
      context: {
        project: null,
        table: null
      }
    }
  },
  computed: {
    ...mapState(useProjectsStore, {
      projectsStoreProjects: 'projects',
      projectsStoreOrphanTables: 'orphanTables'
    }),
    ...mapState(useAppStore, {
      appStoreModalIsOpen: 'modalIsOpen'
    }),
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreProjectId: 'project'
    }),
    ...mapWritableState(usePrefsStore, {
      prefsStoreSiteSidebarStatus: 'siteSidebarStatus'
    }),

    filteredProjects () {
      return this.projectsStoreProjects && this.projectsStoreProjects.filter(
          project => project.title.toLowerCase().indexOf(this.searchText.toLowerCase()) > -1
      )
    },

    orphanProject () {
      // Gets a fake 'project' for all teh orphaned tables
      return {
        id: null,
        title: 'Ungrouped Tables',
        tables: this.projectsStoreOrphanTables
      }
    }
  },
  methods: {
    ...mapActions(useProjectsStore, {
      projectsStoreDeleteTable: 'deleteTable',
      projectsStoreDelete: 'delete',
      projectsStoreReload: 'reload'
    }),
    showContextMenu ({ e, project, table }) {
      this.context.project = project
      this.context.table = table
      this.$refs.contextMenu.showMenu(e)
    },

    async deleteTable (tableId) {
      // Triggers only after a confirm dialog has shown
      await this.projectsStoreDeleteTable(tableId)
      await this.projectsStoreReload()

      /* if we are viewing the table just deleted, reset and redirect */
      if (this.tableStoreId === tableId) {
        await router.push({ name: 'app-dashboard' })
      }
    },

    async deleteProject (projectId) {
      // Triggers only after a confirm dialog has shown
      await this.projectsStoreDelete(projectId)
      await this.projectsStoreReload()

      /* if we are viewing the table just deleted, reset and redirect */
      if (this.tableStoreProjectId === projectId) {
        await router.push({ name: 'app-dashboard' })
      }
    }
  }
}
</script>

<style scoped>

nav {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

nav:hover {
  scrollbar-color: inherit;
}

</style>
