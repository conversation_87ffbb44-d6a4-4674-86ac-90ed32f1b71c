<template>
  <li class="text-sm"
      :class="{ 'bg-gray-700': tableDraggingOver }"
      @dragover="dragOverProject"
      @dragleave="tableDraggingOver = false"
      @drop="dropTableOnProject($event, project)">
    <div
        class="font-light flex items-center justify-between cursor-pointer group/project"
        :class="current || tableDraggingOver ? 'text-gray-100' : 'text-gray-300 hover:text-gray-100'"
        @contextmenu.exact.prevent="e => $emit('show-context-menu', { e, project })">
      <a class="block flex-1 cursor-pointer"
         @click.prevent="$emit('select-project', current ? undefined : project.id)">
        {{ project.title }}
      </a>
      <div
          class="flex-none flex space-x-2 ml-1 transition text-transparent group-hover/project:text-gray-400 cursor-pointer">
        <div v-if="!orphanProject" class="hover:group-hover/project:text-gray-100" title="Edit project"
             @click="$emit('edit-project', project)">
          <Pencil class="size-4"/>
        </div>
        <div class="hover:group-hover/project:text-gray-100" title="Add new table to project"
             @click="$emit('add-table', project)">
          <Grid-2x2-Plus class="size-4"/>
        </div>
        <div class="hover:group-hover/project:text-gray-100" title="Import data to project"
             @click="$emit('import-data', project)">
          <HardDriveUpload class="size-4"/>
        </div>
      </div>
    </div>
    <transition
        mode="out-in"
        enter-active-class="transition-all duration-300"
        leave-active-class="transition-all duration-100"
        enter-from-class="max-h-0"
        enter-to-class="max-h-60"
        leave-from-class="max-h-60"
        leave-to-class="max-h-0">
      <div v-show="current && project.tables.length" class="overflow-hidden text-sm">
        <ul class="mt-2 space-y-2 border-l border-gray-700">
          <site-sidebar-table
              draggable="true"
              @dragstart="e => startTableDrag(e, table)"
              v-for="table in project.tables"
              :key="table.id"
              :table="table"
              :selected="tableId === table.id"
              @contextmenu.exact.prevent="e => $emit('show-context-menu', { e, project, table })"
              @edit-table="$emit('edit-table', $event)"
          />
        </ul>
      </div>
    </transition>
  </li>
</template>
<script>

import SiteSidebarTable from '@/components/ui/sidebar/SiteSidebarTable.vue'
import { Grid2x2Plus, HardDriveUpload, Pencil } from 'lucide-vue-next'
import { mapActions } from 'pinia'
import { useProjectsStore } from '@/stores/projects.js'

export default {
  components: {
    SiteSidebarTable,
    Pencil,
    Grid2x2Plus,
    HardDriveUpload
  }, emits: ['select-project', 'edit-project', 'edit-table', 'show-context-menu', 'add-table', 'import-data'],
  props: {
    project: Object,
    current: Boolean,
    orphanProject: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      tableDraggingOver: false
    }
  },
  computed: {
    tableId () {
      return this.$route.params.tableId
    }
  },
  methods: {
    ...mapActions(useProjectsStore, {
      moveTable: 'moveTable'
    }),
    startTableDrag (event, table) {
      event.dataTransfer.clearData()
      event.dataTransfer.setData('sidebar-source', 1)
      event.dataTransfer.setData('application/json', JSON.stringify(table))
    },

    getDragTable (event) {
      const data = event.dataTransfer.getData('application/json')
      if (data) {
        const json = JSON.parse(data)
        if (json.id) {
          return json
        }
      }
    },

    dragOverProject (event) {
      // In chrome, datatransfer data is 'protected' during a drag, so values cannot
      // be accessed; but types can
      if (event.dataTransfer.types.includes('sidebar-source')) {
        event.preventDefault()
        event.dataTransfer.dropEffect = 'move'
        this.tableDraggingOver = true
      } else {
        event.dataTransfer.dropEffect = 'none'
        this.tableDraggingOver = false
      }
    },

    dropTableOnProject (event, project) {
      this.tableDraggingOver = false
      const table = this.getDragTable(event)
      if (table) {
        this.moveTable(table.id, project.id)
      }
    }
  }
}
</script>
