<template>
  <div class="overflow-y-auto">
    <ul role="list" class="divide-y divide-gray-100">
      <li
          v-for="item in items" :key="item.id"
          class="relative flex flex-col px-4 py-4 text-sm hover:bg-zinc-100">
        <slot :item="item">{{ item }}</slot>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'DataList',
  props: {
    items: {
      type: Array,
      required: true
    },
    selectedId: {
      type: String,
      required: false
    }
  }
}
</script>


<style scoped>
.overflow-y-auto {
  scrollbar-width: thin;
}
</style>
