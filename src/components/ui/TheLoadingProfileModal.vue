<template>
  <base-modal ref="base" small hide-buttons :closable="false">
    <div class="grid grid-cols-1 justify-items-center text-center p-4 space-y-8">
      <div class="text-lg font-semibold">
        Welcome back to <span class="text-fuchsia-700">DataHero</span>
      </div>
      <div class="text-base">Loading your profile...</div>
      <div>
        <loading-spinner class="w-16 h-16"/>
      </div>
    </div>
  </base-modal>
</template>

<script>
import BaseModal from '@/components/modal/BaseModal.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'

export default {
  components: { LoadingSpinner, BaseModal },
  methods: {
    open () {
      this.$refs.base.open()
    },
    close () {
      this.$refs.base.close(true)
    }
  }
}
</script>

<style scoped></style>
