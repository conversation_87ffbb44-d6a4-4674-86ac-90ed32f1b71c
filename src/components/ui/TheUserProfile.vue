<template>
  <div class="shrink-0 flex border-t border-gray-600 bg-gray-800 p-4">
    <Menu as="div" class="relative flex flex-1">
      <transition-dropdown>
        <menu-items
            class="origin-top-right absolute left-0 -bottom-full w-64 mb-20 z-10 bg-white divide-y divide-gray-100 font-normal rounded-md shadow-lg ring-1 ring-black/5 focus:outline-hidden">
          <menu-item-group v-if="authStoreOrganizations" title="Select Workspace">
            <menu-item-button
                v-for="org in authStoreOrganizations"
                :selected="org.id === authStoreWorkspaceId"
                :key="org.id"
                @click="switchWorkspace(org.id)">
              {{ org.name }}
            </menu-item-button>
            <menu-item-button :selected="authStoreWorkspaceId === null" @click="switchWorkspace(null)">
              Personal
            </menu-item-button>
          </menu-item-group>
          <menu-item-group>
            <menu-item-button v-if="debug" @click="test400">Test 400</menu-item-button>
            <menu-item-button @click="$router.push({ name: 'change-password'})">Change Password</menu-item-button>

            <context-sub-menu label="About DataHero...">
              <menu-item-group>
                <menu-item-button @click="$router.push({ name: 'homepage'})">Go to Homepage</menu-item-button>
                <menu-item-button @click="$router.push({ name: 'blog'})">DataHero blog</menu-item-button>
                <menu-item-button @click="$router.push({ name: 'contact-us'})">Contact us</menu-item-button>
              </menu-item-group>
            </context-sub-menu>

          </menu-item-group>
          <menu-item-group>
            <menu-item-button @click="logout">Log out</menu-item-button>
          </menu-item-group>
        </menu-items>
      </transition-dropdown>
      <menu-button class="shrink-0 w-full group block cursor-pointer">
        <div class="flex items-center">
          <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-900">
            <svg class="h-full w-full text-gray-600" fill="currentColor" viewBox="0 0 24 24">
              <path
                  d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"/>
            </svg>
          </span>
          <div class="ml-3 flex-1 text-left">
            <p class="text-sm font-medium text-white">{{ profileTitle }}</p>
            <p class="text-xs font-medium text-gray-200 group-hover:text-white">
              {{ profileSub }}
            </p>
          </div>
          <ChevronUp class="w-5 h-5 ml-2 -mr-1 text-white" aria-hidden="true"/>
        </div>
      </menu-button>
    </Menu>
  </div>
</template>
<script>
import { mapActions, mapState, mapWritableState } from 'pinia'

import { Menu, MenuButton, MenuItems } from '@headlessui/vue'
import MenuItemButton from '@/components/ui/menu/MenuItemButton.vue'
import TransitionDropdown from '@/components/transitions/TransitionDropdown.vue'
import { ChevronUp } from 'lucide-vue-next'
import MenuItemGroup from '@/components/ui/menu/MenuItemGroup.vue'
import { usePrefsStore } from '@/stores/prefs.js'
import { useAuthStore } from '@/stores/auth'
import { useProjectsStore } from '@/stores/projects.js'
import { useTableStore } from '@/stores/table.js'
import ContextSubMenu from '@/components/ui/menu/ContextSubMenu.vue'
import router from '@/router/index.js'

export default {
  components: {
    ContextSubMenu, MenuItemGroup, ChevronUp, TransitionDropdown, MenuItemButton, Menu, MenuButton, MenuItems
  },
  computed: {
    ...mapState(useAuthStore, {
      authStoreIsLoggedIn: 'isLoggedIn',
      authStoreUser: 'user',
      authStoreWorkspace: 'workspace',
      authStoreIsServerDown: 'isServerDown',
      authStoreOrganizations: 'organizations'
    }),
    ...mapWritableState(usePrefsStore, {
      authStoreWorkspaceId: 'workspaceId'
    }),

    debug () {
      return import.meta.env.VITE_DEBUG === 'true'
    },

    profileTitle () {
      if (this.authStoreIsLoggedIn) {
        return this.authStoreUser.name
      }
      return 'Offline Mode'
    },

    profileSub () {
      if (this.authStoreIsLoggedIn) {
        if (this.authStoreWorkspace) {
          return this.authStoreWorkspace.name
        }
        return 'Personal'
      }
      if (this.authStoreIsServerDown) {
        return 'Server is offline'
      }
      return 'You are disconnected'
    }
  },

  methods: {
    ...mapActions(useAuthStore, {
      authStoreClear: 'clearAuth'
    }),
    router () {
      return router
    },
    ...mapActions(useProjectsStore, {
      projectsStoreReload: 'reload'
    }),

    switchWorkspace (id) {
      this.authStoreWorkspaceId = id
      this.projectsStoreReload()
      this.$router.push({ name: 'app-dashboard' })
    },

    async logout () {
      // Flush any save queue before logging out
      await useTableStore().flushQueue()
      this.authStoreClear()
    },

    test400 () {
      const tableId = useTableStore().id
      useAuthStore().patch(`tables/${tableId}/update-column/0/`, { name: 'bad-col-data' })
    }

  }
}

</script>
