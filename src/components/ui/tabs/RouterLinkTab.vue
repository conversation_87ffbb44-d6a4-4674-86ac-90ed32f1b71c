<template>
  <router-link
      :key="name"
      :to="to"
      v-slot="{ isActive }"
      class="group inline-flex justify-center py-2.5 px-2.5 rounded-t-md items-center text-sm font-medium text-zinc-500 hover:bg-fuchsia-700 transition-all duration-100 hover:text-white"
      active-class="bg-fuchsia-600! text-white!"
      :title="name"
  >
    <component v-if="icon" :is="icon" class="-ml-0.5 mr-2 h-5 w-5" aria-hidden="true"/>
    <slot :isActive="isActive">
      <span>{{ name }}</span>
    </slot>
  </router-link>
</template>

<script>
export default {
  name: 'RouterLinkTab',
  props: {
    name: {
      required: false,
      type: String
    },
    to: Object,
    icon: {
      required: false,
      default: null,
      type: Object
    }
  }
}
</script>


<style scoped>

</style>
