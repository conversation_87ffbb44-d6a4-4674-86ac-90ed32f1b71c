<template>
  <div ref="mainDiv" class="relative overflow-x-hidden flex items-center">
    <button
        v-if="!allTabsFit"
        @click="scrollTabs('left')"
        :disabled="!canScrollLeft"
        class="p-2 bg-zinc-100 hover:bg-zinc-200 disabled:bg-transparent text-zinc-700 disabled:text-zinc-300 rounded-sm mr-1"
        :class="{ 'cursor-pointer': canScrollLeft }"
    >
      <ChevronLeft class="size-5" aria-hidden="true"/>
    </button>

    <div class="flex-1 overflow-x-hidden">
      <nav
          ref="tabList"
          class="flex whitespace-nowrap scroll-smooth overflow-x-auto hide-scrollbar space-x-0.5"
          aria-label="Tabs">
        <slot>
          <router-link-tab
              v-for="tab in tabs"
              :key="tab.name"
              :to="tab.to"
              :name="tab.name"/>
        </slot>
      </nav>
    </div>

    <button
        v-if="!allTabsFit"
        @click="scrollTabs('right')"
        :disabled="!canScrollRight"
        class="p-2 bg-zinc-100 hover:bg-zinc-200 disabled:bg-transparent text-zinc-700 disabled:text-zinc-300 rounded-sm ml-1"
        :class="{ 'cursor-pointer': canScrollRight }"
    >
      <ChevronRight class="size-5" aria-hidden="true"/>
    </button>
  </div>
</template>

<script>
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'
import RouterLinkTab from '@/components/ui/tabs/RouterLinkTab.vue'

export default {
  components: {
    RouterLinkTab,
    ChevronLeft,
    ChevronRight
  },
  props: {
    tabs: {
      type: Array,
      required: false
    },
    small: {
      type: Boolean,
      default: false
    },
    restrictWidth: {
      type: Boolean,
      default: false
    },
    minWidth: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      canScrollLeft: false,
      canScrollRight: false,
      mutationObserver: null,
      allTabsFit: true
    }
  },

  watch: {
    $route: {
      immediate: true,
      handler () {
        // make sure the active tab is in view on mount on next tick
        this.$nextTick(() => {
          const activeTab = this.$refs.tabList.querySelector('.router-link-exact-active')
          if (activeTab) {
            activeTab.scrollIntoView({ behavior: 'smooth', inline: 'nearest' })
          }
        })
      }
    }
  },

  methods: {
    updateScrollState () {
      const tabList = this.$refs.tabList
      this.canScrollLeft = tabList.scrollLeft > 0
      this.canScrollRight = (tabList.scrollLeft + tabList.clientWidth) < tabList.scrollWidth
      this.allTabsFit = tabList.scrollWidth <= tabList.clientWidth
    },
    scrollTabs (direction) {
      const tabList = this.$refs.tabList
      const scrollAmount = tabList.clientWidth / 2 // Adjust the scroll amount as needed
      if (direction === 'left') {
        tabList.scrollLeft -= scrollAmount
      } else if (direction === 'right') {
        tabList.scrollLeft += scrollAmount
      }
    },
    observeActiveTab () {
      const tabList = this.$refs.tabList
      this.mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const target = mutation.target
            if (target.classList.contains('router-link-exact-active')) {
              target.scrollIntoView({ behavior: 'smooth', inline: 'nearest' })
            }
          }
        })
      })
      Array.from(tabList.children).forEach((tab) => {
        this.mutationObserver.observe(tab, { attributes: true, attributeFilter: ['class'] })
      })
    }
  },
  mounted () {
    this.resizeObserver = new ResizeObserver(this.updateScrollState)
    this.resizeObserver.observe(this.$refs.mainDiv)
    window.addEventListener('resize', this.updateScrollState)
    this.updateScrollState()
    this.$refs.tabList.addEventListener('scroll', this.updateScrollState)
    this.observeActiveTab()
  },

  beforeUnmount () {
    if (this.resizeObserver) {
      this.resizeObserver.unobserve(this.$refs.mainDiv)
      this.resizeObserver.disconnect()
    }
    if (this.mutationObserver) {
      this.mutationObserver.disconnect()
    }
    window.removeEventListener('resize', this.updateScrollState)
    this.$refs.tabList.removeEventListener('scroll', this.updateScrollState)
  }
}
</script>

<style scoped>
.hide-scrollbar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* Firefox */
.hide-scrollbar {
  scrollbar-width: none;
}

/* Internet Explorer and Edge */
.hide-scrollbar {
  -ms-overflow-style: none;
}
</style>
