<template>
  <a
      :key="name"
      @click="$router.push(to)"
      class="group cursor-pointer inline-flex justify-center py-2.5 px-2.5 rounded-t-md items-center text-sm font-medium transition-all duration-100"
      :class="[
          isActive ? 'router-link-exact-active bg-fuchsia-600 text-white' : 'text-zinc-500 hover:text-white hover:bg-fuchsia-700',
      ]"
      :title="name"
  >
    <component v-if="icon" :is="icon" class="-ml-0.5 mr-2 h-5 w-5" aria-hidden="true"/>
    <slot :isActive="isActive">
      <span>{{ name }}</span>
    </slot>
  </a>
</template>

<script>
export default {
  name: 'BaseTab',
  props: {
    isActive: Boolean,
    name: {
      required: false,
      type: String
    },
    to: Object,
    icon: {
      required: false,
      default: null,
      type: Function
    }
  }
}
</script>


<style scoped>

</style>
