<template>
  <div
      class="splitter bg-gray-300 h-full cursor-row-resize"
      @mousedown="dragging = true"
  ></div>
</template>

<script>
export default {
  emits: ['move', 'up'],

  data () {
    return {
      dragging: false
    }
  },

  watch: {
    dragging (val) {
      // this prevents text selecting when the mouse is down on a drag
      document.querySelector('body').classList.toggle('select-none', val)
    }
  },

  methods: {
    mouseMove (e) {
      if (this.dragging) {
        this.$emit('move', e.movementY)
      }
    },

    mouseUp (e) {
      this.dragging = false
      this.$emit('up', e.movementY)
    }
  },

  mounted () {
    window.addEventListener('mousemove', this.mouseMove)
    window.addEventListener('mouseup', this.mouseUp)
  },

  unmounted () {
    window.removeEventListener('mousemove', this.mouseMove)
    window.removeEventListener('mouseup', this.mouseUp)
  }
}
</script>

<style scoped></style>
