<template>
  <transition-root :show="alwaysShow || dragging">
    <transition-child
        as="template"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave-from="opacity-80"
        leave-to="opacity-0"
    >
      <div
          class="dropzone p-8 inset-0 z-40 bg-gray-800/80 flex items-center justify-center transition-opacity duration-150"
          :class="[absolute ? 'absolute' : 'fixed']"
          @click="this.dragging = false"
          @drop.prevent="handleDrop"
          @dragover.prevent
          @dragleave.prevent
          @dragenter.prevent
      >
        <slot>
          <div
              class="flex flex-col space-y-6 pointer-events-none text-center text-fuchsia-50"
              :class="[small ? 'text-2xl' : 'text-4xl']"
          >
            <ArrowUpFromLine
                class="mx-auto"
                :class="[small ? 'w-12 h-12' : 'w-24 h-24']"
            />
            <p>{{ label }}</p>
          </div>
        </slot>
      </div>
    </transition-child>

    <teleport to="body" v-if="absolute">
      <transition-child
          as="template"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave-from="opacity-100"
          leave-to="opacity-0"
      >
        <div
            class="dropzone-background fixed inset-0 z-30 bg-gray-800/50 transition-opacity duration-150"
            :draggable="false"
            @drop.prevent
            @dragover.prevent="noDrop"
            @dragend.prevent="noDrop"
        ></div>
      </transition-child>
    </teleport>
  </transition-root>
</template>

<script>
import { ArrowUpFromLine } from 'lucide-vue-next'
import { TransitionChild, TransitionRoot } from '@headlessui/vue'

export default {
  name: 'BaseDropzone',
  components: { TransitionChild, TransitionRoot, ArrowUpFromLine },
  emits: ['file-drop'],
  props: {
    alwaysShow: {
      type: Boolean,
      default: false
    },
    absolute: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: 'Drop your file here'
    }
  },
  data () {
    return {
      dragging: false,
      dragTarget: undefined
    }
  },
  methods: {
    noDrop (e) {
      e.dataTransfer.dropEffect = 'none'
      e.dataTransfer.effectAllowed = 'none'
    },
    handleDrop (e) {
      this.dragging = false
      this.$emit('file-drop', e.dataTransfer.files)
    },
    dragEnter (e) {
      const hasFiles = e.dataTransfer.types.includes('Files')
      if (hasFiles) {
        this.dragTarget = e.target
        this.dragging = true
      }
    },
    dragLeave (e) {
      if (e.target === this.dragTarget || e.target === document) {
        this.dragging = false
      }
    }
  },

  mounted () {
    window.addEventListener('dragenter', this.dragEnter)
    window.addEventListener('dragleave', this.dragLeave)
  },

  unmounted () {
    window.removeEventListener('dragenter', this.dragEnter)
    window.removeEventListener('dragleave', this.dragLeave)
  }
}
</script>

<style scoped></style>
