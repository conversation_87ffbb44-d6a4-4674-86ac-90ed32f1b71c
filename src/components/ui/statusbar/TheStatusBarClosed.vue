<template>
  <div class="relative h-8">
    <nav
        class="bg-gray-900 absolute bottom-0 inset-x-0 z-20 group p-2 flex items-center h-8 hover:h-10 hover:bg-gray-800 transition-all cursor-pointer text-gray-400 hover:text-white"
        @click="open">
      <div class="flex-1">
        <p v-if="tableStoreDbTable" class="text-xs truncate">
          <!-- Single column selected -->
          <template v-if="appStoreSelectedColumnIds.length === 1">
            1 column selected. Hold down shift or ctrl to select multiple columns. Click to chart this data.
          </template>

          <!-- Multiple columns selected -->
          <template v-else-if="appStoreSelectedColumnIds.length > 1">
            {{ appStoreSelectedColumnIds.length }} columns selected. Click to chart this data.
          </template>

          <!-- All rows selected -->
          <template v-else-if="appStoreSelectedRowIds.length === tableStoreViewRowCount">
            {{ tableStoreViewRowCount > 1 ? 'All ' : '' }}{{ tableStoreViewRowCount }} {{
              pluralize('row', tableStoreViewRowCount)
            }} selected. Click
            to chart this data.
          </template>

          <!-- Single row selected -->
          <template v-else-if="appStoreSelectedRowIds.length === 1">
            1 of {{ tableStoreViewRowCount }} {{ pluralize('row', tableStoreViewRowCount) }} selected. Hold down shift
            or ctrl to select
            multiple rows. Click to chart this data.
          </template>

          <!-- Multiple rows selected -->
          <template v-else-if="appStoreSelectedRowIds.length > 1">
            {{ appStoreSelectedRowIds.length }} of {{ tableStoreViewRowCount }}
            {{ pluralize('row', tableStoreViewRowCount) }} selected. Click to
            chart this data.
          </template>

          <!-- Cells selected -->
          <template v-else-if="selectedCellsCount">
            {{ selectedCellsCount }} cells selected ({{
              appStoreSelectedCells.cols[1] - appStoreSelectedCells.cols[0] + 1
            }}x{{ appStoreSelectedCells.rows[1] - appStoreSelectedCells.rows[0] + 1 }}). Click to chart this data.
          </template>

          <!-- Has existing charts -->
          <template v-else-if="tableStoreCharts.length">
            {{ tableStoreViewRowCount }} {{ pluralize('row', tableStoreViewRowCount) }}. Click to view your charts for
            this table.
          </template>

          <!-- Default state -->
          <template v-else>
            {{ tableStoreViewRowCount }} {{ pluralize('row', tableStoreViewRowCount) }}. Click to create a new chart.
          </template>
        </p>
      </div>
      <button type="button" class="flex-none rounded-md">
        <ChevronsUp class="h-4 w-4" aria-hidden="true"/>
      </button>
    </nav>
  </div>
</template>
<script>
import { ChevronsUp } from 'lucide-vue-next'
import { mapState } from 'pinia'
import { useAppStore } from '@/stores/app.js'
import { useTableStore } from '@/stores/table.js'
import { pluralize } from '@/utils/helpers.js'

export default {
  components: { ChevronsUp },
  computed: {
    ...mapState(useAppStore, {
      appStoreSelectedCells: 'selectedCells',
      appStoreSelectedColumnIds: 'selectedColumnIds',
      appStoreSelectedRowIds: 'selectedRowIds'
    }),
    ...mapState(useTableStore, {
      tableStoreViewRowCount: 'viewRowCount',
      tableStoreId: 'id',
      tableStoreCharts: 'charts',
      tableStoreDbTable: 'dbTable'
    }),

    selectedCellsCount () {
      if (!this.appStoreSelectedCells) return
      return (this.appStoreSelectedCells.cols[1] - this.appStoreSelectedCells.cols[0] + 1) * (this.appStoreSelectedCells.rows[1] - this.appStoreSelectedCells.rows[0] + 1)
    }
  },

  methods: {
    pluralize,
    open () {
      // if we have no rows or cols selected, and we have charts, go to the chart list
      if (!this.selectedCellsCount && !this.appStoreSelectedColumnIds.length && !this.appStoreSelectedRowIds.length && this.tableStoreCharts.length) {
        this.$router.push({
          name: `app-${this.$route.params.viewId ? 'view' : 'table'}-charts`,
          params: {
            tableId: this.tableStoreId
          }
        })
      } else {
        this.$router.push({
          name: `app-${this.$route.params.viewId ? 'view' : 'table'}-chart-add`,
          params: {
            tableId: this.tableStoreId
          }
        })
      }
    }
  }

}
</script>

<style scoped>

</style>
