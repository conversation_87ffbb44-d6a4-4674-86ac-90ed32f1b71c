<template>
  <nav class="flex-none shrink-0 bg-gray-900 p-2 flex items-center">
    <div class="flex-1"></div>
    <button
        type="button"
        class="flex-none rounded-md cursor-pointer  text-gray-400 transition-all hover:text-white"
        @click="$emit('close')">
      <span class="sr-only">Close panel</span>
      <X class="size-5" aria-hidden="true"/>
    </button>
  </nav>
</template>

<script>
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { X } from 'lucide-vue-next'

export default {
  components: {
    BaseBtn,
    X
  },
  emits: ['close']
}
</script>

<style scoped>

</style>
