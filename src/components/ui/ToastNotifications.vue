<template>
  <div
      class="fixed bottom-0 right-0 p-4 space-y-2 w-80"
      :class="[notificationStoreActives.length > 0 ? 'z-50' : '-z-10']">
    <transition-group
        name="notification"
        tag="div"
        class="space-y-2"
    >
      <div
          v-for="notification in notificationStoreActives"
          :key="notification.id"
          data-test="notification"
          :class="[
          'flex items-start p-4 rounded-md shadow-lg transform transition-all duration-300',
          getNotificationClass(notification.type)
        ]"
      >
        <!-- Icon based on notification type -->
        <div class="shrink-0 mr-3">
          <loading-spinner v-if="notification.status === 'processing'" class="w-5 h-5" data-test="loading-spinner"/>
          <CheckCircle v-else-if="notification.type === 'success'" class="h-5 w-5 text-green-400"/>
          <XCircle v-else-if="notification.type === 'error'" class="h-5 w-5 text-red-400"/>
          <TriangleAlert v-else-if="notification.type === 'warning'" class="h-5 w-5 text-yellow-400"/>
          <PlusCircle v-else-if="notification.type === 'debug'" class="h-5 w-5 text-purple-400"/>
          <Info v-else class="h-5 w-5 text-blue-400"/>
        </div>

        <!-- Content -->
        <div class="flex-1">
          <div class="flex justify-between items-start">
            <p class="text-sm font-medium">
              {{ notification.message }}
            </p>
            <button
                v-if="notification.dismissible"
                @click="dismiss(notification.id)"
                class="ml-3 shrink-0 text-gray-400 hover:text-gray-500 focus:outline-hidden cursor-pointer"
                aria-label="Close"
            >
              <span class="sr-only">Close</span>
              <X class="h-5 w-5"/>
            </button>
          </div>

          <!-- Details if available -->
          <div v-if="notification.details" class="mt-2">
            <p class="text-sm text-gray-600">{{ notification.details }}</p>
          </div>

          <!-- Action buttons -->
          <div v-if="notification.actions && notification.actions.length" class="mt-3 flex space-x-2">
            <button
                v-for="(action, index) in notification.actions"
                :key="index"
                @click="handleAction(notification.id, action)"
                data-test="action-button"
                class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded-sm text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {{ action.label }}
            </button>
          </div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { mapActions, mapState } from 'pinia'
import { useNotificationStore } from '@/stores/notifications.js'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import { CheckCircle, Info, PlusCircle, TriangleAlert, X, XCircle } from 'lucide-vue-next'

export default {
  name: 'ToastNotifications',

  components: {
    LoadingSpinner,
    CheckCircle,
    XCircle,
    TriangleAlert,
    PlusCircle,
    Info,
    X
  },

  computed: {
    ...mapState(useNotificationStore, {
      notificationStoreActives: 'activeNotifications'
    })
  },

  methods: {
    ...mapActions(useNotificationStore, {
      notificationStoreRemove: 'remove'
    }),

    getNotificationClass (type) {
      const classes = {
        success: 'bg-green-50 text-green-800 border-l-4 border-green-400',
        error: 'bg-red-50 text-red-800 border-l-4 border-red-400',
        warning: 'bg-yellow-50 text-yellow-800 border-l-4 border-yellow-400',
        info: 'bg-blue-50 text-blue-800 border-l-4 border-blue-400',
        debug: 'bg-purple-50 text-purple-800 border-l-4 border-purple-400',
        processing: 'bg-gray-50 text-gray-800 border-l-4 border-gray-400'
      }
      return classes[type] || classes.info
    },

    dismiss (id) {
      this.notificationStoreRemove(id)
    },

    handleAction (notificationId, action) {
      if (typeof action.handler === 'function') {
        action.handler()
      }

      // If the action should dismiss the notification after execution
      if (action.dismissAfter) {
        this.dismiss(notificationId)
      }
    }
  }
}
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100px);
}
</style>
