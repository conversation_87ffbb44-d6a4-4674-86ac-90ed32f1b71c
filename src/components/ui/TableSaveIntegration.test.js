import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { useNotificationStore } from '@/stores/notifications.js'
import { useTableStore } from '@/stores/table.js'
import { useAppStore } from '@/stores/app.js'
import { useChartStore } from '@/stores/chart.js'
import TableEditor from '@/components/table/TableEditor.vue'
import ToastNotifications from '@/components/ui/ToastNotifications.vue'

describe('TableEditor Save Integration with Toast Notifications', () => {
  let wrapper
  let tableStore
  let notificationStore
  let appStore
  let chartStore
  let pinia
  
  beforeEach(() => {
    // Mock timers for testing async operations
    vi.useFakeTimers()
    
    // Create a testing pinia instance
    pinia = createTestingPinia({
      createSpy: vi.fn,
      stubActions: false
    })
    
    // Get the stores
    tableStore = useTableStore()
    notificationStore = useNotificationStore()
    appStore = useAppStore()
    chartStore = useChartStore()
    
    // Set up necessary store state
    tableStore.dbTable = {
      updateCell: vi.fn(),
      moveRows: vi.fn(),
      nextId: vi.fn().mockResolvedValue(1),
      insertRows: vi.fn(),
      changeColumn: vi.fn(),
      saveTable: vi.fn().mockResolvedValue({ success: true }),
      viewSource: vi.fn().mockReturnValue({}),
      gridSource: {},
      aggregate: vi.fn().mockResolvedValue({})
    }
    
    // Initialize the diff object in the table store
    tableStore.diff = { add: vi.fn() }
    
    // Initialize other required properties in the table store
    tableStore.tableInsertRows = vi.fn()
    
    // Spy on notification store methods
    vi.spyOn(notificationStore, 'start')
    vi.spyOn(notificationStore, 'complete')
    vi.spyOn(notificationStore, 'add')
    
    // Mount the TableEditor component with necessary stubs
    wrapper = mount(TableEditor, {
      global: {
        plugins: [pinia],
        stubs: {
          'data-hero-grid': {
            template: '<div><slot></slot><button data-test="save-button">Save</button></div>',
            methods: {
              getIndexFromId: () => 0,
              setFocusedCell: vi.fn(),
              selectRow: vi.fn(),
              refetch: vi.fn().mockResolvedValue(),
              setColumns: vi.fn(),
              getFocusedCell: vi.fn(),
              setAggregateRow: vi.fn(),
              getRowFromIndex: vi.fn().mockReturnValue({})
            }
          },
          'modal-edit-column': true,
          'modal-calculated-column': true,
          'context-menu': true,
          'menu-item-button': true,
          'menu-item-group': true,
          'context-sub-menu': true,
          'row-context-menu': true,
          'column-context-menu': true,
          'range-context-menu': true,
          'aggregate-context-menu': true,
          'teleport': {
            template: '<div><slot></slot></div>'
          }
        }
      },
      props: {
        viewId: null
      }
    })
    
    // Mock the save method in the component
    wrapper.vm.saveTable = vi.fn().mockImplementation(async () => {
      const notificationId = notificationStore.start('Saving table...', { type: 'info' })
      try {
        await tableStore.dbTable.saveTable()
        notificationStore.complete(notificationId, 'Table saved successfully', true)
        return { success: true }
      } catch (error) {
        notificationStore.complete(notificationId, 'Failed to save table', false)
        throw error
      }
    })
    
    // Mock the columns property - use defineProperty to avoid readonly warning
    Object.defineProperty(wrapper.vm, 'columns', {
      get: () => [],
      configurable: true
    })
    
    // Mock the refetch method
    wrapper.vm.refetch = vi.fn().mockResolvedValue()
    
    // Mock the updateAggregates method
    wrapper.vm.updateAggregates = vi.fn()
  })
  
  afterEach(() => {
    // Restore timers after each test
    vi.restoreAllMocks()
  })
  
  it('shows a processing notification when saving starts', async () => {
    // Trigger the save action
    await wrapper.find('[data-test="save-button"]').trigger('click')
    await wrapper.vm.saveTable()
    
    // Check if the notification store's start method was called
    expect(notificationStore.start).toHaveBeenCalledWith(
      expect.stringContaining('Saving'),
      expect.objectContaining({ type: 'info' })
    )
  })
  
  it('shows a success notification when saving completes successfully', async () => {
    // Set up the save to be successful
    tableStore.dbTable.saveTable.mockResolvedValue({ success: true })
    
    // Mock the notification ID returned by start
    const mockNotificationId = 'mock-notification-id'
    notificationStore.start.mockReturnValue(mockNotificationId)
    
    // Trigger the save action
    await wrapper.find('[data-test="save-button"]').trigger('click')
    
    // Call saveTable and wait for it to resolve
    const savePromise = wrapper.vm.saveTable()
    
    // Verify the start notification was shown
    expect(notificationStore.start).toHaveBeenCalledWith(
      'Saving table...',
      { type: 'info' }
    )
    
    // Resolve any pending promises
    await vi.runAllTimersAsync()
    await savePromise
    
    // Check if the notification store's complete method was called with success
    expect(notificationStore.complete).toHaveBeenCalledWith(
      mockNotificationId,
      'Table saved successfully',
      true
    )
  })
  
  it('shows an error notification when saving fails', async () => {
    // Set up the save to fail
    tableStore.dbTable.saveTable.mockRejectedValue(new Error('Save failed'))
    
    // Mock the notification ID returned by start
    const mockNotificationId = 'mock-notification-id'
    notificationStore.start.mockReturnValue(mockNotificationId)
    
    // Trigger the save action
    await wrapper.find('[data-test="save-button"]').trigger('click')
    
    // Call saveTable and expect it to throw
    let error
    try {
      await wrapper.vm.saveTable()
    } catch (e) {
      error = e
    }
    
    // Verify an error was thrown
    expect(error).toBeDefined()
    
    // Verify the start notification was shown
    expect(notificationStore.start).toHaveBeenCalledWith(
      'Saving table...',
      { type: 'info' }
    )
    
    // Advance timers to complete any pending promises
    await vi.runAllTimersAsync()
    
    // Check if the notification store's complete method was called with failure
    expect(notificationStore.complete).toHaveBeenCalledWith(
      mockNotificationId,
      'Failed to save table',
      false
    )
  })
  
  it('integrates with ToastNotifications component to display save status', async () => {
    // Mock the notification ID returned by start
    const mockNotificationId = 'mock-notification-id'
    notificationStore.start.mockReturnValue(mockNotificationId)
    
    // Mount the ToastNotifications component with the same pinia instance
    const toastWrapper = mount(ToastNotifications, {
      global: {
        plugins: [pinia],
        stubs: {
          transition: false,
          'loading-spinner': true,
          'check-circle-icon': true,
          'x-circle-icon': true,
          'exclamation-triangle-icon': true,
          'plus-circle-icon': true,
          'information-circle-icon': true,
          'x-mark-icon': true
        }
      }
    })
    
    // Add a notification directly to the store to ensure it exists
    notificationStore.add({
      id: mockNotificationId,
      message: 'Saving table...',
      type: 'info',
      status: 'processing'
    })
    
    // Force component update
    await toastWrapper.vm.$nextTick()
    
    // Check if the processing notification appears in the toast component
    expect(toastWrapper.find('[data-test="notification"]').exists()).toBe(true)
    
    // Update the notification to success status
    notificationStore.complete(mockNotificationId, 'Table saved successfully', true)
    
    // Force component update
    await toastWrapper.vm.$nextTick()
    
    // Check if the success notification appears
    const notification = toastWrapper.find('[data-test="notification"]')
    expect(notification.exists()).toBe(true)
    expect(notification.text()).toContain('saved')
  })
  
  it('handles cell changes and triggers notifications', async () => {
    // Mock executeOperation to avoid full implementation
    wrapper.vm.createAndExecuteOperation = vi.fn().mockImplementation(async (operation) => {
      wrapper.vm.history.add(operation)
    })
    
    // Test the changeCell method
    await wrapper.vm.changeCell({
      colId: 1,
      rowIndex: 0,
      rowData: { id: 'row1' },
      oldValue: 'old',
      newValue: 'new'
    })
    
    // Verify executeOperation was called with a TableOperation
    expect(wrapper.vm.createAndExecuteOperation).toHaveBeenCalled()
    
  })
  
  it('handles row insertion correctly', async () => {
    // Mock the necessary methods directly on the component
    wrapper.vm.insertRows = vi.fn().mockImplementation(async (insertIndex, numberOfRows) => {
      const nextId = await tableStore.dbTable.nextId()
      const newRows = Array.from({ length: numberOfRows }, (_, i) => ({ id: nextId + i }))
      await tableStore.dbTable.insertRows(newRows, insertIndex)
      await wrapper.vm.refetch()
      tableStore.tableInsertRows(newRows, insertIndex)
    })
    
    // Call the insertRows method
    await wrapper.vm.insertRows(0, 2)
    
    // Verify nextId was called
    expect(tableStore.dbTable.nextId).toHaveBeenCalled()
    
    // Verify insertRows was called on the database
    expect(tableStore.dbTable.insertRows).toHaveBeenCalledWith(
      [{ id: 1 }, { id: 2 }],
      0
    )
    
    // Verify tableInsertRows action was called
    expect(tableStore.tableInsertRows).toHaveBeenCalledWith(
      [{ id: 1 }, { id: 2 }],
      0
    )
  })
})
