import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import ToastNotifications from './ToastNotifications.vue'
import { useNotificationStore } from '@/stores/notifications.js'

describe('ToastNotifications Edge Cases', () => {
  let wrapper
  let store
  
  beforeEach(() => {
    // Create a testing pinia instance
    const pinia = createTestingPinia({
      createSpy: vi.fn,
      stubActions: false
    })
    
    // Mount the component with the testing pinia
    wrapper = mount(ToastNotifications, {
      global: {
        plugins: [pinia],
        stubs: {
          transition: false,
          'loading-spinner': true,
          'check-circle-icon': true,
          'x-circle-icon': true,
          'exclamation-triangle-icon': true,
          'plus-circle-icon': true,
          'information-circle-icon': true,
          'x-mark-icon': true
        }
      },
      attachTo: document.createElement('div')
    })
    
    // Get the notification store
    store = useNotificationStore()
    
    // Mock timers for testing auto-dismiss functionality
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  it('handles very long notification messages correctly', async () => {
    // Add a notification with a very long message
    const longMessage = 'This is an extremely long notification message that should be handled gracefully by the component. It contains many words and should test how the component deals with text overflow and wrapping.'
    
    store.add({
      id: 'long-message',
      message: longMessage,
      type: 'info'
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if the notification is rendered
    const notification = wrapper.find('[data-test="notification"]')
    expect(notification.exists()).toBe(true)
    expect(notification.text()).toContain(longMessage)
    
    // Check that the text is contained within the notification bounds
    // This is a visual test, but we can at least check that the component doesn't break
    expect(notification.element.scrollWidth).toBeLessThanOrEqual(notification.element.clientWidth + 5) // Allow small margin of error
  })
  
  it('handles the maximum number of notifications correctly', async () => {
    // Set the maximum number of notifications
    store.maxNotifications = 3
    
    // Add more notifications than the maximum
    for (let i = 1; i <= 5; i++) {
      store.add({
        id: `notification-${i}`,
        message: `Notification ${i}`,
        type: 'info'
      })
    }
    
    await wrapper.vm.$nextTick()
    
    // Check that only the maximum number of notifications are rendered
    const notifications = wrapper.findAll('[data-test="notification"]')
    expect(notifications.length).toBe(3)
    
    // Check that the most recent notifications are shown (3, 4, 5)
    expect(notifications[0].text()).toContain('Notification 3')
    expect(notifications[1].text()).toContain('Notification 4')
    expect(notifications[2].text()).toContain('Notification 5')
  })
  
  it('handles notifications with HTML content safely', async () => {
    // Add a notification with HTML content
    const htmlMessage = '<script>alert("XSS")</script><b>Bold text</b>'
    
    store.add({
      id: 'html-content',
      message: htmlMessage,
      type: 'info'
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if the notification is rendered
    const notification = wrapper.find('[data-test="notification"]')
    expect(notification.exists()).toBe(true)
    
    // Check that HTML is escaped and not rendered as HTML
    expect(notification.html()).toContain('&lt;script&gt;')
    expect(notification.html()).not.toContain('<b>Bold text</b>')
  })
  
  it('handles rapid addition and removal of notifications', async () => {
    // Add and immediately remove notifications in rapid succession
    for (let i = 1; i <= 10; i++) {
      const id = `rapid-${i}`
      store.add({
        id,
        message: `Rapid notification ${i}`,
        type: 'info'
      })
      
      // Remove odd-numbered notifications immediately
      if (i % 2 === 1) {
        store.remove(id)
      }
    }
    
    await wrapper.vm.$nextTick()
    
    // Check that only the even-numbered notifications remain
    const notifications = wrapper.findAll('[data-test="notification"]')
    expect(notifications.length).toBe(5) // 2, 4, 6, 8, 10
    
    for (let i = 0; i < notifications.length; i++) {
      const num = (i + 1) * 2
      expect(notifications[i].text()).toContain(`Rapid notification ${num}`)
    }
  })
  
  it('handles notifications with multiple action buttons correctly', async () => {
    // Create mock handlers
    const handler1 = vi.fn()
    const handler2 = vi.fn()
    const handler3 = vi.fn()
    
    // Add a notification with multiple action buttons
    store.add({
      id: 'multi-action',
      message: 'Notification with multiple actions',
      type: 'info',
      actions: [
        { label: 'Action 1', handler: handler1 },
        { label: 'Action 2', handler: handler2 },
        { label: 'Action 3', handler: handler3 }
      ]
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if all action buttons are rendered
    const actionButtons = wrapper.findAll('[data-test="action-button"]')
    expect(actionButtons.length).toBe(3)
    expect(actionButtons[0].text()).toBe('Action 1')
    expect(actionButtons[1].text()).toBe('Action 2')
    expect(actionButtons[2].text()).toBe('Action 3')
    
    // Click each button and check if the correct handler is called
    await actionButtons[0].trigger('click')
    expect(handler1).toHaveBeenCalledTimes(1)
    expect(handler2).not.toHaveBeenCalled()
    expect(handler3).not.toHaveBeenCalled()
    
    await actionButtons[1].trigger('click')
    expect(handler1).toHaveBeenCalledTimes(1)
    expect(handler2).toHaveBeenCalledTimes(1)
    expect(handler3).not.toHaveBeenCalled()
    
    await actionButtons[2].trigger('click')
    expect(handler1).toHaveBeenCalledTimes(1)
    expect(handler2).toHaveBeenCalledTimes(1)
    expect(handler3).toHaveBeenCalledTimes(1)
  })
  
  it('handles auto-dismiss timing correctly for multiple notifications', async () => {
    // Add notifications with different auto-dismiss durations
    store.add({
      id: 'quick-dismiss',
      message: 'Quick dismiss (1s)',
      type: 'info',
      duration: 1000
    })
    
    store.add({
      id: 'medium-dismiss',
      message: 'Medium dismiss (3s)',
      type: 'info',
      duration: 3000
    })
    
    store.add({
      id: 'slow-dismiss',
      message: 'Slow dismiss (5s)',
      type: 'info',
      duration: 5000
    })
    
    await wrapper.vm.$nextTick()
    
    // Initially all notifications should be visible
    expect(wrapper.findAll('[data-test="notification"]').length).toBe(3)
    
    // After 1 second, the quick one should be gone
    vi.advanceTimersByTime(1000)
    await wrapper.vm.$nextTick()
    expect(wrapper.findAll('[data-test="notification"]').length).toBe(2)
    expect(wrapper.text()).not.toContain('Quick dismiss')
    
    // After 3 seconds total, the medium one should be gone
    vi.advanceTimersByTime(2000) // 3s total
    await wrapper.vm.$nextTick()
    expect(wrapper.findAll('[data-test="notification"]').length).toBe(1)
    expect(wrapper.text()).not.toContain('Medium dismiss')
    
    // After 5 seconds total, all should be gone
    vi.advanceTimersByTime(2000) // 5s total
    await wrapper.vm.$nextTick()
    expect(wrapper.findAll('[data-test="notification"]').length).toBe(0)
  })
})
