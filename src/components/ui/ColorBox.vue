<template>
  <div
      class="w-6 h-6 border-2 rounded-md cursor-pointer transition-colors duration-100"
      :style="colorStyles"
      @click="$emit('click')"
      @mouseenter="hover = true"
      @mouseleave="hover = false"
  ></div>
</template>

<script>

import colorLib from '@kurkle/color'

export default {
  name: 'ColorBox',
  emits: ['click'],
  props: {
    // Must be a color object as created by createColorObject
    color: {
      type: Object,
      required: true
    },
    // If selected, we use the hover border regardless
    selected: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    hover: false
  }),
  computed: {
    colorStyles () {
      const background = (this.selected || this.hover) ? 'hoverBackground' : 'background'
      const border = (this.selected || this.hover) ? 'hoverBorder' : 'border'
      return {
        'background-color': colorLib(this.color[background]).hexString(),
        'border-color': colorLib(this.color[border]).hexString()
      }
    }
  }
}
</script>

<style scoped>

</style>
