<template>
  <div
      v-if="shown"
      class="fixed z-30 inset-0 overflow-y-auto"
      aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 sm:block sm:p-0">
      <div class="fixed inset-0"
           @click="closeMenu"
           @contextmenu.exact.prevent="closeMenu"
      ></div>
      <Menu as="div">
        <focus-trap>
          <menu-items
              static
              ref="menu"
              @click="closeMenu()"
              class="fixed z-10 bg-white divide-y divide-gray-100 font-normal rounded-md shadow-lg ring-1 ring-black/10 focus:outline-hidden"
              :style="{
                top: `${pos.top}px`,
                left: `${pos.left}px`,
                width: `${width}px`
              }">
            <slot></slot>
          </menu-items>
        </focus-trap>
      </Menu>

    </div>
  </div>
</template>

<script>
import { FocusTrap, Menu, MenuItems, TransitionChild, TransitionRoot } from '@headlessui/vue'

export default {
  name: 'ContextMenu',
  emits: ['close'],
  props: {
    width: {
      type: Number,
      default: 256
    }
  },
  components: {
    FocusTrap,
    TransitionChild,
    TransitionRoot,
    MenuItems,
    Menu
  },

  data () {
    return {
      shown: false,
      pos: {
        e: undefined,
        top: 0,
        left: 0
      }
    }
  },

  watch: {
    shown () {
      if (this.shown) {
        window.addEventListener('keyup', this.keyUp)
      } else {
        window.removeEventListener('keyup', this.keyUp)
      }
    }
  },

  methods: {
    keyUp (e) {
      if (e.key === 'Escape' || e.key === 'Enter') {
        this.closeMenu()
      }
    },

    closeMenu () {
      this.shown = false
      this.$emit('close')
    },

    showMenu (e) {
      /* This is called by the parent component through $refs, passing in the event object to get the position. */

      this.pos.top = e.y
      this.pos.left = e.x

      // if the menu is too close to the right edge of the screen, move it to the left
      if (this.pos.left + this.width > window.innerWidth) {
        this.pos.left = window.innerWidth - this.width
      }
      this.shown = true

      // bit of a hack, need to render first to get height; presumably a
      // better way of doing this, hook on transition? but displays fine
      this.$nextTick(() => {
        // if the bottom fo this.$refs.menu.$el is greater than the window height, move it up
        // The 32 is the height of the closed status bar
        if (this.$refs.menu.$el.getBoundingClientRect().bottom > (window.innerHeight)) {
          // only pop up if there's space to pop it up
          if (e.y - this.$refs.menu.$el.clientHeight > 0) {
            this.pos.top = e.y - this.$refs.menu.$el.clientHeight
          }
        }
      })

    }
  }

}
</script>

<style scoped>

</style>
