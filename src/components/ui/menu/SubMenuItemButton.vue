<template>

  <menu-item
      v-slot="{ active }"
      as="template">
    <button
        ref="button"
        class="text-left group flex rounded-md items-center w-full px-2 py-2 text-sm"
        :class="activeClasses(active)">

      <span class="flex-1 text-left"><slot></slot></span>
      <span class="mr-auto -rotate-90">
          <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
          </svg>
      </span>
    </button>
  </menu-item>
</template>

<script>

import { MenuItem } from '@headlessui/vue'

export default {
  components: { MenuItem },
  emits: ['activated', 'deactivated'],
  props: {
    submenuActive: {
      type: Boolean,
      required: true,
      default: false
    }
  },
  mounted () {
    const button = this.$refs.button
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          const classList = mutation.target.classList
          if (classList.contains('active')) {
            this.$emit('activated')
          } else {
            this.$emit('deactivated')
          }
        }
      })
    })

    this.observer.observe(button, { attributes: true })
  },
  beforeUnmount () {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  
  methods: {
    activeClasses (active) {
      if (active) {
        return 'active bg-fuchsia-600 text-white'
      } else if (this.submenuActive) {
        return 'sub-active bg-fuchsia-600 text-white'
      }
      return 'text-gray-900'
    }
  }
}
</script>

<style scoped>

</style>
