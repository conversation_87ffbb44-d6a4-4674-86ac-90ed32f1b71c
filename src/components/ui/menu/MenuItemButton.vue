<template>

  <menu-item
      v-slot="{ active }"
      as="template">
    <button
        class="text-left group flex rounded-md items-center w-full px-2 py-2 text-sm cursor-pointer group"
        :class="{
          'active text-white bg-red-600': active && type === 'warning',
          'text-red-600': !active && type === 'warning',

          'active text-white bg-fuchsia-600': active && type !== 'warning',
          'text-gray-900': !active && type !== 'warning'
        }"
    >
      <component v-if="icon" :is="icon" class="size-4 mr-2"/>
      <slot></slot>
      <check-circle v-if="selected" class="size-4 ml-auto text-fuchsia-600 group-hover:text-white"/>
    </button>
  </menu-item>
</template>

<script>

import { MenuItem } from '@headlessui/vue'
import { CheckCircle } from 'lucide-vue-next'

export default {
  components: { MenuItem, CheckCircle },
  props: {
    icon: {
      type: Function,
      required: false
    },
    type: {
      type: String,
      required: false,
      default: 'default'
    },
    selected: {
      type: Boolean,
      required: false,
      default: false
    }
  }
}
</script>

<style scoped>

</style>
