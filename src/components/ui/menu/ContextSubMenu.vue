<template>
  <div ref="parent" class="submenu relative" @mouseenter="mouseEnter" @mouseleave="mouseLeave">
    <sub-menu-item-button
        @activated="activate"
        @deactivated="deactivate"
        :submenu-active="submenuActive || active || hovering"
        class="flex">{{ label }}
    </sub-menu-item-button>
    <div
        ref="submenu"
        class="z-10 bg-white divide-gray-100 font-normal rounded-md shadow-lg ring-1 ring-black/10 absolute divide-y w-48 focus:outline-hidden"
        :style="{ right, top, bottom, visibility }">
      <slot></slot>
    </div>
  </div>
</template>

<script>

import SubMenuItemButton from '@/components/ui/menu/SubMenuItemButton.vue'

export default {
  name: 'ContextSubMenu',
  components: { SubMenuItemButton },
  props: {
    label: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      right: 0,
      top: 0,
      hovering: false,
      active: false,
      submenuActive: false,
      bottom: 'auto'
    }
  },

  computed: {
    visibility () {
      if (this.hovering || this.active || this.submenuActive) {
        this.setPosition()
        return 'visible'
      }
      return 'hidden'
    }
  },

  mounted () {
    // using a mutation observer, listen for all buttons in the submenu for adding or removing 'active' class
    const submenu = this.$refs.submenu
    this.observer = new MutationObserver((mutations) => {
      // is there a single button in refs.submenu that has an 'active' class?
      this.submenuActive = Array.from(submenu.querySelectorAll('button')).some((button) => button.classList.contains('active'))
    })
    this.observer.observe(submenu, { attributes: true, subtree: true, childList: true })
  },
  beforeUnmount () {
    if (this.observer) {
      this.observer.disconnect()
    }
  },

  methods: {

    activate () {
      this.active = true
    },

    deactivate () {
      this.active = false
    },

    mouseEnter () {
      this.hovering = true
    },

    mouseLeave () {
      this.hovering = false
    },

    setPosition () {
      // how many pixels from the right edge of the screen is the parent element? pop left if neccessary
      const distanceToEdge = window.innerWidth - this.$refs.parent.getBoundingClientRect().right
      const submenuWidth = this.$refs.submenu.offsetWidth
      const parentWidth = this.$refs.parent.offsetWidth
      if (distanceToEdge < submenuWidth) {
        this.right = `${parentWidth}px`
      } else {
        this.right = `${-1 * submenuWidth}px`
      }

      // how many pixels from the bottom edge of the screen is the parent element? pop up if neccessary
      this.top = 0
      this.bottom = 'auto'
      const distanceToBottom = window.innerHeight - this.$refs.parent.getBoundingClientRect().bottom
      const submenuHeight = this.$refs.submenu.offsetHeight
      if (distanceToBottom < submenuHeight) {
        this.bottom = '0'
        this.top = 'auto'
      }
    }
  }
}
</script>
