<template>
  <div
      v-if="shown"
      class="fixed z-20 inset-0 overflow-y-auto"
      aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 sm:block sm:p-0">
      <div class="fixed inset-0"
           @click="closeMenu"
           @contextmenu.exact.prevent="closeMenu"
      ></div>
      <div>
        <focus-trap>
          <div
              ref="formMenu"
              class="fixed z-30 bg-white divide-y divide-gray-100 font-normal rounded-md shadow-lg ring-1 ring-black/10 focus:outline-hidden"
              :style="{
                  top: `${pos.top}px`,
                  left: `${pos.left}px`,
                  width: `${width}px`
                }">
            <slot></slot>
          </div>
        </focus-trap>
      </div>
    </div>
  </div>
</template>

<script>
import { FocusTrap, TransitionChild, TransitionRoot } from '@headlessui/vue'

export default {
  name: 'FormMenu',
  emits: ['close'],
  props: {
    width: {
      type: Number,
      default: 256
    }
  },
  components: {
    FocusTrap,
    TransitionChild,
    TransitionRoot
  },

  data () {
    return {
      shown: false,
      pos: {
        e: undefined,
        top: 0,
        left: 0
      }
    }
  },

  watch: {
    shown () {
      if (this.shown) {
        window.addEventListener('keyup', this.keyUp)
      } else {
        window.removeEventListener('keyup', this.keyUp)
      }
    }
  },

  methods: {
    closeMenu () {
      this.shown = false
      this.$emit('close')
    },

    keyUp (e) {
      if (e.key === 'Escape' || e.key === 'Enter') {
        this.closeMenu()
      }
    },

    showMenu (e) {
      /* This is called by the parent component through $refs, passing in the event object to get the position. */
      this.pos.top = e.y
      this.pos.left = e.x

      // if the menu is too close to the right edge of the screen, move it to the left
      if (this.pos.left + this.width > window.innerWidth) {
        this.pos.left = window.innerWidth - this.width
      }
      this.shown = true

      // bit of a hack, need to render first to get height; presumably a
      // better way of doing this, hook on transition? but displays fine
      this.$nextTick(() => {
        // if the bottom fo this.$refs.menu.$el is greater than the window height, move it up
        // The 32 is the height of the closed status bar
        if (this.$refs.formMenu.getBoundingClientRect().bottom > (window.innerHeight)) {
          // only pop up if there's space to pop it up
          if (e.y - this.$refs.formMenu.clientHeight > 0) {
            this.pos.top = e.y - this.$refs.formMenu.clientHeight
          }
        }
      })
    }
  }

}
</script>

<style scoped>

</style>
