import { beforeEach, describe, expect, it, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import ToastNotifications from './ToastNotifications.vue'
import { useNotificationStore } from '@/stores/notifications.js'

describe('ToastNotifications.vue', () => {
  let wrapper
  let store
  
  beforeEach(() => {
    // Create a testing pinia instance
    const pinia = createTestingPinia({
      createSpy: vi.fn,
      stubActions: false
    })
    
    // Mount the component with the testing pinia
    wrapper = mount(ToastNotifications, {
      global: {
        plugins: [pinia],
        stubs: {
          transition: false, // Disable transition stubs if needed
          'loading-spinner': true, // Stub the loading spinner component
          'check-circle-icon': true,
          'x-circle-icon': true,
          'exclamation-triangle-icon': true,
          'plus-circle-icon': true,
          'information-circle-icon': true,
          'x-mark-icon': true
        }
      },
      attachTo: document.createElement('div') // Mock DOM attachment
    })
    
    // Get the notification store
    store = useNotificationStore()
  })
  
  it('renders correctly with no notifications', () => {
    // Check that no notifications are rendered initially
    expect(wrapper.findAll('[data-test="notification"]').length).toBe(0)
  })
  
  it('renders a notification when one is added', async () => {
    // Add a notification to the store
    store.add({
      id: 'test-1',
      message: 'Test notification',
      type: 'info',
      dismissible: true
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if the notification is rendered
    const notification = wrapper.find('[data-test="notification"]')
    expect(notification.exists()).toBe(true)
    expect(notification.text()).toContain('Test notification')
    
    // Check if the dismiss button is rendered
    expect(wrapper.find('button[aria-label="Close"]').exists()).toBe(true)
  })
  
  it('renders multiple notifications', async () => {
    // Add multiple notifications
    store.add({
      id: 'test-1',
      message: 'First notification',
      type: 'info'
    })
    
    store.add({
      id: 'test-2',
      message: 'Second notification',
      type: 'success'
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if both notifications are rendered
    const notifications = wrapper.findAll('[data-test="notification"]')
    expect(notifications.length).toBe(2)
    expect(notifications[0].text()).toContain('First notification')
    expect(notifications[1].text()).toContain('Second notification')
  })
  
  it('applies the correct CSS class based on notification type', async () => {
    // Add notifications with different types
    store.add({
      id: 'test-success',
      message: 'Success message',
      type: 'success'
    })
    
    store.add({
      id: 'test-error',
      message: 'Error message',
      type: 'error'
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if the correct CSS classes are applied
    const notifications = wrapper.findAll('[data-test="notification"]')
    expect(notifications[0].classes()).toContain('bg-green-50')
    expect(notifications[1].classes()).toContain('bg-red-50')
  })
  
  it('calls dismiss method when close button is clicked', async () => {
    // Add a dismissible notification
    store.add({
      id: 'test-dismiss',
      message: 'Dismissible notification',
      type: 'info',
      dismissible: true
    })
    
    await wrapper.vm.$nextTick()
    
    // Spy on the remove method
    const removeSpy = vi.spyOn(store, 'remove')
    
    // Click the dismiss button
    await wrapper.find('button[aria-label="Close"]').trigger('click')
    
    // Check if the remove method was called with the correct ID
    expect(removeSpy).toHaveBeenCalledWith('test-dismiss')
  })
  
  it('renders action buttons when provided', async () => {
    // Add a notification with actions
    store.add({
      id: 'test-actions',
      message: 'Notification with actions',
      type: 'info',
      actions: [
        { label: 'Confirm', handler: vi.fn(), dismissAfter: true },
        { label: 'Cancel', handler: vi.fn() }
      ]
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if action buttons are rendered
    const actionButtons = wrapper.findAll('[data-test="action-button"]')
    expect(actionButtons.length).toBe(2)
    expect(actionButtons[0].text()).toBe('Confirm')
    expect(actionButtons[1].text()).toBe('Cancel')
  })
  
  it('calls action handler when action button is clicked', async () => {
    // Create a mock handler
    const mockHandler = vi.fn()
    
    // Add a notification with an action
    store.add({
      id: 'test-action-handler',
      message: 'Notification with action',
      type: 'info',
      actions: [
        { label: 'Test Action', handler: mockHandler }
      ]
    })
    
    await wrapper.vm.$nextTick()
    
    // Click the action button
    await wrapper.find('[data-test="action-button"]').trigger('click')
    
    // Check if the handler was called
    expect(mockHandler).toHaveBeenCalled()
  })
  
  it('shows a loading spinner for processing notifications', async () => {
    // Add a processing notification
    store.add({
      id: 'test-processing',
      message: 'Processing task',
      status: 'processing',
      type: 'info'
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if the loading spinner is rendered
    expect(wrapper.find('[data-test="loading-spinner"]').exists()).toBe(true)
  })
  
  it('shows details when provided', async () => {
    // Add a notification with details
    store.add({
      id: 'test-details',
      message: 'Main message',
      details: 'Additional details',
      type: 'info'
    })
    
    await wrapper.vm.$nextTick()
    
    // Check if the details are rendered
    const notification = wrapper.find('[data-test="notification"]')
    expect(notification.text()).toContain('Additional details')
  })
})
