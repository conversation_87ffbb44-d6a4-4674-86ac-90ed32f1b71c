import { beforeEach, describe, expect, it, vi } from 'vitest'
import { DEFAULT_CHUNK_SIZE, DEFAULT_POSITION_CHUNK_SIZE, positionUpdateQueries, rowUpdateQueries } from './update.js'
import * as helpers from '@/utils/helpers.js'

// Mock dependencies
vi.mock('@/utils/formats.js', () => ({
  convertValueForDb: (value) => value,
  convertRawValueForDb: (value) => value
}))

vi.mock('@/db/types.js', () => ({
  typeMap: {
    text: 'TEXT',
    number: 'NUMERIC',
    date: 'DATE'
  }
}))

describe('rowUpdateQueries', () => {
  beforeEach(() => {
    // Spy on processInChunks to verify it's called correctly
    vi.spyOn(helpers, 'processInChunks')
  })
  
  it('should return empty array for empty updates', () => {
    const result = rowUpdateQueries([])
    expect(result).toEqual([])
  })
  
  it('should chunk updates based on custom chunk size', () => {
    // Create 5 updates
    const updates = [
      { rowId: 1, cells: [{ columnId: 1, rawValue: 'a', cleanValue: 'a', type: 'text' }] },
      { rowId: 2, cells: [{ columnId: 1, rawValue: 'b', cleanValue: 'b', type: 'text' }] },
      { rowId: 3, cells: [{ columnId: 1, rawValue: 'c', cleanValue: 'c', type: 'text' }] },
      { rowId: 4, cells: [{ columnId: 1, rawValue: 'd', cleanValue: 'd', type: 'text' }] },
      { rowId: 5, cells: [{ columnId: 1, rawValue: 'e', cleanValue: 'e', type: 'text' }] }
    ]
    
    // Set chunk size to 3
    const result = rowUpdateQueries(updates, 3)
    
    // Verify processInChunks was called with the right parameters
    expect(helpers.processInChunks).toHaveBeenCalledWith(updates, expect.any(Function), 3)
    
    // Should generate 2 queries (3 updates in first chunk, 2 in second)
    expect(result.length).toBe(2)
    
    // First chunk should include rowIds 1, 2, 3
    expect(result[0].params).toContain(1)
    expect(result[0].params).toContain(2)
    expect(result[0].params).toContain(3)
    expect(result[0].params).not.toContain(4)
    
    // Second chunk should include rowIds 4, 5
    expect(result[1].params).toContain(4)
    expect(result[1].params).toContain(5)
    expect(result[1].params).not.toContain(1)
  })
  
  it('should calculate appropriate chunk size when not provided', () => {
    const updates = Array(10).fill().map((_, i) => ({
      rowId: i + 1,
      cells: [
        { columnId: 1, rawValue: `val${i}`, cleanValue: `val${i}`, type: 'text' },
        { columnId: 2, rawValue: i, cleanValue: i, type: 'number' }
      ]
    }))
    
    rowUpdateQueries(updates)
    
    // Verify a calculated chunk size was used (not the default)
    expect(helpers.processInChunks).not.toHaveBeenCalledWith(updates, expect.any(Function), DEFAULT_CHUNK_SIZE)
    
    // The calculated chunk size should be based on parameter count
    // With 2 cells, we expect 9 parameters per update (4*2 + 1), so MAX_PARAMETERS/9 should be the chunk size
    const calculatedChunkSize = Math.floor(1000 / 9)
    expect(helpers.processInChunks).toHaveBeenCalledWith(updates, expect.any(Function), calculatedChunkSize)
  })
  
  it('should generate correct SQL and parameters for updates', () => {
    const updates = [
      {
        rowId: 1,
        cells: [
          { columnId: 1, rawValue: 'a', cleanValue: 'a', type: 'text' },
          { columnId: 2, rawValue: '10', cleanValue: 10, type: 'number' }
        ]
      },
      {
        rowId: 2,
        cells: [
          { columnId: 1, rawValue: 'b', cleanValue: 'b', type: 'text' },
          { columnId: 2, rawValue: '20', cleanValue: 20, type: 'number' }
        ]
      }
    ]
    
    const result = rowUpdateQueries(updates, 2)
    
    // Should generate one query for the chunk
    expect(result.length).toBe(1)
    
    // SQL should contain CASE expressions for each column
    expect(result[0].sql).toContain('c1 = CASE')
    expect(result[0].sql).toContain('r1 = CASE')
    expect(result[0].sql).toContain('c2 = CASE')
    expect(result[0].sql).toContain('r2 = CASE')
    
    // SQL should have IN clause with row IDs
    expect(result[0].sql).toContain('WHERE id IN (?,?)')
    
    // Parameters should include all values in the right order
    const { params } = result[0]
    
    // For column 1, we should have clean values with CAST to TEXT
    const c1Index = result[0].sql.indexOf('c1 = CASE')
    expect(result[0].sql.substring(c1Index, c1Index + 50)).toContain('CAST(? AS TEXT)')
    
    // For column 2, we should have clean values with CAST to NUMERIC
    const c2Index = result[0].sql.indexOf('c2 = CASE')
    expect(result[0].sql.substring(c2Index, c2Index + 50)).toContain('CAST(? AS NUMERIC)')
    
    // Verify params contain the expected values for both columns
    expect(params).toContain(1)  // rowId 1
    expect(params).toContain(2)  // rowId 2
    expect(params).toContain('a')  // clean value for row 1, col 1
    expect(params).toContain('b')  // clean value for row 2, col 1
    expect(params).toContain(10)  // clean value for row 1, col 2
    expect(params).toContain(20)  // clean value for row 2, col 2
  })
})

describe('positionUpdateQueries', () => {
  beforeEach(() => {
    vi.spyOn(helpers, 'processInChunks')
  })
  
  it('should return empty array for empty position map', () => {
    expect(positionUpdateQueries({})).toEqual([])
    expect(positionUpdateQueries([])).toEqual([])
    expect(positionUpdateQueries(new Map())).toEqual([])
  })
  
  it('should chunk position updates based on DEFAULT_POSITION_CHUNK_SIZE when no custom size provided', () => {
    const positions = Object.fromEntries(Array(1000).fill().map((_, i) => [i + 1, i]))
    
    positionUpdateQueries(positions)
    
    expect(helpers.processInChunks).toHaveBeenCalledWith(
      expect.any(Array),
      expect.any(Function),
      DEFAULT_POSITION_CHUNK_SIZE
    )
  })
  
  it('should chunk position updates based on custom chunk size', () => {
    // Create 5 position updates
    const positions = {
      1: 10,
      2: 20,
      3: 30,
      4: 40,
      5: 50
    }
    
    // Set chunk size to 2
    const result = positionUpdateQueries(positions, 2)
    
    // Verify processInChunks was called with the right parameters
    expect(helpers.processInChunks).toHaveBeenCalledWith(
      expect.any(Array),
      expect.any(Function),
      2
    )
    
    // Should generate 3 queries (2 updates in first and second chunks, 1 in third)
    expect(result.length).toBe(3)
  })
  
  it('should accept different input formats for position updates', () => {
    // Object format
    const objectPositions = { 1: 10, 2: 20 }
    const objectResult = positionUpdateQueries(objectPositions, 10)
    
    // Map format
    const mapPositions = new Map([[1, 10], [2, 20]])
    const mapResult = positionUpdateQueries(mapPositions, 10)
    
    // Array format
    const arrayPositions = [[1, 10], [2, 20]]
    const arrayResult = positionUpdateQueries(arrayPositions, 10)
    
    // All should produce the same result
    expect(objectResult).toEqual(mapResult)
    expect(objectResult).toEqual(arrayResult)
    
    // Check the generated SQL has the correct format
    expect(objectResult[0].sql).toContain('UPDATE {table} SET pos = CASE')
    expect(objectResult[0].sql).toContain('WHERE id IN (?,?)')
    
    // Check the parameters
    expect(objectResult[0].params).toContain(1)
    expect(objectResult[0].params).toContain(2)
    expect(objectResult[0].params).toContain(10)
    expect(objectResult[0].params).toContain(20)
  })
})
