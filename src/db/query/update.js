import { convertRawValueForDb, convertValueForDb } from '@/utils/formats.js'
import { typeMap } from '@/db/types.js'
import { processInChunks } from '@/utils/helpers.js'

// Default chunk sizes - can be adjusted based on testing
export const DEFAULT_CHUNK_SIZE = 250
export const DEFAULT_POSITION_CHUNK_SIZE = 500

/**
 * Generate SQL query and parameters for updating a chunk of rows
 * @param {Array} updates - Chunk of update objects
 * @returns {Object} Object with SQL query and parameters
 * @private
 */
const generateChunkUpdateQuery = (updates) => {
  // Group updates by columnId to build CASE expressions
  const columnUpdates = {}
  updates.forEach(update => {
    update.cells.forEach(cell => {
      if (!columnUpdates[cell.columnId]) {
        columnUpdates[cell.columnId] = {
          clean: [],
          raw: [],
          type: cell.type
        }
      }
      columnUpdates[cell.columnId].clean.push([update.rowId, cell.cleanValue])
      columnUpdates[cell.columnId].raw.push([update.rowId, cell.rawValue])
    })
  })
  
  // Build CASE expressions for each column
  const setClauses = []
  const values = []
  
  Object.entries(columnUpdates).forEach(([columnId, { clean, raw, type }]) => {
    const dbType = typeMap[type]
    // Handle null values by casting them to the correct type
    const cleanCases = clean.map(_ => `WHEN id = ? THEN CAST(? AS ${dbType})`).join(' ')
    const rawCases = raw.map(_ => `WHEN id = ? THEN ?`).join(' ')
    
    setClauses.push(
      `c${columnId} = CASE ${cleanCases} ELSE c${columnId} END`,
      `r${columnId} = CASE ${rawCases} ELSE r${columnId} END`
    )
    
    // Add values for clean cases
    values.push(...clean.flatMap(([rowId, value]) => [
      Number(rowId),
      convertValueForDb(value, type)
    ]))
    
    // Add values for raw cases
    values.push(...raw.flatMap(([rowId, value]) => [
      Number(rowId),
      convertRawValueForDb(value)
    ]))
  })
  
  const rowIds = [...new Set(updates.map(u => u.rowId))]
  const updateSQL = `UPDATE {table} SET ${setClauses.join(', ')} WHERE id IN (${rowIds.map(() => '?').join(',')})`
  
  return {
    sql: updateSQL,
    params: [...values, ...rowIds]
  }
}

/**
 * Generate SQL queries and parameters for updating rows
 * @param {Array} updates - Array of update objects with format: [{
 *   rowId: number,
 *   cells: [{ columnId, rawValue, cleanValue, type }]
 * }]
 * @param {number} [chunkSize] - Optional custom chunk size
 * @returns {Array<Object>} Array of objects with SQL queries and parameters
 */
export const rowUpdateQueries = (updates, chunkSize) => {
  if (updates.length === 0) return []
  
  // Calculate default chunk size if not provided
  if (!chunkSize) {
    const MAX_PARAMETERS = 1000 // Conservative limit for SQL parameters
    const PARAMETERS_PER_UPDATE = updates[0].cells.length * 4 + 1 // 4 params per cell (2 for CASE, 2 for values) + 1 for WHERE IN
    chunkSize = Math.floor(MAX_PARAMETERS / PARAMETERS_PER_UPDATE)
  }
  
  return processInChunks(updates, chunk => generateChunkUpdateQuery(chunk), chunkSize)
}

/**
 * Generate SQL query and parameters for updating positions of a chunk of rows
 * @param {Array} positionUpdates - Array of [id, position] pairs
 * @returns {Object} Object with SQL query and parameters
 * @private
 */
const generatePositionUpdateQuery = (positionUpdates) => {
  // Build CASE expression for position updates
  const caseStatements = positionUpdates.map(() => 'WHEN id = ? THEN ?').join(' ')
  const values = positionUpdates.flatMap(([id, pos]) => [Number(id), Number(pos)])
  
  // Get all row IDs for the WHERE IN clause
  const rowIds = positionUpdates.map(([id]) => Number(id))
  
  const updateSQL = `UPDATE {table} SET pos = CASE ${caseStatements} ELSE pos END WHERE id IN (${rowIds.map(() => '?').join(',')})`
  
  return {
    sql: updateSQL,
    params: [...values, ...rowIds]
  }
}

/**
 * Generate SQL queries and parameters for updating row positions
 * @param {Object|Map|Array} positionMap - Map or object with row IDs as keys and new positions as values,
 *                                          or array of [id, position] pairs
 * @param {number} [chunkSize=DEFAULT_POSITION_CHUNK_SIZE] - Optional custom chunk size
 * @returns {Array<Object>} Array of objects with SQL queries and parameters
 */
export const positionUpdateQueries = (positionMap, chunkSize = DEFAULT_POSITION_CHUNK_SIZE) => {
  // Convert position map to array of [id, position] pairs if needed
  let positionPairs = Array.isArray(positionMap)
                      ? positionMap
                      : (positionMap instanceof Map
                         ? Array.from(positionMap.entries())
                         : Object.entries(positionMap))
  
  if (positionPairs.length === 0) return []
  
  return processInChunks(
    positionPairs,
    chunk => generatePositionUpdateQuery(chunk),
    chunkSize
  )
}

export const refreshRowPositionQuery = () => {
  return `UPDATE {table} SET pos = new_pos - 1 FROM (
    SELECT id, ROW_NUMBER() OVER (ORDER BY pos) AS new_pos
    FROM {table}
  ) AS numbered WHERE {table}.id = numbered.id`
  
}
