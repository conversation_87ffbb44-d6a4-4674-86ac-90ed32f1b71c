/**
 * Converts an array of columns with filters into a SQL WHERE clause with hardcoded values
 * @param {Array} columns - Array of column objects potentially containing filter arrays
 * @returns {string} SQL WHERE clause with hardcoded values
 */
import {
  addDays,
  addMonths,
  addQuarters,
  addWeeks,
  addYears,
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
  subDays,
  subMonths,
  subQuarters,
  subWeeks,
  subYears
} from 'date-fns'
import { UTCDate } from '@date-fns/utc'

/**
 * Safely escapes string values for SQL
 * @param {string} value - String value to escape
 * @returns {string} Escaped string
 */
function escapeString (value) {
  if (typeof value !== 'string') return value
  // Replace single quotes with doubled single quotes to escape them in SQL
  return value.replace(/'/g, '\'\'')
}

/**
 * Safely formats value based on its type
 * @param {*} value - Value to format
 * @param {string} colType - Column type
 * @returns {string} Formatted value for SQL
 */
function formatValue (value, colType) {
  if (value === null || value === undefined) {
    return 'NULL'
    
  } else if (colType === 'text') {
    return `'${escapeString(value)}'`
    
  } else if (colType === 'datetime') {
    return `epoch_ms(${value})`
    
  } else if (typeof value === 'boolean') {
    return value ? 'TRUE' : 'FALSE'
    
  } else {
    // All numeric types are actually strings values, compared against decimals or varchars
    return String(value)
  }
  
}

/**
 * Processes a single filter and returns the appropriate SQL condition
 * @param {Object} column - The column object
 * @param {Object} filter - The filter to process
 * @returns {string} SQL condition for this filter
 */
function buildColumnFilter (column, filter) {
  const columnId = `c${column.id}`
  
  // Some queries work on the raw, but if its a calculated column, there's no such thing
  // as rawId - so we just use columnId in those cases. E.g. "not blank" on a moving avg
  // nb 'has error' doesn't make sense in a calc column tho right now we don't prevent it in UI
  const rawId = column.calc ? columnId : `r${column.id}`
  const operator = filter.operator
  
  // An error column is defined as one with a clean value of null, but a raw value with something
  if (operator === 'error') {
    return `${columnId} IS NULL AND ${rawId} IS NOT NULL`
  }
  if (operator === 'not_error') {
    return `NOT (${columnId} IS NULL AND ${rawId} IS NOT NULL)`
  }
  
  // Whereas a blank value is one that has no raw value
  if (operator === 'blank') {
    return `${rawId} IS NULL`
  }
  
  if (operator === 'not_blank') {
    return `${rawId} IS NOT NULL`
  }
  
  // Handle date-specific operators
  if (column.type === 'datetime') {
    if (operator.startsWith('this_') ||
      operator.startsWith('previous_') ||
      operator.startsWith('next_')) {
      return processDateRangeFilter(columnId, operator)
    }
    
    if (operator === 'in_last' || operator === 'not_in_last' ||
      operator === 'in_next' || operator === 'not_in_next') {
      return processRelativeDateFilter(columnId, operator, filter.value)
    }
  }
  
  /* Special string cases with ILIKE */
  switch (operator) {
    case 'contains': {
      const escapedValue = escapeString(filter.value)
      return `${columnId} ILIKE '%${escapedValue}%'`
    }
    
    case 'not_contains': {
      const escapedValue = escapeString(filter.value)
      return `${columnId} NOT ILIKE '%${escapedValue}%'`
    }
    
    case 'begins_with': {
      const escapedValue = escapeString(filter.value)
      return `${columnId} ILIKE '${escapedValue}%'`
    }
    
    case 'ends_with': {
      // Collations could handle accents and case-insensitivity; apparently
      // this change has been merged https://github.com/duckdb/duckdb/pull/14717 but not sure if
      // in duckdb-wasm - it didn't error but didn't work (col COLLATE NOACCENT.NOCASE LIKE... or SIMILAR TO...)
      const escapedValue = escapeString(filter.value)
      return `${columnId} ILIKE '%${escapedValue}'`
    }
  }
  
  // Otherwise an array of all values formatted for a query
  const preppedValues = Array.isArray(filter.value)
                        ? filter.value.map(v => formatValue(v, column.type))
                        : [formatValue(filter.value, column.type)]
  
  // Handle standard operators
  switch (operator) {
    case 'equals':
      return `${columnId} = ${preppedValues[0]}`
    
    case 'not_equals':
      // includes null values https://duckdb.org/docs/stable/sql/expressions/comparison_operators.html
      return `${columnId} IS DISTINCT FROM ${preppedValues[0]}`
    
    case 'greater_than':
      return `${columnId} > ${preppedValues[0]}`
    
    case 'greater_than_or_equals':
      return `${columnId} >= ${preppedValues[0]}`
    
    case 'less_than':
      return `${columnId} < ${preppedValues[0]}`
    
    case 'less_than_or_equals':
      return `${columnId} <= ${preppedValues[0]}`
    
    // Array methods
    case 'between': {
      return `${columnId} BETWEEN ${preppedValues[0]} AND ${preppedValues[1]}`
    }
    
    case 'not_between': {
      return `${columnId} NOT BETWEEN ${preppedValues[0]} AND ${preppedValues[1]}`
    }
    
    // List methods
    case 'in': {
      return `${columnId} IN (${preppedValues.join(', ')})`
    }
    
    case 'not_in': {
      return `${columnId} NOT IN (${preppedValues.join(', ')})`
    }
    
    default:
      return undefined // Unsupported operator
  }
}

/**
 * Process date range filters like this_month, previous_year, etc.
 * @param {string} columnId - Column identifier
 * @param {string} operator - Date range operator
 * @returns {string} SQL condition
 */
function processDateRangeFilter (columnId, operator) {
  // Extract the time period from the operator
  const [timeDirection, timePeriod] = operator.split('_')
  
  // Current time in UTC
  const now = new UTCDate()
  let startDate, endDate
  
  // Calculate the date range based on the period
  switch (timePeriod) {
    case 'week':
      // Start and end of current week (using Sunday as first day)
      startDate = startOfWeek(now, { weekStartsOn: 0 })
      endDate = endOfWeek(now, { weekStartsOn: 0 })
      break
    
    case 'month':
      // Start and end of current month
      startDate = startOfMonth(now)
      endDate = endOfMonth(now)
      break
    
    case 'quarter':
      // Start and end of current quarter
      startDate = startOfQuarter(now)
      endDate = endOfQuarter(now)
      break
    
    case 'year':
      // Start and end of current year
      startDate = startOfYear(now)
      endDate = endOfYear(now)
      break
    
    default:
      return null // Unsupported period
  }
  
  // Adjust for previous or next periods
  if (timeDirection === 'previous') {
    if (timePeriod === 'week') {
      startDate = subWeeks(startDate, 1)
      endDate = subWeeks(endDate, 1)
    } else if (timePeriod === 'month') {
      startDate = subMonths(startDate, 1)
      endDate = subMonths(endDate, 1)
    } else if (timePeriod === 'quarter') {
      startDate = subQuarters(startDate, 1)
      endDate = subQuarters(endDate, 1)
    } else if (timePeriod === 'year') {
      startDate = subYears(startDate, 1)
      endDate = subYears(endDate, 1)
    }
  } else if (timeDirection === 'next') {
    if (timePeriod === 'week') {
      startDate = addWeeks(startDate, 1)
      endDate = addWeeks(endDate, 1)
    } else if (timePeriod === 'month') {
      startDate = addMonths(startDate, 1)
      endDate = addMonths(endDate, 1)
    } else if (timePeriod === 'quarter') {
      startDate = addQuarters(startDate, 1)
      endDate = addQuarters(endDate, 1)
    } else if (timePeriod === 'year') {
      startDate = addYears(startDate, 1)
      endDate = addYears(endDate, 1)
    }
  }
  
  // Convert to milliseconds timestamp (what we store in DuckDB)
  const startTimestamp = startDate.getTime()
  const endTimestamp = endDate.getTime()
  
  return `${columnId} BETWEEN ${startTimestamp} AND ${endTimestamp}`
}

/**
 * Process relative date filters like in_last_30_days
 * @param {string} columnId - Column identifier
 * @param {string} operator - Relative date operator
 * @param {Object} value - Object containing amount and unit
 * @returns {string} SQL condition
 */
function processRelativeDateFilter (columnId, operator, value) {
  if (!value || !value.amount || !value.unit) {
    return null
  }
  
  const { amount, unit } = value
  const now = new UTCDate()
  let comparisonDate
  
  // Calculate the comparison date
  switch (unit) {
    case 'days':
      comparisonDate = subDays(now, amount)
      break
    
    case 'weeks':
      comparisonDate = subWeeks(now, amount)
      break
    
    case 'months':
      comparisonDate = subMonths(now, amount)
      break
    
    case 'quarters':
      comparisonDate = subQuarters(now, amount)
      break
    
    case 'years':
      comparisonDate = subYears(now, amount)
      break
    
    default:
      return null // Unsupported unit
  }
  
  // Convert to timestamps
  const nowTimestamp = now.getTime()
  const comparisonTimestamp = comparisonDate.getTime()
  
  // Swap the dates for in_next operations
  let startTimestamp, endTimestamp
  if (operator.startsWith('in_next')) {
    // For "in_next", we're looking forward from now
    startTimestamp = nowTimestamp
    // Use the addX functions instead
    switch (unit) {
      case 'days':
        endTimestamp = addDays(now, amount).getTime()
        break
      case 'weeks':
        endTimestamp = addWeeks(now, amount).getTime()
        break
      case 'months':
        endTimestamp = addMonths(now, amount).getTime()
        break
      case 'quarters':
        endTimestamp = addQuarters(now, amount).getTime()
        break
      case 'years':
        endTimestamp = addYears(now, amount).getTime()
        break
    }
  } else {
    // For "in_last", we're looking backward from now
    startTimestamp = comparisonTimestamp
    endTimestamp = nowTimestamp
  }
  
  // Create the SQL condition
  if (operator === 'in_last' || operator === 'in_next') {
    return `${columnId} BETWEEN ${startTimestamp} AND ${endTimestamp}`
  } else {
    return `(${columnId} < ${startTimestamp} OR ${columnId} > ${endTimestamp})`
  }
}

const rangeFilter = (col, fromTo) => {
  
  // if fromTo is a 2-length array, use that as the between
  if (Array.isArray(fromTo) && fromTo.length === 2) {
    fromTo = {
      from: fromTo[0],
      to: fromTo[1]
    }
  }
  
  // Check if fromTo is valid and has at least one defined property
  if (!fromTo || (fromTo.from === undefined && fromTo.to === undefined)) {
    return undefined
  }
  
  if (fromTo.from !== undefined && fromTo.to !== undefined) {
    return `${col} BETWEEN ${fromTo.from} AND ${fromTo.to}`
  }
  if (fromTo.from !== undefined) {
    return `${col} >= ${fromTo.from}`
  }
  if (fromTo.to !== undefined) {
    return `${col} <= ${fromTo.to}`
  }
}

export const getRowIdFilter = (rowIds) => {
  if (rowIds) {
    if (rowIds.length) {
      return `id IN (${rowIds.join(', ')})`
    }
    return `id = null`
  }
  return ''
}

export const getPositionalFilter = ({ posList, posRange } = {}) => {
  if (posList && posList.length) {
    return `WHERE pos IN (${posList.join(', ')})`
  } else if (posRange && Object.keys(posRange).length > 0) {
    const range = rangeFilter('pos', posRange)
    if (!range) return ''
    return 'WHERE ' + rangeFilter('pos', posRange)
  }
  return ''
}

export function buildColumnFilters (column, filters = []) {
  if (!column || filters.length === 0) return []
  return filters.map(filter => buildColumnFilter(column, filter)).filter(Boolean)
}

const getColumnFilterSql = (columnFilters = []) => {
  // Within one column, we wrap all clauses in an OR; with multiple columns, we wrap each in an AND
  return columnFilters.map(filters => filters.length ? '(' + filters.join(' OR ') + ')' : '').filter(Boolean).join(' AND ')
}

export const getFilterSql = ({ where, columns, rowIds } = {}) => {
  const filters = [
    where,
    getColumnFilterSql(columns),
    getRowIdFilter(rowIds)
  ].filter(f => f)
  return filters.length ? `WHERE ${filters.join(' AND ')}` : ''
}
