import { describe, expect, it } from 'vitest'

import { __test__ } from '@/db/query/utils.js'

const colMap = {
  '0': {
    'id': 0,
    'type': 'datetime'
  },
  
  // sum raw numbers
  '1': {
    'id': 1,
    'type': 'number'
  },
  '2': {
    'id': 2,
    'type': 'number'
  },
  '3': {
    'id': 2,
    'type': 'number'
  },
  
  '4': {
    'id': 2,
    'type': 'number'
  },
  
  // Valid non-window sum calculation on the above
  '5': {
    'id': 5,
    'calc': {
      'type': 'sum',
      'sourceCols': [1, 2, 3, 4]
    },
    'type': 'number'
  },
  
  // valid window function on the sum above
  '6': {
    'id': 6,
    'calc': {
      'type': 'cumulative',
      'sourceCols': [5]
    },
    'type': 'number'
  },
  
  // valid (if weird) calc on the window function above
  '7': {
    'id': 7,
    'calc': {
      'type': 'sum',
      'sourceCols': [1, 6]
    },
    'type': 'number'
  },
  
  // invalid window function on a window function
  '8': {
    'id': 8,
    'calc': {
      'type': 'cumulative',
      'sourceCols': [6]
    },
    'type': 'number'
  },
  
  // invalid window function on 7, a non-window function that has a window function in its chain
  '9': {
    'id': 9,
    'calc': {
      'type': 'cumulative',
      'sourceCols': [7]
    },
    'type': 'number'
  }
}

describe('colHasWindowFunctionInChain', () => {
  it('should return true if a column has a window function in its chain', () => {
    expect(__test__.countWindowCalcsInChain(colMap['5'].calc, colMap)).toBe(0)
    expect(__test__.countWindowCalcsInChain(colMap['6'].calc, colMap)).toBe(1)
    expect(__test__.countWindowCalcsInChain(colMap['7'].calc, colMap)).toBe(1)
    expect(__test__.countWindowCalcsInChain(colMap['8'].calc, colMap)).toBe(2)
    expect(__test__.countWindowCalcsInChain(colMap['9'].calc, colMap)).toBe(2)
  })
  
})
