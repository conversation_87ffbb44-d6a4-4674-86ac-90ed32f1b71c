import { calculatedColumns } from '@/utils/calcs.js'
import { getFilterSql, getPositionalFilterSql } from '@/db/query/filters.js'
import { aggregates } from '@/utils/aggregation.js'
import { dateGrouping } from '@/db/grouping.js'

// Special case columns we can select/aggregate/sort on
const specialCols = ['*', 'id', 'pos']

/**
 * Converts a column ID to the appropriate field reference in SQL
 */
const colIdToField = (colId, aliases, grouped = false) => {
  if (specialCols.includes(colId)) return colId
  if (grouped) return `g${colId}`
  return `c${aliases[colId] ?? colId}`
}

/**
 Given a sorting definition, return the SQL for sorting.
 */
const getSortSql = (sorting, aliases, groupIds) => {
  // always return with consistent ordering
  if (!sorting?.length) {
    if (groupIds.length) return ''
    return 'ORDER BY pos'
  }
  const colSort = ({ id, desc }) => `${colIdToField(id, aliases, groupIds.includes(id))} ${desc ? 'DESC' : 'ASC'}`
  return `ORDER BY ${sorting.map(colSort).join(', ')}`
}

const summaryValueSql = (columns) => {
  return columns.map(col => {
    if (!col.valid) {
      return `NULL AS c${col.id}` // This is set in the querySpec, see viewToCols
    }
    const summary = aggregates[col.summarize].query(`c${col.sourceColumnId}`)
    return `${summary} AS c${col.id}`
  }).join(', ')
}

const prepGroupBy = (groupBy) => {
  return groupBy.map(g => `g${g.id}`).join(', ')
}

const prepGroupValuesSql = (groupBy) => {
  if (groupBy === undefined) return ''
  const groupBySql = ({ id, valid, sourceColumnId, by: groupBy }) => {
    if (!valid) return `NULL AS g${id}`
    if (!groupBy) return `c${sourceColumnId} as g${id}`
    const groupSql = dateGrouping[groupBy].query(`c${sourceColumnId}`)
    return `${groupSql} as g${id}`
  }
  return groupBy.map(groupBySql).join(', ')
}

const prepAggValuesSql = (values, aliases, groupIds) => {
  const aggSql = ({ id, aggregate }) => {
    // get an aggregate query, aliased to the name of the aggregate.
    // e.g. `MAX(c1) AS max`
    const field = colIdToField(id, aliases, groupIds.includes(id))
    const agg = aggregates[aggregate].query(field)
    return `${agg} AS ${aggregate}`
  }
  return values.map(aggSql).join(',  ')
}

const prepValuesSql = (colIds, columns, aliases, skipRaws, groupIds) => {
  
  // Prefixed `{col} AS {alias}`
  const aliasedValueSql = (prefix, colId, origId) => `${prefix}${origId} as ${prefix}${colId}`
  
  // Get prefixed value, aliased if origId provided
  const valueSql = (prefix, colId, origId) => origId !== undefined ? aliasedValueSql(prefix, colId, origId) : `${prefix}${colId}`
  
  // Get clean and raw values, if doRaw; does aliasing if origId
  const selectBothSql = (colId, origId, doRaw) => `${valueSql('c', colId, origId)}${doRaw ? `, ${valueSql('r', colId, origId)}` : ''}`
  
  const prepColId = (colId) => {
    // For grouped columns, this was created with a g prefix to avoid clashes; on the final
    // select, map back to a c for consistency
    if (groupIds.includes(colId)) {
      return `g${colId} AS c${colId}`
    }
    const origColId = aliases[colId]
    const colIsCalc = columns.find(c => c.id === (origColId ?? colId))?.calc
    const doRaw = (!skipRaws & !colIsCalc) // Don't do raw if this is an underlying calc
    return selectBothSql(colId, origColId, doRaw)
  }
  return 'id, pos, ' + colIds.map(prepColId).join(', ')
}

export const baseQuery = (columns, skipRaws = false, includePos = true) => {
  /*
  Create a base SELECT query that gets all values from {table}.
  
  This handles calculations in order, but does no sorting, filtering, grouping etc.
  Subsequent build on this by wrapping it and selecting on it - basically a view, but don't ad hoc.
   */
  
  // This gets all columns for the initial select (r and c) - we could improve this to only get columns that
  //   are needed, but we'd need to check calculations, filtering, sorting, and grouping to get a full list of
  //   valid calcs
  const allValues = columns.map(col => {
    if (!col.valid) {
      return `NULL AS c${col.id}`
      
    } else if (col.calc) {
      const calc = calculatedColumns[col.calc.type].column(col.calc.sourceCols, col.calc.data)
      return `${calc} AS c${col.id}`
    }
    
    // Return both clean and raw values for non-calculated columns
    const raw = skipRaws ? '' : `, r${col.id}`
    const clean = col.date_trunc
                  ? `date_trunc('${col.date_trunc}', c${col.id}) AS c${col.id}`
                  : `c${col.id}`
    return clean + raw
  })
  return `SELECT id, ${includePos ? 'pos, ' : ''} ${allValues.join(', ')} FROM {table} ORDER BY pos`
}

/**
 * Build a CTE query from an array of [name, query] pairs and a final select query.
 */
const cteQuery = (cteArray, finalSelect) => {
  cteArray = cteArray.filter(Boolean)
  if (!cteArray.length) return ''
  
  // Build CTEs with table substitution
  let lastTable = '{table}'
  const cteSql = cteArray.map(([name, query]) => {
    const cte = query.replace('{table}', lastTable)
    lastTable = name
    return `${name} AS (${cte})`
  }).join(',\n')
  
  finalSelect = finalSelect.replace('{table}', lastTable)
  return `WITH ${cteSql}\n${finalSelect}`
}

export const selectQuery = (
  // Query spec
  {
    // Contains a map of alias id: sourceColumnId for building aliases in the select. This is static and includes all
    // possible lookups; one specific select may or may not use them.
    aliases = {},
    
    // A list of table columns, sorted by calculation order.
    columns = [],
    
    // If this is set to true, then we don't get the pos in teh base query, but rather build it dynamically in the final select.
    // This gives you a pos that's always 0-based after any filtering.
    dynamicPos = false,
    
    grouping: {
      // An array of grouping definitions. Grouping should be an object
      // in the format { id: Number, sourceColumnId: Number, by: string | undefined }.
      // `by` is an optional grouping method (currently only date extractors
      // are supported from dateGrouping)
      groups = [],
      
      // { id: Number, sourceColumnId: Number, summarize: string }.
      summaries = []
    } = {},
    
    // A filter definition. Supported: { where, columns, rowIds, posList, posRange, output: { limit, offset } }
    filtering = {},
    
    // A sort definition is an array of objects in the format { id: number, desc: boolean } where the field
    // is the output sql column to sort by, and desc is true for descending. Should be in order.
    sorting = []
    
  } = {},
  // Optional select definition, listing columns to return. If this is an array of objects, it assumes they are
  // aggregates in the format { id: string, aggregate: string }. Otherwise, assumes an array of col ids.
  select = []
) => {
  
  // Helper to build a CTE, but only if the boolean is true
  const cte = (name, query, boolean = true) => boolean ? [name, query] : ''
  
  // This gets the final values we select; this could be improved to only get the values that are needed
  // but right now unless we are doing aggregates, we just select * - which will get often way more than needed
  const isAggregateQuery = select.some(v => v.aggregate)
  const skipRaws = groups.length > 0
  const groupIds = groups.map(g => g.id)
  let preppedValues = select.length === 0 ? 'id, pos' : isAggregateQuery
                                                        ? prepAggValuesSql(select, aliases, groupIds)
                                                        : prepValuesSql(select, columns, aliases, skipRaws, groupIds)
  
  // If we are doing an aggregate query, we don't do ordering
  const orderSql = isAggregateQuery ? '' : getSortSql(sorting, aliases, groupIds)
  
  // Get the filtering parts
  const output = filtering.output || {}
  const limitSql = `${output.limit ? `LIMIT ${output.limit}` : ''} ${output.offset ? `OFFSET ${output.offset}` : ''}`
  
  if (!groups?.length) {
    const filterSql = getFilterSql(filtering)
    const posFilterSql = getPositionalFilterSql(filtering)
    
    /* Notes and gotchas:
     - OrderSql needs to come at the end so filtered view aliasing works - otherwise the sort occurs
       on the pre-aliased cols and will fail.
     - If the querySpec defines a dynamicPos, we add that in during filtering. We also apply it when
       we are doing posFiltering (i.e. specifically picking a range of pos). This gets a 0-based pos
       that matches the user's POV. Why don't we always do this? For filtered tables, better for row
       counts to be the original
     */
    dynamicPos = dynamicPos || posFilterSql
    return cteQuery([
      cte('original', baseQuery(columns, skipRaws, !dynamicPos)),
      cte('filtered', `SELECT * FROM {table} ${filterSql}`, filterSql),
      cte('numbered', `SELECT *, CAST(row_number() OVER () - 1 as INTEGER) AS pos FROM {table}`, dynamicPos)
    ], `SELECT ${preppedValues} FROM {table} ${posFilterSql} ${orderSql} ${limitSql}`)
    
  } else {
    // For all values that we want, we wrap them in their mandatory summarize and alias
    const groupValues = prepGroupValuesSql(groups)
    const summaryValues = summaryValueSql(summaries)
    const groupBySql = prepGroupBy(groups)
    const posFilterSql = getPositionalFilterSql(filtering)
    
    // We split filtering into two parts; one that applies to the grouped data, and one that applies
    // to the final output.
    const filterSql = getFilterSql({ columns: filtering.columns })
    const groupedFilterSql = getFilterSql({
      where: filtering.where, // TODO make 'where' more explicit imo? right now no rason why this would be post-grouping
      rowIds: filtering.rowIds
    })
    
    /* Notes and gotchas
     - Here we do ordering first, then we do numbering. Pos is then a 0-based count of post-filtered rows.
     - As this is a grouped view, `id` doesn't exist; we just duplicate the `pos` column.
     - Filtering below numbered - numbered will always
       match the user's POV. Honestly 'filtered' isn't needed if we combined the two WHERE clauses,
       but it's easier to read this way.
     */
    return cteQuery([
      cte('original', baseQuery(columns, skipRaws, false)),
      cte('filtered', `SELECT * FROM {table} ${filterSql}`, filterSql),
      cte('grouped', `SELECT ${groupValues}, ${summaryValues} FROM {table} GROUP BY ${groupBySql}`),  // < this is always called
      cte('ordered', `SELECT * FROM {table} ${orderSql}`, orderSql),
      cte('numbered', `SELECT *, cast(ROW_NUMBER() OVER () - 1 AS INTEGER) AS pos, pos as id FROM {table}`),
      cte('groupFiltered', `SELECT * FROM {table} ${groupedFilterSql}`, groupedFilterSql)
    ], `SELECT ${preppedValues} FROM {table} ${posFilterSql} ${limitSql}`)
  }
}

/* const pivotQuery = (params) => {
  return `PIVOT {table} ON ${params.on} USING ${params.using}
       ${params.groups ? `GROUP BY ${params.groups}` : ''}
       ${params.order ? `ORDER BY ${params.order}` : ''}
       ${params.limit ? `LIMIT ${params.limit}` : ''}
       ${params.offset ? `OFFSET ${params.offset}` : ''}`
} */

