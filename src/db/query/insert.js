const getColumnNames = (cols) => {
  return [
    'id',
    'pos',
    ...cols.map(col => `c${col.id}`),
    ...cols.map(col => `r${col.id}`)
  ]
}

export const bulkInsert = (cols, rows, values) => {
  const maxValuesPerInsert = 100   // we went up to 3000+ so this isn't a hard cap, but can't find it documented
  const maxRowsPerInsert = Math.floor(maxValuesPerInsert / cols.length)
  const columnNames = getColumnNames(cols)
  const getValuePlaceholders = (rows, colLength) => {
    // Returns an array of placeholders for the given number of columns -
    // which is 2x + 2 (raw and clean for each + id + pos)
    return rows.map(() => `(${Array((colLength * 2) + 2).fill('?').join(', ')})`)
  }
  
  const numRows = rows.length
  const numChunks = Math.ceil(numRows / maxRowsPerInsert)
  const queries = []
  for (let i = 0; i < numChunks; i++) {
    const start = i * maxRowsPerInsert
    const end = Math.min(start + maxRowsPerInsert, numRows)
    const chunkRows = rows.slice(start, end)
    const chunkValues = values.slice(start, end)
    
    const placeholders = getValuePlaceholders(chunkRows, cols.length)
    const flatValues = chunkValues.flat()
    const query = `INSERT INTO {table} (${columnNames.join(', ')}) VALUES ${placeholders.join(', ')};`
    queries.push({ sql: query, params: flatValues })
  }
  return queries
}

