import Big from 'big.js'
import { reprCol } from '@/utils/formats.js'

/**
 * Takes a duckdb results object and creates a tuple of [name, type]
 */
const resultSchemaToTuples = (result) => result.schema.fields.map(({ name, type }) => [name, type])

/**
 * Takes a duckdb results object and returns a map of name: type for each column
 */
const columnTypeMap = (result) => {
  return Object.fromEntries(resultSchemaToTuples(result))
}

/**
 * Takes a duckdb results object and returns a map of id: type for each numeric column, filtering out id and pos.
 */
const columnIdTypeMap = (result) => {
  return Object.fromEntries(
    resultSchemaToTuples(result)
      .filter(([name]) => name.startsWith('c'))
      .map(([name, type]) => ([name.slice(1), type]))
  )
}

/**
 * Processes a value from duckdb, taking a Numeric object (one with scale) and dividing it by its scale divisor.
 */
const processValue = (value, colType) => {
  if (value === null) return null
  if (colType.scale) return Big(value).div(Big(10).pow(colType.scale))
  return value
}

export const results = (result) => {
  return {
    raw () {
      return result.toArray()
    },
    
    grid (columnIds, { pickValue = false, cleanOnly = false } = {}) {
      const columnTypes = columnIdTypeMap(result)
      
      // console log the last column
      return result.toArray().map(row => {
        const rowObj = {
          id: Number(row.id),
          pos: Number(row.pos)
        }
        columnIds.forEach(colId => {
          
          const key = `c${colId}`
          const value = row[key]
          
          if (pickValue && (value === undefined || value === null) && row[`r${colId}`] !== undefined) {
            // If we're picking a value, we return the raw in the 'clean' field
            rowObj[key] = row[`r${colId}`]
          } else {
            // Otherwise, we return the clean value
            rowObj[key] = processValue(value, columnTypes[colId])
            
            /// And if we want raw as well, this gets added in
            if (!cleanOnly) {
              rowObj[`r${colId}`] = row[`r${colId}`]
            }
          }
        })
        return rowObj
      })
    },
    
    array (colIds, colTypes, reprValues = false) {
      /*
      Executes the query, returning the rows as an array.
      
      This is much simpler process than grid, as it's just an array of arrays. For each cell,
      it picks the "best" value (clean/group if exists, otherwise raw) and can optionally try to repr the val.
      */
      const columnTypes = columnIdTypeMap(result)
      return result.toArray().map(row => colIds.map(colId => {
        if (colId === 'id' || colId === 'pos') return row[colId]
        let val = row[`c${colId}`]
        if (val) {
          val = processValue(val, columnTypes[colId])
          if (!reprValues) return val
          return reprCol(val, colTypes[colId])
        }
        return row[`r${colId}`]
      }))
    },
    
    scalar () {
      /* Returns the first value of the first row with only basic processing. */
      const r = result.toArray()[0]
      const firstKey = Object.keys(r)[0]
      const firstVal = r[firstKey]
      const colType = columnTypeMap(result)[firstKey]
      return processValue(firstVal, colType)
    },
    
    row () {
      /* Returns the first row. */
      const r = result.toArray()
      const resultTypes = columnTypeMap(result)
      if (!r.length) return undefined
      const firstRow = r[0]
      // For each value in first row, map it to processValue and return the object
      return Object.fromEntries(Object.entries(firstRow).map(([key, value]) => [key, processValue(value, resultTypes[key])]))
    },
    
    console ({ cleanOnly = false, rawOnly = false } = {}) {
      const rows = result.toArray()
      const resultTypes = columnTypeMap(result)
      console.table(rows.map(row => Object
          .fromEntries(Object.entries(row)
            .filter(([key]) => {
              if (key.startsWith('r') && cleanOnly) return false
              // noinspection RedundantIfStatementJS
              if (key.startsWith('c') && rawOnly) return false
              return true
            })
            .map(([key, value]) => {
              const val = processValue(value, resultTypes[key])
              if (val instanceof Big) {
                return [key, val.toNumber()]
              }
              return [key, val]
            }))
        )
      )
    }
  }
}
