/*
Utils for querying
*/
import { buildColumnFilters } from '@/db/query/filters.js'
import { twoLevelMerge } from '@/utils/helpers.js'
import { dateGrouping } from '@/db/grouping.js'
import { aggregates } from '@/utils/aggregation.js'
import { calculatedColumns, cantCalcOnTypes, multiSourceCalcTypes, oneSourceCalcTypes } from '@/utils/calcs.js'
import { buildDependencyGraph, topologicalSort } from '@/utils/columngraph.js'
import { dateTypes } from '@/utils/dates.js'

const countWindowCalcsInChain = (calc, colMap) => {
  /*
  Count the number of window functions in this calculation's source chain.
  
  At most, we can have 1; so we should always guard against doing queries of 2 (or more!)
  This is also useful to check for _one_ to pre-emptively check about user creating a 2+ window function query.
  */
  if (!calc) return 0
  
  // If a calc in a chain is invalid, skip the check
  if (calculatedColumns[calc.type] === undefined) return 0
  
  // Recursively count window calculations for all source columns
  return calc.sourceCols
    .map(colId => colMap[colId]?.calc)
    .filter(Boolean) // Only process columns with calcs
    .reduce(
      // recursively call other calcs
      (total, sourceCalc) => total + countWindowCalcsInChain(sourceCalc, colMap),
      
      // Start on 1 or 0 depending on this calc
      calculatedColumns[calc.type].isWindow ? 1 : 0
    )
}

export const calcIsValid = (calc, colMap) => {
  /*
  Check the calculation `calc` is valid.
  
  This looks at all calc.sourceCols and makes sure they are valid inputs into
  the calculation calc.type.
   */
  
  // First, check calc is actually a valid type in our list
  return !(
    // Check calc is a valid type
    (calculatedColumns[calc.type] === undefined) ||
    
    // one source calcs must have only one source column
    (oneSourceCalcTypes.includes(calc.type) && calc.sourceCols.length !== 1) ||
    
    // multi source calcs must have at least two source columns
    (multiSourceCalcTypes.includes(calc.type) && calc.sourceCols.length < 2) ||
    
    // no source cols can be missing
    calc.sourceCols.some(colId => colMap[colId] === undefined) ||
    
    // no source cols can be text
    // nb the similar aggregation.js has limitsTo which restricts some to only certain types
    calc.sourceCols.some(colId => cantCalcOnTypes.includes(colMap[colId].type)) ||
    
    // no window-on-window calc action
    countWindowCalcsInChain(calc, colMap) > 1
  )
}

export const getSortColumns = (cols) => {
  const sortedCols = cols.filter(col => col.sort).sort((a, b) => a.sort.order - b.sort.order)
  return sortedCols.map(
    col => ({
      id: col.id,
      desc: col.sort.desc === true
    })
  )
}

const getAliasViewType = (sourceColumn, viewColumn) => {
  if (viewColumn.sourceColumnId === 'id') {
    // If we're doing a 'row count' summarize, it's just a count.
    return { type: 'number', props: {} }
    
  } else if (sourceColumn.type === 'datetime' && viewColumn.by) {
    // grouping by a datetime results in a string response unless specified in dateGrouping
    return {
      type: dateGrouping[viewColumn.by]?.type || 'text',
      props: {}
    }
    
  } else if (viewColumn.summarize) {
    // If column is a summarized column with a type, always use that
    const agg = aggregates[viewColumn.summarize]
    if (agg?.type) {
      return {
        type: agg.type,
        props: {}
      }
    }
  }
  
  return {
    type: sourceColumn.type,
    props: sourceColumn.props
  }
}

export const aggregateIsValid = (aggregateKey) => {
  return aggregates[aggregateKey] !== undefined
}

const summarizeIsValid = ({ summarize }, originalCol) => {
  /*
  Given a summarize, check if it is valid for the column's type.
 
  Returns false if summarize is not given, not a valid aggregate, or if it is pointing
  to a column type that is not permitted for this aggregation type.
  */
  if (!summarize) return false
  const agg = aggregates[summarize]
  if (!agg) return false
  return (!agg.limitTo || agg.limitTo.includes(originalCol.type))
}

const groupByIsValid = ({ sourceColumnId, by: groupBy }) => {
  /*
  Given a group by, check if it is valid for the column's type.
 
  Returns false if by is not given, not a valid group by, or if it is pointing
  to a column type that is not permitted for this group by type.
  */
  if (sourceColumnId === undefined) return false
  if (!groupBy) return true
  if (typeof groupBy !== 'string') return false
  // noinspection RedundantIfStatementJS
  if (!dateGrouping[groupBy]) return false
  return true
}

function viewToCols (apiCols, { groups = [], columns = [] }) {
  return [...groups, ...columns].map((viewCol, index) => {
    
    // Get the properties we want to inherit from the original column if not set in the view
    const originalCol = apiCols.find(c => c.id === viewCol.sourceColumnId) || {}
    
    const { title, width } = originalCol
    
    // Validate view column definitions
    const isGroup = index < groups.length
    const inGroupedView = groups.length > 0
    const valid = isGroup
                  ? groupByIsValid(viewCol)
                  : summarizeIsValid(viewCol, originalCol)
    
    // Set the type and props for the view column. Delete 'summarize' if we shouldn't
    // be summarizing or getAliasViewType can be wrong
    if (!inGroupedView && viewCol.summarize) delete viewCol.summarize
    const { type, props } = getAliasViewType(originalCol, viewCol)
    
    return {
      title, width, type, props, valid, isGroup,
      ...viewCol,
      pin: isGroup ? 'l' : undefined,
      inView: true,
      inGroupedView
    }
  })
}

export function tableToCols (apiCols, view = undefined) {
  /*
  Takes columns direct from the API, and optionally a view, and returns
  columns ready for db access.
  
  Usage: A lot of tests  + getViewColumns
  */
  if (!view) {
    // If we don't have a view, columns just need a few extra props
    return apiCols.map(col => ({
      ...col,
      inView: false,
      inGroupedView: false
    }))
  } else {
    // Otherwise we need to do a full mapping
    return viewToCols(apiCols, view)
  }
}

/**
 * Returns a list of columns in the order they should be calculated.
 *
 * This uses a graph to determine the order.
 * Gotcha: Note the boolean check at the end; bad calc definitions (referring to source cols that don't exist)
 * will result in colIds that don't map to anything; so we need to remove them from the sort, or we have issues
 * with undefined values in the column list.
 */
const getTableColumns = (columns) => {
  const graph = buildDependencyGraph(columns)
  const ordered = topologicalSort(graph.graph, graph.indegree)
    .map(colId => columns.find(c => c.id === colId))
    .filter(Boolean)
    .map(col => ({
      id: col.id,
      type: col.type,
      calc: col.calc,
      // date type is needed for datetime columns for truncation
      date_trunc: col.props?.dateType ? dateTypes[col.props.dateType].date_trunc : undefined
    }))
  
  const colMap = Object.fromEntries(columns.map(col => [col.id, col]))
  for (const col of ordered) {
    col.valid = col.calc ? calcIsValid(col.calc, colMap) : true
  }
  return ordered
  
}

const apiViewToColumnFilters = (apiCols, apiView = {}) => {
  // Given an array pair of [col id, filters], looks it up and adds a `filters` property to it.
  const addFiltersToCol = ([id, filters]) => ({ ...apiCols.find(col => col.id === parseInt(id)), filters })
  return Object.entries(apiView.filters || {}).map(addFiltersToCol)
}

// Takes a column with a `filters` param and builds the query filters
const colToFilters = (col) => (buildColumnFilters(col, col.filters))

export const _getQuerySpec = (apiCols, apiView, columnsToFilter) => {
  const viewCols = tableToCols(apiCols, apiView)
  const isView = apiView !== undefined
  const isGroupedView = isView && viewCols.some(col => col.isGroup)
  const grouping = !isGroupedView ? undefined : {
    groups: viewCols.filter(col => col.isGroup),
    summaries: viewCols.filter(col => !col.isGroup)
  }
  
  // Map of alias id: sourceColumnId for filtered reports
  const aliases = (!isView || isGroupedView) ? undefined : Object.fromEntries(viewCols
    .filter(col => (col.id !== col.sourceColumnId))
    .map(col => [col.id, col.sourceColumnId])
  )
  
  return {
    // An array of the columns at the table - with id, type and calc.
    columns: getTableColumns(apiCols),
    aliases,
    grouping,
    
    // This will get set to true in cases where we *have a view*
    // We don't want this when filtering a regular table, as we want the pos to be based on the table, not the view.
    dynamicPos: isView,
    
    // Col type maps the *output* id to an object of type/props. These will have already been set in tableToCols.
    colTypes: Object.fromEntries(viewCols.map(col => ([col.id, { type: col.type, props: col.props }]))),
    sorting: getSortColumns(viewCols),
    filtering: {
      columns: columnsToFilter.map(colToFilters).filter(Boolean),
      where: undefined,
      rowIds: undefined,
      posList: undefined,
      posRange: undefined,
      output: {}
    }
  }
}

export const getChartQuerySpec = (apiCols, apiView) => {
  // QuerySpec for charts removes the table column filters but
  // keeps any chart-specific filters
  const colsWithFilters = apiViewToColumnFilters(apiCols, apiView)
  return _getQuerySpec(apiCols, apiView, colsWithFilters)
}

export const getQuerySpec = (apiCols, apiView) => {
  // QuerySpec is either table _or_ view filters - in a view, we don't want
  // to copy over any filters in the table
  const colsWithFilters = apiView ? apiViewToColumnFilters(apiCols, apiView) : apiCols
  return _getQuerySpec(apiCols, apiView, colsWithFilters)
}

export const filterQuerySpec = (querySpec, extraFilters) => {
  return {
    ...querySpec,
    filtering: twoLevelMerge(querySpec.filtering, extraFilters)
  }
  
}

/* Export private functions for testing */
export const __test__ = {
  countWindowCalcsInChain
}
