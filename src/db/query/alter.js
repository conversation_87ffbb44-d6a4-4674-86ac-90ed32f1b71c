import { typeMap } from '@/db/types.js'

const addColumnQueries = (columnId, columnType) => {
  return [
    `ALTER TABLE {table} ADD COLUMN c${columnId} ${typeMap[columnType]}`,
    `ALTER TABLE {table} ADD COLUMN r${columnId} VARCHAR`
  ]
}

const dropColumnQueries = (columnId) => {
  return [
    `ALTER TABLE {table} DROP COLUMN c${columnId}`,
    `ALTER TABLE {table} DROP COLUMN r${columnId}`
  ]
}

const changeColumnQueries = (columnId, columnType) => {
  return [
    `ALTER TABLE {table} DROP COLUMN c${columnId}`,
    `ALTER TABLE {table} ADD COLUMN c${columnId} ${typeMap[columnType]}`
  ]
}

export const alter = {
  add: addColumnQueries,
  drop: dropColumnQueries,
  change: changeColumnQueries
}
