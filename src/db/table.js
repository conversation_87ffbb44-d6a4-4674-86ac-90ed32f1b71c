import { convertValueForDb } from '@/utils/formats.js'
import { selectQuery } from '@/db/query/select.js'
import { alter } from '@/db/query/alter.js'
import { results } from '@/db/query/results.js'
import { positionUpdateQueries, refreshRowPositionQuery, rowUpdateQueries } from '@/db/query/update.js'
import { bulkInsert } from '@/db/query/insert.js'
import { aggregateIsValid, filterQuerySpec, getQuerySpec } from '@/db/query/utils.js'

export class DatabaseTable {
  
  /* -------------------------------
  Creation methods
  /* ------------------------------- */
  
  /**
   * Creates a new DatabaseTable instance
   * @param {Object} connection - Database connection instance
   * @param {string} name - Table name
   * @param {Array<Object>} columns - Array of column definitions
   */
  constructor (connection, name, columns) {
    this.connection = connection
    this.name = name
    
    // We store the first col map - this is useful for tests and console testing, so we can
    // call queries without constructing a query spec. However, it shouldn't be relied on,
    // and is wiped whenever there's a column change! In reality, we always have a querySpec
    // as we might be filtering, in a view, etc.
    this._initialQuerySpec = getQuerySpec(columns)
  }
  
  getSpec (querySpec) {
    if (!querySpec && !this._initialQuerySpec) {
      throw new Error('No querySpec provided and no initialQuerySpec available as table modified!')
    }
    return querySpec ?? this._initialQuerySpec
  }
  
  /* -------------------------------
  Internal methods
  /* ------------------------------- */
  
  /** Executes a SQL query on the database against the current table. */
  async execute (sql, params) {
    sql = sql
      .replace(/\n\s*/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/\{table}/g, this.name)
      .trim() + (!sql.endsWith(';') ? ';' : '')
    
    if (this.connection.log) {
      const logObj = { sql, time: new Date().toISOString() }
      if (params) {
        logObj.params = params
      }
      console.trace(logObj)
    }
    
    if (params === undefined) {
      return await this.connection.conn.query(sql)
    } else {
      const statement = await this.connection.conn.prepare(sql)
      return await statement.query(...params)
    }
  }
  
  async executeParallel (queries) {
    /**
     * Executes an array of SQL queries on the database against the current table.
     * @param {Array<Object|string>} queries - Array of queries to execute (objects with sql/params or strings)
     * @returns {Promise<Array>} Array of results from each query
     */
    return await Promise.all(queries.map(query => {
      if (typeof query === 'string') {
        return this.execute(query)
      } else {
        return this.execute(query.sql, query.params)
      }
    }))
  }
  
  async executeSequential (queries) {
    /**
     * Executes an array of SQL queries sequentially on the database against the current table.
     * @param {Array<Object|string>} queries - Array of queries to execute (objects with sql/params or strings)
     * @returns {Promise<Array>} Array of results from each query in order of execution
     */
    const results = []
    for (const query of queries) {
      if (typeof query === 'string') {
        results.push(await this.execute(query))
      } else {
        results.push(await this.execute(query.sql, query.params))
      }
    }
    return results
  }
  
  /**
   * Gets results from a query.
   * @param query
   * @returns {Promise<Object>}
   */
  async getResults (query) {
    return await this.execute(query).then(result => results(result))
  }
  
  /** Shorthand useful for debugging. */
  async console (query, cleanOnly = false) {
    return await this.getResults(query).then(results => results.console({ cleanOnly }))
  }
  
  /**
   * Gets results from a select query.
   * @param querySpec
   * @param selectSpec
   * @returns {Promise<Object>}
   */
  async getSelectResults (querySpec, selectSpec) {
    const sql = selectQuery(this.getSpec(querySpec), selectSpec)
    console.log('\ngetSelectResults:\n', sql)
    return await this.getResults(sql)
  }
  
  /* -------------------------------
  Query methods
  /* ------------------------------- */
  
  async count (querySpec) {
    const result = await this.getSelectResults(querySpec, [{
      id: '*',
      aggregate: 'count'
    }])
    return Number(result.scalar())
  }
  
  async aggregate (colId, aggregateKeys = [], querySpec) {
    const keys = aggregateKeys.filter(aggregateIsValid)
    if (keys.length === 0) return undefined
    const selects = keys.map(aggregate => ({ id: colId, aggregate }))
    const results = await this.getSelectResults(querySpec, selects)
    return results.row()
  }
  
  async rowIds (posListOrRange, querySpec) {
    
    const rowIdSpec = {}
    if (Array.isArray(posListOrRange)) {
      rowIdSpec.posList = posListOrRange
      
    } else if (posListOrRange) {
      rowIdSpec.posRange = posListOrRange
    }
    const spec = filterQuerySpec(this.getSpec(querySpec), rowIdSpec)
    // console.log('\n getRowObjects:', selectQuery(this.getSpec(spec)))
    const results = await this.getSelectResults(spec)
    // results.console()
    const data = await results.raw()
    return data.map(row => row.id)
  }
  
  async rowIndexes (rowIds, querySpec) {
    const spec = filterQuerySpec(this.getSpec(querySpec), { rowIds })
    const results = await this.getSelectResults(spec)
    const data = await results.raw()
    return data.map(row => row.pos)
  }
  
  async chartData (colIds, querySpec) {
    /* Gets row data as an array of objects, ready for ag-grid display */
    // console.log('\n getRowObjects:', selectQuery(this.getSpec(querySpec), colIds))
    const results = await this.getSelectResults(querySpec, colIds)
    // results.console()
    return results.grid(colIds, { cleanOnly: true })
  }
  
  async rowObjects (colIds, querySpec) {
    /*
    With a list of columns and either rowIds or positional range, get an array of row objects.
     */
    if (!colIds) throw new Error('Columns must be set for rowObjects')
    //console.log('\n getRowObjects:', selectQuery(this.getSpec(querySpec), colIds))
    const results = await this.getSelectResults(querySpec, colIds)
    //results.console()
    return results.grid(colIds, { pickValue: true })
  }
  
  async rowArray (colIds, querySpec, reprValues = false) {
    if (!colIds) throw new Error('Columns must be set for rowArray')
    querySpec = this.getSpec(querySpec)
    const results = await this.getSelectResults(querySpec, colIds)
    return results.array(colIds, querySpec.colTypes, reprValues)
  }
  
  async autoRowLimits (colIdsToCheck, querySpec) {
    /*
    Get a row limits object { from, to } to exclude rows that are empty in every column in columnsToCheck.
    */
    
    const spec = filterQuerySpec(this.getSpec(querySpec), {
      // Filter `c1 IS NOT NULL OR c2 IS NOT NULL ...`
      where: colIdsToCheck.map(colId => `c${colId} IS NOT NULL`).join(' OR ')
    })
    
    const selectSpec = [
      { id: 'pos', aggregate: 'min' },
      { id: 'pos', aggregate: 'max' }
    ]
    
    // So we only return max if its less than all rows, we do a count on the whole table and compare
    const [rowCount, results] = await Promise.all([
      this.count(querySpec),
      this.getSelectResults(spec, selectSpec)
    ])
    const limits = results.row()
    const [min, max] = [Number(limits.min), Number(limits.max)]
    return {
      ...(min > 0 && { from: min }),
      ...(max < rowCount - 1 && { to: max })
    }
  }
  
  /**
   * Gets the next available ID for a new row.
   *
   * Simply calls the MAX on the id column and adds one.
   *
   * @returns {Promise<number>} Next available ID
   */
  async nextId () {
    const results = await this.getResults('SELECT MAX(id) + 1 FROM {table}')
    return results.scalar()
  }
  
  /* -------------------------------
  Column changes
  /* ------------------------------- */
  /**
   * Adds a new column to the table
   */
  async addColumn (columnId, columnType) {
    this._initialQuerySpec = undefined
    await this.executeParallel(alter.add(columnId, columnType))
  }
  
  /**
   * Removes a column from the table
   * @param {string} columnId - ID of the column to remove
   * @returns {Promise<void>}
   */
  async removeColumn (columnId) {
    this._initialQuerySpec = undefined
    await this.executeParallel(alter.drop(columnId))
  }
  
  /**
   * Changes a column's type and properties
   * @param {string} columnId - ID of the column to change
   * @param {string} newType - New column type
   * @returns {Promise<void>}
   */
  async changeColumn (columnId, newType) {
    this._initialQuerySpec = undefined
    await this.executeSequential(alter.change(columnId, newType))
  }
  
  /**
   * Updates multiple rows using chunked CASE-based UPDATE statements
   * @param updates - Array of objects with format: [{
   *   rowId: number,
   *   cells: [{ columnId, rawValue, cleanValue, type }]
   * }]
   * @returns {Promise<void>}
   */
  async updateRows (updates) {
    if (updates.length === 0) return
    await this.executeParallel(rowUpdateQueries(updates))
  }
  
  /**
   * Inserts multiple rows in one shot
   * @param rows - Array of row objects
   * @param insertIndex - Index to insert rows at
   * @param querySpec
   * @returns {Promise<void>}
   */
  async insertRows (rows, insertIndex, querySpec) {
    // Update the positions of existing rows - do this first otherwise we'll update pos of new row too
    querySpec = this.getSpec(querySpec)
    const cols = querySpec.columns.filter(col => !col.calc)
    const updatePositionsSQL = `UPDATE {table} SET pos = pos + ? WHERE pos >= ?`
    await this.execute(updatePositionsSQL, [rows.length, insertIndex])
    
    // Now insert all columns in one shot
    const values = rows.map((row, index) => [
      row.id,
      insertIndex + index,
      ...cols.map(col => convertValueForDb(row[`c${col.id}`], col.type)),
      ...cols.map(col => row[`c${col.id}`])
    ])
    await this.executeParallel(bulkInsert(cols, rows, values))
  }
  
  /**
   * Moves multiple rows to a new position.
   * @param rowIds
   * @param startIndex
   * @returns {Promise<void>}
   */
  async moveRows (rowIds, startIndex) {
    const rowIdsPlaceholders = rowIds.map(() => '?').join(', ')
    
    // Step 1: Shift positions of rows starting from startIndex to make space
    const shiftSQL = `UPDATE {table} SET pos = pos + ? WHERE pos >= ? AND id NOT IN (${rowIdsPlaceholders})`
    await this.execute(shiftSQL, [rowIds.length, startIndex, ...rowIds])
    
    // Step 2: Create position map and update in chunks
    const positionMap = rowIds.reduce((map, rowId, index) => {
      map[rowId] = startIndex + index
      return map
    }, {})
    
    await this.executeParallel(positionUpdateQueries(positionMap))
    await this.execute(refreshRowPositionQuery())
  }
  
  async deleteRows (rowIds) {
    const deleteSQL = `DELETE FROM {table} WHERE id IN (${rowIds.map(() => '?').join(', ')})`
    await this.execute(deleteSQL, rowIds)
    await this.execute(refreshRowPositionQuery())
  }
}
