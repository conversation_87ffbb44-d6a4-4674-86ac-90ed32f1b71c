import { describe, expect, it } from 'vitest'
import { dateGrouping, groupByBand } from './grouping.js'

describe('dateGrouping', () => {
  const testColumn = 'test_date_column'
  
  Object.keys(dateGrouping).forEach((key) => {
    const { label, id, query } = dateGrouping[key]
    
    it(`should return correct query for ${label} (${id})`, () => {
      const sql = query(testColumn)
      expect(sql).toBeDefined()
      expect(typeof sql).toBe('string')
      expect(sql).toContain(testColumn) // Ensure the column is included in the output
    })
    
    it(`should have a valid label and id for ${key}`, () => {
      expect(label).toBeDefined()
      expect(typeof label).toBe('string')
      expect(label.length).toBeGreaterThan(0)
      
      expect(id).toBeDefined()
      expect(id).toBe(key) // The `id` should match the key
    })
  })
})

describe('groupByBand', () => {
  it('should throw an error if interval is missing', () => {
    expect(() => groupByBand('clicks', { min: 50, max: 80 })).toThrow('Interval is required')
  })
  
  it('should create a correct SQL case for given min, max, and interval', () => {
    const result = groupByBand('clicks', { min: 50, max: 79, interval: 10 })
    const expected = `
CASE
    WHEN clicks < 50 THEN '< 50'
    WHEN clicks BETWEEN 50 AND 59 THEN '50 - 59'
    WHEN clicks BETWEEN 60 AND 69 THEN '60 - 69'
    WHEN clicks BETWEEN 70 AND 79 THEN '70 - 79'
    ELSE '>= 80'
END AS clicks_range`.trim()
    expect(result).toBe(expected)
  })
  
  it('should throw an error if min is missing', () => {
    expect(() => groupByBand('clicks', { max: 80, interval: 10 })).toThrow('Both min and max are required')
  })
  
  it('should throw an error if max is missing', () => {
    expect(() => groupByBand('clicks', { min: 50, interval: 10 })).toThrow('Both min and max are required')
  })
  
  it('should throw an error if min and max are missing', () => {
    expect(() => groupByBand('clicks', { interval: 10 })).toThrow('Both min and max are required')
  })
  
  it('should throw an error if interval is missing', () => {
    expect(() => groupByBand('clicks', { min: 50, max: 80 })).toThrow('Interval is required')
  })
  
})
