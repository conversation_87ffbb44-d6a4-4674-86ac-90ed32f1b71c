export const typeMap = {
  text: 'VARCHAR',
  
  // This is the max digits (38 long) with 8 decimal places within that 38. This is massive an overkill almost always, and
  // will slow down some maths. Setting smaller defaults is going to be better, but this is a quick fix
  number: 'DECIMAL(38, 8)',
  percent: 'DECIMAL(38, 8)',
  
  // While 3 dp is the most of any real world currency, bitcoin is 8dp and ETH is 18dp - nothing right now would cover this scenario
  // - we need a way ultimately of customizing this at the precision level stored in our data store, which then comes through in dbConn.js here
  currency: 'DECIMAL(38, 3)',
  
  datetime: 'TIMESTAMP',
  boolean: 'BOOLEAN'
}
