/*
DuckDB strftime support:
https://duckdb.org/docs/sql/functions/dateformat.html

 */

import { LayoutGrid, RotateCw, SlidersHorizontal } from 'lucide-vue-next'

export const viewTypes = {
  'filter': {
    label: 'Filtered View',
    description: 'Same data structured like the original table, but with optional filtering and sorting applied.',
    icon: SlidersHorizontal,
    disable: false
  },
  'group': {
    label: 'Grouped View',
    description: 'Data grouped by one or more columns, with optional aggregations applied.',
    icon: LayoutGrid
  },
  'pivot': {
    label: 'Pivot View',
    description: 'Data pivoted by one or more columns, with optional aggregations applied.',
    icon: RotateCw,
    disable: true
  }
}

export const getViewType = (view) => {
  if (view.groups?.length) {
    return 'group'
  } else {
    return 'filter'
  }
}

export const getViewIcon = (view) => {
  return viewTypes[getViewType(view)].icon
}

export const dateGrouping = {
  
  'year': {
    label: 'Year',
    id: 'year',
    query: (col) => `year(${col})`
  },
  'year-month': {
    label: 'Year-Month',
    id: 'year-month',
    query: (col) => `strftime(${col}, '%Y-%m')`
  },
  'year-quarter': {
    label: 'Year-Quarter',
    id: 'year-quarter',
    query: (col) => `printf('%d-Q%d', year(${col}), quarter(${col}))`
  },
  
  'year-week': {
    label: 'Year-Week',
    id: 'year-week',
    query: (col) => `strftime(${col}, '%Y-%W')`
  },
  
  'year-month-day': {
    label: 'Year-Month-Day',
    id: 'year-month-day',
    query: (col) => `strftime(${col}, '%Y-%m-%d')`
  },
  
  'hour': {
    label: 'Hour',
    id: 'hour',
    query: (col) => `hour(${col})`
  },
  'minute': {
    label: 'Minute',
    id: 'minute',
    query: (col) => `minute(${col})`
  },
  'second': {
    label: 'Second',
    id: 'second',
    query: (col) => `second(${col})`
  },
  'hour-minute-24': {
    label: 'Hour-Minute (24 hour)',
    id: 'hour-minute-24',
    query: (col) => `strftime(${col}, '%H:%M')`
  },
  'hour-minute-12': {
    label: 'Hour-Minute (12 hour)',
    id: 'hour-minute-12',
    query: (col) => `strftime(${col}, '%I:%M %p')`
  },
  'day-of-the-week': {
    label: 'Day of the Week',
    id: 'day-of-the-week',
    query: (col) => `dayname(${col})`
  },
  'day-of-the-year': {
    label: 'Day of the Year',
    id: 'day-of-the-year',
    query: (col) => `doy(${col})`
  },
  'day-of-the-month': {
    label: 'Day of the Month',
    id: 'day-of-the-month',
    query: (col) => `day(${col})`
  },
  'day-month': {
    label: 'Day-Month',
    id: 'day-month',
    query: (col) => `strftime(${col}, '%d-%m')`
  },
  'month': {
    label: 'Month',
    id: 'month',
    query: (col) => `monthname(${col})`
  },
  'month-num': {
    label: 'Month (number)',
    id: 'month-num',
    query: (col) => `month(${col})`
  },
  'quarter': {
    label: 'Quarter',
    id: 'quarter',
    query: (col) => `quarter(${col})`
  }
}

export function groupByBand (col, { min, max, interval }) {
  // Not currently used though we do have tests
  if (min === undefined || max === undefined) {
    throw new Error('Both min and max are required')
  }
  if (!interval) {
    throw new Error('Interval is required')
  }
  
  let sql = `CASE\n`
  
  // Add the "< min" condition
  sql += `    WHEN ${col} < ${min} THEN '< ${min}'\n`
  
  // Add range conditions
  let lowerBound = min
  while (lowerBound <= max) {
    const upperBound = Math.min(lowerBound + interval - 1, max)
    sql += `    WHEN ${col} BETWEEN ${lowerBound} AND ${upperBound} THEN '${lowerBound} - ${upperBound}'\n`
    lowerBound += interval
  }
  
  // Add the "> max" condition
  sql += `    ELSE '>= ${max + 1}'\n`
  sql += `END AS ${col}_range`
  return sql
}
