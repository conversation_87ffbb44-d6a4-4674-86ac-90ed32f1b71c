import * as duckdb from '@duckdb/duckdb-wasm'
import duckdb_wasm from '@duckdb/duckdb-wasm/dist/duckdb-mvp.wasm?url'
import mvp_worker from '@duckdb/duckdb-wasm/dist/duckdb-browser-mvp.worker.js?url'
import duckdb_wasm_next from '@duckdb/duckdb-wasm/dist/duckdb-eh.wasm?url'
import eh_worker from '@duckdb/duckdb-wasm/dist/duckdb-browser-eh.worker.js?url'
import { DatabaseTable } from '@/db/table.js'
import { typeMap } from '@/db/types.js'
import { convertRawValueForDb, convertValueForDb } from '@/utils/formats.js'

const MANUAL_BUNDLES = {
  mvp: {
    mainModule: duckdb_wasm,
    mainWorker: mvp_worker
  },
  eh: {
    mainModule: duckdb_wasm_next,
    mainWorker: eh_worker
  }
}

const noCalcCols = (columns) => {
  return columns
    // filter out calcs
    .filter(col => !col.calc)
    
    // alphanumeric sort - consistent ordering needed for bulk insert. Tests fail if we do numeric test a.id - b.id ?
    .toSorted((a, b) => a.id.toString().localeCompare(b.id.toString()))
  
}

const getTableName = (tableId) => `table${tableId}`

const getCreateTableSql = (tableName, cols) => {
  // CREATE OR REPLACE is duckdb special: https://duckdb.org/docs/stable/sql/statements/create_table.html#create-or-replace
  // We replaced "DROP IF EXISTS; ... CREATE" with this for a single sql call
  const columnDefinitions = cols.map((col) => `c${col.id} ${typeMap[col.type]}`)
  const rawColumnDefinitions = cols.map(col => `r${col.id} VARCHAR`)
  
  // Primary key on id leads to issues with dropping columns so we don't index on anything
  // alt would be to drop the index and recreate it after each column drop. Could do some
  // tests on whether this is much faster.
  // track this bug: https://github.com/duckdb/duckdb/issues/14664
  return `CREATE OR REPLACE TABLE ${tableName} (
    ${columnDefinitions.join(', ')},
    id INTEGER,
    pos INTEGER NOT NULL,
    ${rawColumnDefinitions.join(', ')}
  );`
}

const loadData = async (db, conn, name, columns, rows) => {
  const rowArray = rows.map((row, index) => {
    return {
      id: row.id,
      pos: index,
      ...Object.fromEntries(columns.map(col => [`c${col.id}`, convertValueForDb(row[`c${col.id}`], col.type)])),
      ...Object.fromEntries(columns.map(col => [`r${col.id}`, convertRawValueForDb(row[`c${col.id}`])]))
    }
  })
  await db.registerFileText('data.json', JSON.stringify(rowArray))
  await conn.insertJSONFromPath('data.json', { name, create: false })
}

export const createConnection = () => {
  const connection = {
    log: false,
    conn: null,
    db: null,
    tables: {},
    firstTableId: null,
    lastTableId: null,
    prepare: null,
    
    connect: async (log = false) => {
      connection.log = log
      const bundle = await duckdb.selectBundle(MANUAL_BUNDLES)
      const worker = new Worker(bundle.mainWorker)
      const logger = new duckdb.VoidLogger()  // connection.log ? new duckdb.ConsoleLogger() :
      connection.db = new duckdb.AsyncDuckDB(logger, worker)
      await connection.db.instantiate(bundle.mainModule, bundle.pthreadWorker)
      connection.conn = await connection.db.connect()
      return connection
    },
    
    disconnect: async () => {
      if (connection.conn) {
        await connection.conn.close()
        connection.conn = null
        connection.tables = {}
        connection.firstTableId = null
        connection.lastTableId = null
      }
    },
    
    buildTable: async (tableId, columns, rows, forceRebuild = true) => {
      if (forceRebuild || !connection.tables[tableId]) {
        const tableName = getTableName(tableId)
        
        // Create the table and load in all data
        const cols = noCalcCols(columns)
        await connection.conn.query(getCreateTableSql(tableName, cols))
        await loadData(connection.db, connection.conn, tableName, cols, rows)
        
        // Create the table instance
        connection.tables[tableId] = new DatabaseTable(connection, tableName, columns)
        if (!connection.firstTableId) connection.firstTableId = tableId
      }
      connection.lastTableId = tableId
    },
    
    dropTable: async (tableId) => {
      await connection.conn.query(`DROP TABLE IF EXISTS ${getTableName(tableId)};`)
      delete connection.tables[tableId]
      if (connection.firstTableId === tableId) {
        connection.firstTableId = Object.keys(connection.tables)[0] || null
      }
    },
    
    get firstTable () {
      return connection.tables[connection.firstTableId]
    },
    
    get lastTable () {
      return connection.tables[connection.lastTableId]
    }
  }
  
  return connection
}
