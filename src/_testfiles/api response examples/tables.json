[{"id": "h3ViYU9HeTiauTvufb44oU", "date_created": "2025-02-24T16:45:43.092488Z", "date_last_updated": "2025-02-25T11:34:31.415857Z", "title": "<PERSON><PERSON><PERSON>", "project": null, "columns": [{"id": 0, "type": "datetime", "props": {"format": "yyyy-MM-dd", "dateType": "date"}, "width": 150, "header": "Date", "isLabel": false, "aggregate": "diff"}, {"id": 1, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "PF", "isLabel": false, "aggregate": "sum"}, {"id": 2, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "F5", "isLabel": false, "aggregate": "sum"}, {"id": 3, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 145, "header": "PRO", "isLabel": false, "aggregate": "sum"}, {"id": 4, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "GR", "isLabel": false, "aggregate": "sum"}, {"id": 5, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Shield", "isLabel": false, "aggregate": "sum"}]}, {"id": "2pgucA4DCMEzqhMLXT7aTJ", "date_created": "2024-12-29T10:55:50.057140Z", "date_last_updated": "2025-02-25T12:13:01.293845Z", "title": "Doing another with manual entry", "project": null, "columns": [{"id": 0, "type": "text", "props": {}, "width": 132, "header": "Product", "isLabel": true, "aggregate": "counta"}, {"id": 5, "type": "boolean", "props": {}, "width": 185, "header": "Tasty?", "isLabel": false, "aggregate": "count"}, {"id": 2, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "List price", "isLabel": false, "aggregate": "max"}, {"id": 3, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 262, "header": "RRP", "isLabel": false, "aggregate": "avg"}, {"id": 4, "calc": {"type": "diff", "sourceCols": [3, 2]}, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "<PERSON><PERSON>", "isLabel": false, "aggregate": "sum"}, {"id": 6, "calc": {"type": "cumulative", "sourceCols": [4]}, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "Cumulative", "isLabel": false, "aggregate": "sum"}, {"id": 7, "calc": {"type": "avg", "sourceCols": [4, 6]}, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "Average", "isLabel": false, "aggregate": "sum"}]}, {"id": "YiDCxwi2FGKnvcJE9Bzce8", "date_created": "2025-01-23T12:47:51.181247Z", "date_last_updated": "2025-02-25T13:16:17.642526Z", "title": "Libra", "project": null, "columns": [{"id": 0, "type": "datetime", "props": {"format": "yyyy-MM-dd", "dateType": "date"}, "width": 236, "header": "#date", "isLabel": false, "aggregate": "diff"}, {"id": 1, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 259, "header": "weight", "isLabel": false, "aggregate": "sum"}, {"id": 3, "type": "text", "props": {}, "width": 213, "isLabel": false, "aggregate": "count"}, {"id": 5, "type": "text", "props": {}, "width": 150, "isLabel": false, "aggregate": "count"}, {"id": 4, "calc": {"type": "sum", "sourceCols": [1, 3]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 102, "header": "Sum", "isLabel": false, "aggregate": "sum"}, {"id": 2, "calc": {"data": {"moving": 10}, "type": "moving", "sourceCols": [1]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 245, "header": "Moving Average of weight", "isLabel": false, "aggregate": "sum"}]}, {"id": "bNrupYH9wgcRRcq9c3geE3", "date_created": "2025-02-20T11:54:59.335381Z", "date_last_updated": "2025-02-23T15:48:01.277899Z", "title": "Libra", "project": null, "columns": [{"id": 0, "type": "datetime", "props": {"format": "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'", "dateType": "datetime"}, "width": 150, "header": "#date", "isLabel": false, "aggregate": "diff"}, {"id": 1, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "weight", "isLabel": false, "aggregate": "sum"}, {"id": 2, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "weight trend", "isLabel": false, "aggregate": "sum"}, {"id": 9, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "isLabel": false, "aggregate": "count"}, {"id": 8, "calc": {"type": "cumulative", "sourceCols": [2]}, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Cumulative", "isLabel": false, "aggregate": "sum"}]}, {"id": "UPxAEk5BT9JTULHfK3k9Pm", "date_created": "2024-12-28T09:06:18.932832Z", "date_last_updated": "2025-02-23T15:47:32.800243Z", "title": "Starting from blank", "project": null, "columns": [{"id": 0, "pin": "n", "type": "text", "props": {}, "width": 269, "header": "Things i'm counting", "isLabel": false, "aggregate": "count"}, {"id": 1, "type": "text", "props": {}, "width": 150, "header": "<PERSON><PERSON>", "isLabel": false, "aggregate": "sum"}, {"id": 2, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "<PERSON><PERSON><PERSON>", "isLabel": false, "aggregate": "sum"}, {"id": 4, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "isLabel": false, "aggregate": "count"}, {"id": 5, "type": "percent", "props": {"decimals": 2, "decimalsPad": false}, "width": 150, "isLabel": false, "aggregate": "avg"}]}, {"id": "jGWQb4dEoRcsUg6EchYGVc", "date_created": "2025-01-22T12:57:35.418097Z", "date_last_updated": "2025-01-22T13:00:10.117548Z", "title": "Testing mode on a string", "project": null, "columns": [{"id": 0, "type": "text", "props": {}, "width": 150, "header": "Make", "isLabel": false, "aggregate": "count"}, {"id": 1, "type": "text", "props": {}, "width": 150, "header": "Model", "isLabel": false, "aggregate": "count"}, {"id": 2, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "List price", "isLabel": false, "aggregate": "count"}]}]