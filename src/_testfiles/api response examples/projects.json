[{"id": "S68bQ28Dyej3RXUQhrHXC8", "date_created": "2024-11-28T10:54:52.118095Z", "date_last_updated": "2024-11-28T10:54:52.118095Z", "title": "Another project", "data": null, "theme": null, "tables": [{"id": "8gS8mapf4YSjDGaFp8MYX4", "date_created": "2024-11-14T10:23:38.574433Z", "date_last_updated": "2025-01-23T17:20:49.374170Z", "title": "Clubs Poker registration data", "project": "S68bQ28Dyej3RXUQhrHXC8", "columns": [{"id": 1, "type": "text", "props": {}, "width": 204, "header": "External ID", "isLabel": false, "aggregate": "min"}, {"id": 3, "type": "text", "props": {}, "width": 173, "header": "", "isLabel": true, "aggregate": "count"}, {"id": 0, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 214, "header": "UniqueId", "isLabel": false, "aggregate": "counta"}, {"id": 2, "type": "text", "props": {}, "width": 150, "header": "External System", "isLabel": false, "aggregate": "distinct"}, {"id": 4, "type": "text", "props": {}, "width": 150, "header": "First Name", "isLabel": false, "aggregate": "count"}, {"id": 5, "type": "text", "props": {}, "width": 150, "header": "Second Name", "isLabel": false, "aggregate": "count"}, {"id": 6, "type": "text", "props": {}, "width": 150, "header": "Display Name", "isLabel": false, "aggregate": "min"}, {"id": 7, "type": "text", "props": {}, "width": 253, "header": "Email", "isLabel": false, "aggregate": "count"}, {"id": 8, "type": "datetime", "props": {"format": "d <PERSON><PERSON> yyyy", "dateType": "date"}, "width": 180, "header": "Registration Date", "isLabel": false, "aggregate": "distinct"}]}, {"id": "2sQCLU3gzfiBemo8bsev3S", "date_created": "2024-11-13T17:07:39.893581Z", "date_last_updated": "2025-01-24T15:34:59.623145Z", "title": "Datahub traffic", "project": "S68bQ28Dyej3RXUQhrHXC8", "columns": [{"id": 0, "pin": "l", "type": "datetime", "props": {"format": "yyyy-MM-dd", "dateType": "date"}, "width": 182, "header": "Date", "isLabel": false, "aggregate": "count"}, {"id": 9, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 138, "header": "Costs", "isLabel": false, "aggregate": "sum"}, {"id": 4, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 96, "header": "GR", "isLabel": false, "aggregate": "sum"}, {"id": 1, "type": "number", "props": {}, "width": 107, "header": "Sum", "isLabel": false, "aggregate": "max"}, {"id": 2, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 93, "header": "F5", "isLabel": false, "aggregate": "sum"}, {"id": 3, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "PRO", "isLabel": false, "aggregate": "sum"}, {"id": 5, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Shield", "isLabel": false, "aggregate": "sum"}, {"id": 6, "calc": {"type": "sum", "sourceCols": [2, 3, 4, 1, 5]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 143, "header": "Sum", "isLabel": false, "aggregate": "sum"}, {"id": 7, "calc": {"type": "cumulative", "sourceCols": [6]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Cumulative", "isLabel": false, "aggregate": "count"}, {"id": 8, "calc": {"type": "sum", "sourceCols": [7, 6, 2]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Sum", "isLabel": false, "aggregate": "skew"}]}, {"id": "gViXQqS4iC6kmdYgrQ85WV", "date_created": "2025-01-25T14:53:21.599189Z", "date_last_updated": "2025-01-26T10:57:58.472722Z", "title": "Stationary", "project": "S68bQ28Dyej3RXUQhrHXC8", "columns": [{"id": 5, "type": "text", "props": {}, "width": 150, "header": "<PERSON><PERSON>", "isLabel": false, "aggregate": "count"}, {"id": 4, "type": "text", "props": {}, "width": 124, "header": "Color", "isLabel": false, "aggregate": "count"}, {"id": 9, "type": "datetime", "props": {"format": "yyyy-MM-dd HH:mm", "dateType": "datetime"}, "width": 150, "header": "Promo", "isLabel": false, "aggregate": "count"}, {"id": 10, "type": "text", "props": {}, "width": 195, "isLabel": false, "aggregate": "count"}, {"id": 6, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 136, "header": "Stock", "isLabel": false, "aggregate": "sum"}, {"id": 7, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "RRP", "isLabel": false, "aggregate": "avg"}, {"id": 8, "calc": {"type": "multiply", "sourceCols": [6, 7]}, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "Stock value", "isLabel": false, "aggregate": "sum"}]}]}, {"id": "Ca8LUwxL6ck4dWi5ZJCqYR", "date_created": "2024-11-14T11:40:59.793842Z", "date_last_updated": "2024-11-14T11:40:59.793842Z", "title": "GTO", "data": null, "theme": null, "tables": [{"id": "GcPE38ufYQwdGLxzEWKVqb", "date_created": "2025-01-30T11:49:49.862790Z", "date_last_updated": "2025-01-30T11:49:55.010424Z", "title": "Player List (2025 01 13 14 31)", "project": "Ca8LUwxL6ck4dWi5ZJCqYR", "columns": [{"id": 0, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Id", "isLabel": true, "aggregate": "sum"}, {"id": 1, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "External ID", "isLabel": false, "aggregate": "sum"}, {"id": 2, "type": "text", "props": {}, "width": 150, "header": "External System", "isLabel": false, "aggregate": "count"}, {"id": 3, "type": "text", "props": {}, "width": 150, "header": "<PERSON>", "isLabel": false, "aggregate": "count"}, {"id": 4, "type": "text", "props": {}, "width": 150, "header": "First Name", "isLabel": false, "aggregate": "count"}, {"id": 5, "type": "text", "props": {}, "width": 150, "header": "Second Name", "isLabel": false, "aggregate": "count"}, {"id": 6, "type": "text", "props": {}, "width": 150, "header": "Display Name", "isLabel": false, "aggregate": "count"}, {"id": 7, "type": "text", "props": {}, "width": 150, "header": "Email", "isLabel": false, "aggregate": "count"}, {"id": 8, "type": "datetime", "props": {"format": "yyyy/M/d HH:mm:ss", "dateType": "datetime"}, "width": 150, "header": "Registration Date", "isLabel": false, "aggregate": "diff"}]}]}, {"id": "EKY7YxE3n23j3YTrN6Nppt", "date_created": "2025-01-28T09:01:05.489606Z", "date_last_updated": "2025-01-28T09:01:05.489606Z", "title": "iGO", "data": null, "theme": null, "tables": [{"id": "QRiKWzo8VZUWjpTDtXB48c", "date_created": "2025-01-28T09:08:12.505532Z", "date_last_updated": "2025-02-21T15:10:28.998423Z", "title": "Cash Wagers by Month", "project": "EKY7YxE3n23j3YTrN6Nppt", "columns": [{"id": 0, "type": "datetime", "props": {"format": "yyyy-MM", "dateType": "year-month"}, "width": 150, "header": "Month", "isLabel": true, "aggregate": "diff"}, {"id": 1, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 285, "header": "Casino", "isLabel": false, "aggregate": "sum"}, {"id": 2, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Betting", "isLabel": false, "aggregate": "sum"}, {"id": 3, "type": "currency", "props": {"locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "Poker", "isLabel": false, "aggregate": "sum"}, {"id": 11, "calc": {"data": {"moving": 3}, "type": "moving", "sourceCols": [3]}, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Moving Average of Poker", "isLabel": false, "aggregate": "sum"}, {"id": 8, "calc": {"type": "sum", "sourceCols": [3, 1, 2]}, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Sum", "isLabel": false, "aggregate": "sum"}, {"id": 10, "calc": {"data": {"moving": 12}, "type": "moving", "sourceCols": [8]}, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Moving Average of Sum", "isLabel": false, "aggregate": "sum"}, {"id": 9, "calc": {"data": {"up": 12}, "type": "change", "sourceCols": [8]}, "type": "percent", "props": {"force": false, "decimals": 2, "decimalsPad": false}, "width": 150, "header": "Change %", "isLabel": false, "aggregate": "avg"}, {"id": 4, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Cash Wagers", "isLabel": false, "aggregate": "sum"}, {"id": 5, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "NAGGR(M)", "isLabel": false, "aggregate": "sum"}, {"id": 6, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 174, "header": "Active Accounts (K)", "isLabel": false, "aggregate": "sum"}, {"id": 7, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "ARPPA", "isLabel": false, "aggregate": "sum"}]}]}]