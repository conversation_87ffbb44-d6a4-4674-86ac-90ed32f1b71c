/*
Maintains state for the active table currently being edited.

Will provide access to save this back to our database via the API.
 */
import { defineStore } from 'pinia'

import { useAuthStore } from '@/stores/auth'
import { columnTypes, getDefaultColProps } from '@/utils/formats'
import { getColTypeAndPropsForCalc, verifyCalcData } from '@/utils/calcs'
import { getColumnHeaderById } from '@/charts/chartConfig.js'
import { TableQueue } from '@/utils/api/tableQueue.js'
import { dateGrouping } from '@/db/grouping.js'
import { calculatedColumns } from '@/utils/calcs.js'
import { getQuerySpec, tableToCols } from '@/db/query/utils.js'

export const defaultColumnWidth = 150

export function createColumn (id, header = undefined, type = 'text', isLabel = false, props = undefined) {
  props = props || getDefaultColProps(type)
  return {
    id,
    header,
    props,
    type,
    aggregate: columnTypes[type]?.aggregate,
    isLabel: Boolean(isLabel),
    width: defaultColumnWidth
  }
}

export function getNextViewColumnId (view) {
  const cols = [...view.groups || [], ...view.columns || []]
  if (cols.length === 0) return 0
  return Math.max(...cols.map(c => c.id)) + 1
}

export function createViewGroup (sourceColumnId, id) {
  return {
    id,
    sourceColumnId,
    header: undefined,
    aggregate: undefined,
    by: undefined,
    width: defaultColumnWidth
  }
}

export function createViewColumn (sourceColumnId, id) {
  return {
    id,
    sourceColumnId,
    header: undefined,
    aggregate: undefined,
    summarize: undefined,
    width: defaultColumnWidth
  }
}

export function createView (table) {
  /* Create a new, empty view for a specified table */
  return {
    table,
    title: undefined,
    id: undefined,
    groups: [],
    columns: []
  }
}

export function createBlankColumns (total) {
  return Array.from({ length: total }, (e, i) => createColumn(i))
}

const verifyColumns = (columns) => {
  // Checks columns coming in from the API and checks now they are valid. This can help with
  // forward compatibility with future changes to the API. Right now it checks that any columns
  // have valid calculations and have data properties set if required. It helps now to set defaults
  // so that future code can rely on it.
  return columns.map(col => {
    if (col.calc) {
      // check column calc is a key in calculatedColumns
      if (!(col.calc.type in calculatedColumns)) {
        col.calc = undefined
        return col
      }
      col.calc.sourceCols = col.calc.sourceCols || []
      verifyCalcData(col.calc)
    }
    return col
  })
}

export const useTableStore = defineStore('table', {
  
  persist: false,
  
  state: () => {
    return {
      // databased fields
      id: null,
      title: null,
      project: null,
      charts: [],
      views: [],
      columns: [],
      
      // --- transient fields
      
      // queue for saving changes
      queue: null,
      
      // Stores timestamp of the last update. Components can watch for a change
      // to this and respond accordingly.
      lastUpdate: null,
      
      // Stores the current view count; this will get set by TableEditor after
      // a successful query and will be based on the current view. This allows
      // other components to watch on the change
      viewRowCount: 0
    }
  },
  
  getters: {
    
    dbTable (state) {
      return useAuthStore().db.tables[state.id]
    },
    
    hasQueue (state) {
      return state.queue !== null && state.queue.queue.length > 0
    },
    
    labelColumnId (state) {
      const col = state.columns.find(col => col.isLabel)
      if (col) {
        return col.id
      }
    },
    
    columnCount (state) {
      return state.columns.length
    },
    
    calcCols (state) {
      return Object.values(state.columns).filter(col => col.calc)
    },
    
    calcColIds (state) {
      return state.columns.filter(col => col.calc).map(col => col.id)
    },
    
    leftPinIndex (state) {
      // Find the index of the first column that doesn't have a pinned left
      return state.columns.findIndex(c => c.pin !== 'l')
    },
    
    rightPinIndex (state) {
      // Find the index of the last column that does not have a pinned right
      return state.columns.map(c => c.pin === 'r' ? 'r' : 'x').lastIndexOf('x')
    },
    
    isGroupedView (state) {
      return (viewId) => {
        return viewId && state.view(viewId).groups.length
      }
    },
    
    viewIsValid (state) {
      return (viewId) => {
        const columns = state.getViewColumns(viewId)
        
        // Check that all columns have a defined type
        if (!columns.every(col => col.type !== undefined)) {
          return false
        }
        // Check that group columns have valid summarize fields for their types
        const view = state.views.find(v => v.id === viewId)
        if (!view || !view.groups) return true
        
        return view.groups.every(group => {
          const col = state.columns.find(c => c.id === group.sourceColumnId)
          if (!col) return false
          
          // For datetime columns, check that 'by' is a valid date grouping
          if (col.type === 'datetime' && group.by) {
            return group.by in dateGrouping
          }
          
          // For other column types, no specific validation needed yet; we get
          // display a filtered view if some columns aren't valid
          return true
        })
      }
    }
    
  },
  
  actions: {
    
    getViewColumns (viewId) {
      const view = this.views.find(v => v.id === viewId) // returns undefined if we don't have a viewId so no match
      return tableToCols(this.columns, view)
    },
    
    getQuerySpec (viewId) {
      return getQuerySpec(this.columns, this.views.find(v => v.id === viewId))
    },
    
    getColumnAutoHeader (id) {
      return getColumnHeaderById(this.columns, id)
    },
    
    getColumn (id) {
      return this.columns.find(c => c.id === id)
    },
    
    getColumnPosition (id) {
      return this.columns.findIndex(c => c.id === id)
    },
    
    async flushQueue () {
      if (this.queue) {
        await this.queue.flush()
      }
    },
    
    /* load from the API */
    async load (id) {
      if (!id) {
        throw Error('Cannot load a table with no ID.')
      }
      await this.flushQueue()
      const authStore = useAuthStore()
      const json = await authStore.get(`tables/${id}/`)
      if (json) {
        
        // load full row data into the database
        const rows = await authStore.get(`tables/${id}/rows/`)
        await authStore.db.buildTable(id, json.columns, rows)
        
        // update the store - only do this after DB is loaded, otherwise grid will try to load rows that don't exist
        this.project = json.project
        this.title = json.title
        this.columns = verifyColumns(json.columns)
        this.charts = json.charts
        this.views = json.views
        this.id = id
        this.queue = new TableQueue(this.id)
      }
    },
    
    diffRows (diff) {
      if (Object.keys(diff).length > 0) {
        this.queue.add('updateRows', diff)
      }
    },
    
    insertRows (rows, index) {
      this.queue.add('insertRows', { rows, index })
    },
    
    moveRows (rowIds, index) {
      this.queue.add('moveRows', { rowIds, index })
    },
    
    deleteRows (rowIds) {
      this.queue.add('deleteRows', { rowIds })
    },
    
    saveColumn (colId) {
      const col = this.columns.find(c => c.id === colId)
      this.queue.add(`updateColumn`, col, { id: colId })
    },
    
    clearColumnAggregate (id) {
      const col = this.columns.find(c => c.id === id)
      col.aggregate = null
      this.queue.add('clearColumnAggregate', null, { id })
    },
    
    deleteColumns (colIds) {
      this.columns = this.columns.filter(c => !colIds.includes(c.id))
      this.queue.add('deleteColumns', { colIds })
    },
    
    saveColumns () {
      this.queue.add('updateTable', { columns: this.columns })
    },
    
    saveTitle () {
      this.queue.add('updateTable', { title: this.title })
    },
    
    saveProject () {
      this.queue.add('updateTable', { project: this.project })
    },
    
    async delete () {
      const authStore = useAuthStore()
      this.queue.kill()
      return await authStore.delete(`tables/${this.id}/`)
    },
    
    moveColumn (id, newPos) {
      // find the column with id and move it to newPos in the array
      const col = this.columns.find(c => c.id === id)
      const oldPos = this.columns.indexOf(col)
      this.columns.splice(oldPos, 1)
      this.columns.splice(newPos, 0, col)
      this.saveColumns()
    },
    
    resizeColumn (id, width) {
      const col = this.columns.find(c => c.id === id)
      col.width = width
      this.saveColumn(id)
    },
    
    pinColumn (id, right = false) {
      const col = this.columns.find(c => c.id === id)
      col.pin = right ? 'r' : 'l'
      this.saveColumn(id)
    },
    
    unpinColumn (id) {
      // While an empty 'pin' option is valid, because of our patch code this will get ignored, so we have to be explicit with an 'n'.
      const col = this.columns.find(c => c.id === id)
      col.pin = 'n'
      this.saveColumn(id)
    },
    
    setColumnAggregate (id, aggregate) {
      const col = this.columns.find(c => c.id === id)
      col.aggregate = aggregate
      this.saveColumn(id)
    },
    
    setColumnHeader (id, header) {
      const col = this.columns.find(c => c.id === id)
      col.header = header
      this.saveColumn(id)
    },
    
    insertColumn (position, type) {
      let id
      if (this.columns.length === 0) {
        id = 0
        position = 0
      } else {
        id = Math.max(...this.columns.map(c => c.id)) + 1
      }
      const col = createColumn(id, undefined, type || 'text')
      this.columns.splice(position, 0, col)
      this.saveColumns()
      return col
    },
    
    changeColumn (colData) {
      // Switch in the new column in-place; assumes newColumn has its id set correctly
      const col = this.columns.find(c => c.id === colData.id)
      const wasLabel = col.isLabel
      
      // go thru props in colData; skip over non API fields
      Object.keys(colData).forEach(key => {
        if (!['sourceColumnId', 'inView', 'inGroupedView'].includes(key)) {
          col[key] = colData[key]
        }
      })
      
      // if this is now a label column, make sure no others are
      if (!wasLabel && col.isLabel) {
        this.columns.forEach(c => {
          if (c.id !== col.id) {
            c.isLabel = false
          }
        })
      }
      this.saveColumns()
      return col
    },
    
    changeCalcColumn (id, calc, sourceCols, data, header) {
      const col = this.columns.find(c => c.id === id)
      
      // Whenever changing a calc col, we rebuild the type and props from scratch
      const cols = sourceCols.map(c => this.columns.find(col => col.id === c))
      const {
        colType,
        colProps
      } = getColTypeAndPropsForCalc(calc, cols)
      
      col.calc = {
        type: calc.type,
        sourceCols,
        data
      }
      
      verifyCalcData(col.calc)
      if (header) {
        col.header = header || calc.label
      }
      col.type = colType
      col.props = colProps
      this.saveColumns()
      return col
    },
    
    insertCalcColumn (position, calc, sourceCols, data, header) {
      // Get new max ID
      const newId = Math.max(...this.columns.map(c => c.id)) + 1
      
      // Get the type and props for this calc
      const cols = sourceCols.map(c => this.columns.find(col => col.id === c))
      const { colType, colProps } = getColTypeAndPropsForCalc(calc, cols)
      const col = createColumn(newId, header || calc.label, colType, false, colProps)
      col.calc = {
        type: calc.type,
        sourceCols,
        data
      }
      verifyCalcData(col.calc)
      this.columns.splice(position, 0, col)
      this.saveColumns()
      return col
    },
    
    toggleLabelColumn (id) {
      this.columns.forEach(col => {
        col.isLabel = (col.id === id && !col.isLabel)
      })
      this.saveColumns()
    },
    
    async reloadCharts () {
      const authStore = useAuthStore()
      this.charts = await authStore.get('charts/', { table: this.id })
    },
    
    async reloadViews () {
      const authStore = useAuthStore()
      this.views = await authStore.get('views/', { table: this.id })
    },
    
    async updateViewGroup (viewId, id, updateKwargs) {
      const view = this.views.find(v => v.id === viewId)
      const group = view.groups.find(g => g.id === id)
      const newData = {
        ...group,
        ...updateKwargs
      }
      Object.assign(group, newData)
      this.queue.add('updateViewGroup', newData, { viewId, id: id })
    },
    
    async updateViewColumn (viewId, id, updateKwargs) {
      const view = this.views.find(v => v.id === viewId)
      const column = view.columns.find(c => c.id === id)
      const newData = {
        ...column,
        ...updateKwargs
      }
      Object.assign(column, newData)
      this.queue.add('updateViewColumn', newData, { viewId, id: id })
    },
    
    async updateViewGroupOrColumn (viewId, colId, updateKwargs) {
      
      // Check if this id is in the view columns or groups, then call the appropriate method above
      const view = this.views.find(v => v.id === viewId)
      if (view.groups.find(g => g.id === colId)) {
        await this.updateViewGroup(viewId, colId, updateKwargs)
      } else {
        await this.updateViewColumn(viewId, colId, updateKwargs)
      }
    },
    
    setViewSortToTop (viewId, colId, desc) {
      const view = this.views.find(v => v.id === viewId)
      const sort = { desc, order: 0 }
      
      // Set our col to the 'order 0' sort; any others will be pushed down as a lower tier sort
      const cols = [...view.groups || [], ...view.columns || []]
      cols.forEach(col => {
        if (col.id === colId) {
          col.sort = sort
        } else if (col.sort) {
          col.sort.order += 1
        }
      })
      this.queue.add('updateView', view, { viewId })
    },
    
    async deleteView (viewId) {
      await this.flushQueue()
      const authStore = useAuthStore()
      this.views = this.views.filter(v => v.id !== viewId)
      await authStore.delete(`tableviews/${viewId}/`)
    },
    
    async addView (viewData) {
      await this.flushQueue()
      const newView = await useAuthStore().post('tableviews/', viewData)
      this.views.push(newView)
      return newView
    },
    
    async changeView (viewData) {
      await this.flushQueue()
      const view = this.views.find(v => v.id === viewData.id)
      Object.assign(view, viewData)
      await useAuthStore().patch(`tableviews/${view.id}/`, view)
      return view
    }
    
  }
})
