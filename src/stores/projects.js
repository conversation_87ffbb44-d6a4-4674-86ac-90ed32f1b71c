import { useAuthStore } from '@/stores/auth'
import { defineStore } from 'pinia'
import { createBlankColumns, useTableStore } from './table'

export const useProjectsStore = defineStore('projects', {
  // persist: true,
  
  state: () => ({
    projects: [],
    orphanTables: []
  }),
  
  actions: {
    
    findTable (tableId) {
      // loop through all projects, and in .tables find the first that has matching .id
      for (const project of this.projects) {
        const table = project.tables.find(t => t.id === tableId)
        if (table) return table
      }
      
      // still here? check orphan tables
      return this.orphanTables.find(t => t.id === tableId)
    },
    
    async reload () {
      const authStore = useAuthStore()
      if (authStore.isLoggedIn) {
        // Get these two async
        const [projects, orphanTables] = await Promise.all([
          authStore.get('projects/'),
          authStore.get('tables/', { orphan: true })
        ])
        this.projects = projects
        this.orphanTables = orphanTables
      }
    },
    
    async createNew (data) {
      const authStore = useAuthStore()
      const r = await authStore.post('projects/', data)
      await this.reload()
      return r.id
    },
    
    async update (projectId, data) {
      await useAuthStore().patch(`projects/${projectId}/`, data)
      await this.reload()
      return projectId
    },
    
    async delete (projectId) {
      return await useAuthStore().delete(`projects/${projectId}/`)
    },
    
    /* Tables - this is to operate on any table (or create new ones); table.js store is for a
       specific active table loaded in the editor. It's a bit of duplication but they are all 1-line
       API calls
    */
    async _createNewTable (title, project, columns, rows) {
      const authStore = useAuthStore()
      const r = await authStore.post('tables/', { title, project, columns, rows })
      await this.reload()
      if (r) return r.id
    },
    
    async createNewBlankTable (title, project, numColumns, numRows) {
      const columns = createBlankColumns(numColumns)
      const rows = Array.from({ length: numRows }, (_, i) => ({ id: i }))
      return await this._createNewTable(title, project, columns, rows)
    },
    
    async createNewTable (title, project, columns, rows) {
      return await this._createNewTable(title, project, columns, rows)
    },
    
    async deleteTable (tableId) {
      return await useAuthStore().delete(`tables/${tableId}/`)
    },
    
    async editTable (tableId, title, project) {
      await useAuthStore().patch(`tables/${tableId}/`, { title, project })
      await this.reload()
      const tableStore = useTableStore()
      if (tableStore.id === tableId) {
        tableStore.title = title
        tableStore.project = project
      }
    },
    
    async moveTable (tableId, projectId) {
      await useAuthStore().patch(`tables/${tableId}/`, { project: projectId })
      await this.reload()
      const tableStore = useTableStore()
      if (tableStore.id === tableId) {
        tableStore.project = projectId
      }
    }
  }
})
