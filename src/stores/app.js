import { defineStore } from 'pinia'

/*
App store

Temporary store for global settings. Use prefs if we want to persist over settings.
*/
export const useAppStore = defineStore('app', {
  persist: false,
  state: () => ({
    // Stores selected column ids and row indexes in TableEditor
    selectedColumnIds: [],
    selectedRowIds: [],
    selectedCells: null,  // { cols: [], rows: [] }
    
    // Global flags that can be set/watched in any component if a modal is open
    modalIsOpen: false
    
  }),
  
  actions: {
    clearSelection () {
      this.selectedColumnIds = []
      this.selectedRowIds = []
      this.selectedCells = null
    }
  }
})
