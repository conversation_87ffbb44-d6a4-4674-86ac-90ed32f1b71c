import { defineStore } from 'pinia'
import { useTableStore } from './table'
import { useAuthStore } from './auth'
import { getChartType } from '@/charts/chartConfig.js'

// eslint-disable-next-line no-unused-vars

function createAxis (show = true, label = undefined) {
  const axis = {
    show,
    min: null,
    max: null
  }
  if (label !== undefined) axis.label = label
  return axis
}

export function createDataset (chartType = null, series = [], values = []) {
  /*
  If byRow:
  - If Pie:
   - series is a 1-item array for the row
   - values is an array of row IDs

  - Else:
   - series is an array of rows indexes
   - values is an array of column ids

  If !byRow:
   - If pie:
    - series is a 1-item array for the column
    - values is an array of IDs of rows

   - Else:
    - series is an array of column ids
    - values blank/unused (row restriction done fromRow toRow as array indexes
   */
  let ds = {
    chartType,
    series,
    values
  }
  if (chartType === 'pie') {
    ds.donutCutout = 0
  } else {
    ds.axis = createAxis()
    if (chartType === 'line') {
      ds.lineFill = false
    }
  }
  return ds
}

export const useChartStore = defineStore('chart', {
  state: () => ({
    id: null,
    title: '',
    
    // Is an id to a view, not an object
    view: undefined,
    
    // chart data
    data: {
      subtitle: '',
      
      // Whether datasets are picked at rows, not columns
      byRow: false,
      
      design: {
        colors: {
          system: 'step'
        }
      },
      
      // Only apply to non-pies
      axis: createAxis(),  // labels ('x') axis
      datasets: [], // array of up to 2 datasets
      rows: {}   /* {  // limit rows displayed
        from: undefined,
        to: undefined
      } */
    }
  }),
  
  getters: {
    
    // Concatenates together all of a certain property from all datasets - either 'indexes' or 'values'
    _getAllDatasetProps (state) {
      return (prop) => {
        return state.data.datasets.reduce((acc, dataset) => acc.concat(dataset[prop]), []).sort((a, b) => a - b)
      }
    },
    
    chartedColumns (state) {
      // Get array of all column indexes charted - flatten either values or series
      // Returns blank if chart is not actually saved
      if (!state.id) return []
      return this._getAllDatasetProps(state.data.byRow ? 'values' : 'series')
    },
    
    chartedRows (state) {
      // For byRow or piecharts byCol, this is set in values. Otherwise, return the range from/to
      if (!state.id) return []
      if (state.data.byRow || state.chartType === 'pie') {
        return this._getAllDatasetProps(state.data.byRow ? 'series' : 'values')
      } else {
        return state.data.rows
      }
    },
    
    isMixed (state) {
      return state.data.datasets.length === 2
    },
    
    chartType (state) {
      return getChartType(state.data.datasets)
    },
    
    labelColumnId (state) {
      // Get the column to use for labels; either the one set specifically on the chart, or a default set at the table
      if (state.data.axis.column !== undefined) {
        return state.data.axis.column
      }
      return useTableStore().labelColumnId
    },
    
    viewId (state) {
      return state.view
    }
  },
  
  actions: {
    
    /* loads the chart data from the existing table store */
    loadFromTable (id) {
      const tableStore = useTableStore()
      const chart = tableStore.charts.find(chart => chart.id === id)
      if (chart === undefined) throw Error(`No chart with id ${id} found.`)
      this.id = id
      this.$reset()
      
      // Don't remove, This is needed: or we take a reference to the original chart object; then when we edit, it updates
      // the table store's chart object. This is not what we want - it prevents us doing a revert later.
      this.$patch(JSON.parse(JSON.stringify(chart)))
    },
    
    addDataSet (chartType) {
      this.data.datasets.push(createDataset(chartType))
    },
    
    removeDataSet (index) {
      this.data.datasets.splice(index, 1)
    },
    
    async saveAsNew () {
      this.id = null
      return await this.save()
    },
    
    async save () {
      const authStore = useAuthStore()
      const tableStore = useTableStore()
      
      const payload = {
        table: tableStore.id,
        title: this.title,
        view: this.view,
        data: this.data
      }
      
      if (!this.id) {
        // We have issues with infinite recursion if we set the ID. This occurs when we save from TheChartPanel
        // couldn't work out the root of it - so instead we return it, and just router redirect.
        const r = await authStore.post('charts/', payload)
        return r.id
      } else {
        await authStore.patch(`charts/${this.id}/`, payload)
      }
    },
    
    async delete () {
      const authStore = useAuthStore()
      return await authStore.delete(`charts/${this.id}/`)
    }
  }
  
})
