import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { create<PERSON><PERSON>, setActiveP<PERSON> } from 'pinia'
import { useNotificationStore } from './notifications.js'

describe('Notification Store', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia())
    
    // Mock timers for testing auto-dismiss functionality
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  it('should add a notification', () => {
    const store = useNotificationStore()
    const notification = {
      message: 'Test notification',
      type: 'info'
    }
    
    store.add(notification)
    
    expect(store.notifications.length).toBe(1)
    expect(store.notifications[0].message).toBe('Test notification')
    expect(store.notifications[0].type).toBe('info')
    expect(store.notifications[0].id).toBeDefined()
  })
  
  it('should add a simple notification with info', () => {
    const store = useNotificationStore()
    
    store.info('Simple message')
    
    expect(store.notifications.length).toBe(1)
    expect(store.notifications[0].message).toBe('Simple message')
    expect(store.notifications[0].type).toBe('info')
  })
  
  it('should remove a notification', () => {
    const store = useNotificationStore()
    const notification = {
      message: 'Test notification',
      type: 'info'
    }
    
    store.add(notification)
    const id = store.notifications[0].id
    store.remove(id)
    
    expect(store.notifications.length).toBe(0)
  })
  
  it('should update an existing notification', () => {
    const store = useNotificationStore()
    store.add({
      message: 'Original message',
      type: 'info'
    })
    
    const id = store.notifications[0].id
    store.update(id, { message: 'Updated message', type: 'success' })
    
    expect(store.notifications[0].message).toBe('Updated message')
    expect(store.notifications[0].type).toBe('success')
  })
  
  it('should start a processing notification', () => {
    const store = useNotificationStore()
    
    const id = store.start('Processing task')
    
    expect(store.notifications.length).toBe(1)
    expect(store.notifications[0].message).toBe('Processing task')
    expect(store.notifications[0].status).toBe('processing')
    expect(store.notifications[0].type).toBe('info')
    expect(store.notifications[0].id).toBe(id)
  })
  
  it('should complete a processing notification with success', () => {
    const store = useNotificationStore()
    const id = store.start('Processing task')
    
    store.complete(id, 'Task completed', true)
    
    expect(store.notifications[0].message).toBe('Task completed')
    expect(store.notifications[0].status).toBe('complete')
    expect(store.notifications[0].type).toBe('success')
  })
  
  it('should complete a processing notification with error', () => {
    const store = useNotificationStore()
    const id = store.start('Processing task')
    
    store.complete(id, 'Task failed', false)
    
    expect(store.notifications[0].message).toBe('Task failed')
    expect(store.notifications[0].status).toBe('error')
    expect(store.notifications[0].type).toBe('error')
  })
  
  it('should auto-dismiss notifications after duration', () => {
    const store = useNotificationStore()
    
    store.add({
      message: 'Auto-dismiss test',
      type: 'info',
      duration: 3000
    })
    
    expect(store.notifications.length).toBe(1)
    
    // Fast-forward time
    vi.advanceTimersByTime(3000)
    
    expect(store.notifications.length).toBe(0)
  })
  
  it('should not auto-dismiss notifications with duration set to 0', () => {
    const store = useNotificationStore()
    
    store.add({
      message: 'No auto-dismiss test',
      type: 'info',
      duration: 0
    })
    
    // Fast-forward time
    vi.advanceTimersByTime(10000)
    
    expect(store.notifications.length).toBe(1)
  })
  
  it('should add to history when removing a notification', () => {
    const store = useNotificationStore()
    
    store.add({
      message: 'Test notification',
      type: 'info'
    })
    
    const id = store.notifications[0].id
    store.remove(id)
    
    expect(store.notifications.length).toBe(0)
    expect(store.history.length).toBe(1)
    expect(store.history[0].id).toBe(id)
  })
  
  it('should limit the number of active notifications', () => {
    const store = useNotificationStore()
    store.maxNotifications = 3
    
    // Add more notifications than the limit
    for (let i = 0; i < 5; i++) {
      store.add({
        message: `Notification ${i}`,
        type: 'info'
      })
    }
    
    // Should only keep the most recent ones
    expect(store.notifications.length).toBe(3)
    expect(store.notifications[0].message).toBe('Notification 2')
    expect(store.notifications[2].message).toBe('Notification 4')
    
    // The older ones should be in history
    expect(store.history.length).toBe(2)
    expect(store.history[0].message).toBe('Notification 0')
    expect(store.history[1].message).toBe('Notification 1')
  })
})
