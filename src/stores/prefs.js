import { defineStore } from 'pinia'

/*
Prefs store

This is used to store any temporary user preferences that should be persisted between sessions.

These are stored in localStorage (with pinia-persistedstate) but are not shared to the DB.
*/
export const usePrefsStore = defineStore('prefs', {
  persist: true,
  state: () => ({
    
    // Proportional split between chart and table
    chartSplit: 0.5,
    
    // Whether sidebar is open or closed; makes sense to persist this in the session
    siteSidebarStatus: 'opened',
    
    // sets the workspace to an organization ID or undefined for personal
    workspaceId: undefined
  })
})
