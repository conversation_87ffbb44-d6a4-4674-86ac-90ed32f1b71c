import { define<PERSON>tore } from 'pinia'

// Visual/semantic category of notification
export const notificationTypes = {
  INFO: 'info',
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  DEBUG: 'debug'  // Added debug type
}

// Lifecycle state of notification
export const notificationStatus = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETE: 'complete',
  ERROR: 'error'
}

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: [],
    history: [],
    maxNotifications: 5,
    defaultDuration: 2500, // 5 seconds
    nextId: 1,
    showDebugNotifications: import.meta.env.VITE_DEBUG === 'true' // Read from env
  }),
  getters: {
    activeNotifications: (state) => {
      // Filter out debug notifications unless showDebugNotifications is true
      return state.notifications.filter(notification =>
        notification.type !== notificationTypes.DEBUG || state.showDebugNotifications
      )
    },
    hasActiveNotifications: (state) => state.notifications.length > 0,
    
    getById: (state) => (id) => {
      return state.notifications.find(notification => notification.id === id)
    }
  },
  actions: {
    add (notification) {
      // Generate ID if not provided
      const id = notification.id || this.nextId++
      // Set defaults for optional fields
      const newNotification = {
        id,
        type: notificationTypes.INFO,
        status: notificationStatus.PENDING,
        dismissible: true,
        duration: this.defaultDuration,
        timestamp: new Date(),
        autoClose: true,
        ...notification
      }
      
      // Always add to notifications array (even debug ones)
      this.notifications.push(newNotification)
      
      // Set up auto-dismiss if duration > 0 and not a processing notification
      if (newNotification.duration > 0 && newNotification.autoClose &&
        newNotification.status !== notificationStatus.PROCESSING) {
        setTimeout(() => {
          this.remove(id)
        }, newNotification.duration)
      }
      
      // Limit the number of visible notifications
      if (this.activeNotifications.length > this.maxNotifications) {
        // Find the oldest non-debug notification or the oldest if all are debug
        const oldestIndex = this.notifications.findIndex(n =>
          n.type !== notificationTypes.DEBUG || this.showDebugNotifications
        )
        
        if (oldestIndex !== -1) {
          const oldestNotification = this.notifications[oldestIndex]
          this.addToHistory(oldestNotification)
          this.notifications.splice(oldestIndex, 1)
        }
      }
      
      return id
    },
    
    info (message) {
      return this.add({ message })
    },
    
    // Add a debug notification - always goes to history, only shows if debug is enabled
    debug (message, details = null) {
      return this.add({
        message,
        details,
        type: notificationTypes.DEBUG,
        duration: this.showDebugNotifications ? this.defaultDuration : 0
      })
    },
    
    // Add an error notification with optional details
    error (message, details = null) {
      return this.add({
        message,
        details,
        type: notificationTypes.ERROR,
        duration: 8000 // Longer duration for errors
      })
    },
    
    // Helper to format API errors
    apiError (message, error) {
      let details = null
      
      // Extract error details from API response
      if (error?.response?.data) {
        if (typeof error.response.data === 'string') {
          details = error.response.data
        } else if (error.response.data.detail) {
          details = error.response.data.detail
        } else if (error.response.data.message) {
          details = error.response.data.message
        } else {
          // Try to stringify the error data
          try {
            details = JSON.stringify(error.response.data)
          } catch (e) {
            details = 'Unknown error format'
          }
        }
      } else if (error?.message) {
        details = error.message
      }
      
      return this.error(message, details)
    },
    
    start (message, options = {}) {
      return this.add({
        message,
        type: notificationTypes.INFO, // Default type for processing is INFO
        status: notificationStatus.PROCESSING,
        duration: 0,
        dismissible: false,
        autoClose: false,
        ...options
      })
    },
    
    complete (id, message, success = true, details = null) {
      const notification = this.getById(id)
      if (!notification) return
      
      this.update(id, {
        type: success ? notificationTypes.SUCCESS : notificationTypes.ERROR,
        status: success ? notificationStatus.COMPLETE : notificationStatus.ERROR,
        message: message || notification.message,
        details: details || notification.details,
        dismissible: true,
        autoClose: true
      })
      
      // Auto-close after completion
      setTimeout(() => {
        this.remove(id)
      }, this.defaultDuration)
    },
    
    update (id, updates) {
      const index = this.notifications.findIndex(n => n.id === id)
      
      if (index !== -1) {
        this.notifications[index] = {
          ...this.notifications[index],
          ...updates
        }
      }
    },
    
    remove (id) {
      const index = this.notifications.findIndex(n => n.id === id)
      
      if (index !== -1) {
        const notification = this.notifications[index]
        this.addToHistory(notification)
        this.notifications.splice(index, 1)
      }
    },
    
    removeAll () {
      // Move all to history
      this.notifications.forEach(notification => {
        this.addToHistory(notification)
      })
      
      // Clear the notifications array
      this.notifications = []
    },
    
    addToHistory (notification) {
      // Add to history and limit history size
      this.history.push({ ...notification, dismissedAt: new Date() })
      
      // Limit history to last 50 notifications
      if (this.history.length > 50) {
        this.history.shift()
      }
    }
    
  }
})
