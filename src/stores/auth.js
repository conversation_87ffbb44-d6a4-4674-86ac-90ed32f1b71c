import { defineStore } from 'pinia'
import { jwtDecode } from 'jwt-decode'
import { usePrefsStore } from '@/stores/prefs.js'

import { api } from '@/utils/api/api.js'
import { createConnection } from '@/db/connection.js'

export const authStatusCodes = {
  offline: 'offline',
  loading: 'loading',
  loggedIn: 'loggedIn',
  loggedOut: 'loggedOut',
  serverDown: 'serverDown'
}

export const useAuthStore = defineStore('auth', {
  state: () => {
    return {
      authStatus: authStatusCodes.loading,
      accessToken: '',
      refreshToken: localStorage.getItem('refreshToken') || '',
      user: {},
      organizations: [],
      
      // stores our dbConn connection - a single connection is used for the entire app
      db: undefined,
      
      // Stores an error response - this triggers ModalError to show
      error: undefined
    }
  },
  
  getters: {
    isLoggedIn: (state) => state.authStatus === authStatusCodes.loggedIn,
    isLoggedOut: (state) => state.authStatus === authStatusCodes.loggedOut,
    isOffline: (state) => state.authStatus === authStatusCodes.offline,
    isServerDown: (state) => state.authStatus === authStatusCodes.serverDown,
    
    workspace: (state) => {
      const prefsStore = usePrefsStore()
      return state.organizations.find(org => org.id === prefsStore.workspaceId)
    },
    
    // Returns true if the app is ready to be used - logged in (or offline/server down) and database is open
    appIsReady: (state) => {
      return state.db && [authStatusCodes.loggedIn, authStatusCodes.offline, authStatusCodes.serverDown].includes(state.authStatus)
    }
    
  },
  
  actions: {
    setToken (accessToken, refreshToken) {
      this.accessToken = accessToken
      this.refreshToken = refreshToken
      localStorage.setItem('refreshToken', refreshToken)
    },
    
    hasTokenExpired () {
      return !this.accessToken || jwtDecode(this.accessToken).exp < (Date.now() / 1000)
    },
    
    setAuthErrorStatus (error) {
      if (error.response && error.response.status === 401) {
        // Unauthorized; we assume the token has expired; go to log in
        this.authStatus = authStatusCodes.loggedOut
      } else if (error.response) {
        // Don't change the authStatus, just flag the error
        this.error = error.response.data
      } else if (error.request) {
        this.authStatus = authStatusCodes.serverDown
      } else {
        this.authStatus = authStatusCodes.offline
      }
    },
    
    async createDb () {
      // This is where you can set database log/logging true or false - leave this comment to find it later!
      const conn = createConnection()
      await conn.connect()
      this.db = conn
    },
    
    async destroyDb () {
      if (this.db) {
        await this.db.disconnect()
      }
    },
    
    async getNewToken () {
      await api.post('jwt/refresh/', {
        refresh: this.refreshToken
      }).then(response => {
        this.setToken(response.data.access, response.data.refresh)
      }).catch(this.setAuthErrorStatus)
    },
    
    async login (username, password) {
      await api.post('jwt/create/', {
        username,
        password
      }).then(response => {
        this.setToken(response.data.access, response.data.refresh)
        this.authStatus = authStatusCodes.loggedIn
      }).catch(this.setAuthErrorStatus)
    },
    
    clearAuth () {
      // If logging out, make sure to flush save queues first!
      this.authStatus = authStatusCodes.loggedOut
      this.setToken('', '')
    },
    
    async checkAuth () {
      // Refreshes token if necessary
      if (!this.refreshToken) return
      if (!this.hasTokenExpired()) return
      await this.getNewToken()
    },
    
    async getAuthorization () {
      if (this.isServerDown || this.isOffline) return
      await this.checkAuth()
      return {
        Authorization: `JWT ${this.accessToken}`,
        Workspace: this.workspace ? this.workspace.id : ''
      }
    },
    
    async get (endPoint, params = {}) {
      const headers = await this.getAuthorization()
      if (this.authStatus !== authStatusCodes.loading && this.authStatus !== authStatusCodes.loggedIn) return
      return await api.get(endPoint, { params, headers })
        .then(response => {
          return response.data
        })
        .catch(this.setAuthErrorStatus)
    },
    
    async post (endPoint, data) {
      const headers = await this.getAuthorization()
      if (this.authStatus !== authStatusCodes.loggedIn) return
      return await api.post(endPoint, data, headers)
        .then(response => {
          return response.data
        })
        .catch(this.setAuthErrorStatus)
    },
    
    async patch (endPoint, data) {
      const headers = await this.getAuthorization()
      if (this.authStatus !== authStatusCodes.loggedIn) return
      return await api.patch(endPoint, data, headers)
        .then(response => {
          return response.data
        })
        .catch(this.setAuthErrorStatus)
    },
    
    async delete (endPoint) {
      const headers = await this.getAuthorization()
      if (this.authStatus !== authStatusCodes.loggedIn) return
      return await api.delete(endPoint, headers)
        .then(response => {
          return response.data
        })
        .catch(this.setAuthErrorStatus)
    },
    
    async loadProfile () {
      const data = await this.get('profile/')
      if (data) {
        this.authStatus = authStatusCodes.loggedIn
        this.organizations = data.organizations
        this.user = data.user
      }
    }
  }
  
})
