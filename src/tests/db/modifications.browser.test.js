// noinspection DuplicatedCode

import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/basic/table.json'
import tableRows from '@/tests/db/testdata/basic/table-rows.json'
import { Big } from 'big.js'
import { filterQuerySpec, getQuerySpec } from '@/db/query/utils.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

describe('Table modification tests', () => {
  let conn
  let table
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.disconnect()
    }
  })
  
  describe('Table Operations', () => {
    // Helper function to verify all rows have sequential positions
    async function verifySequentialPositions () {
      // Just need one column to verify order
      const allRows = await table.rowObjects([0])
      
      // Verify all rows have sequential positions
      allRows.forEach((row, index) => {
        expect(row.pos).toEqual(index)
      })
      
      return allRows // Return rows for additional assertions
    }
    
    const tableId = tableDef.id
    
    beforeAll(async () => {
      await conn.buildTable(tableId, tableDef.columns, tableRows)
      table = conn.tables[tableId]
    })
    
    afterAll(async () => {
      await conn.dropTable(tableId)
    })
    
    describe('Row Update Operations', () => {
      const querySpec = getQuerySpec(tableDef.columns)
      it('should update cost value for a specific row', async () => {
        const newValue = '20.00'
        const newClean = Big(newValue)
        const rowId = tableRows[0].id
        
        await table.updateRows([{
          rowId,
          cells: [{
            columnId: 9,
            rawValue: newValue,
            cleanValue: newClean,
            type: 'currency'
          }]
        }])
        
        const spec = filterQuerySpec(querySpec, { rowIds: [rowId] })
        const result = await table.rowObjects([9], spec)
        expect(result[0].c9).toEqual(newClean)
      })
      it('should update multiple cells in a single row', async () => {
        const numColumn = 1  // "Sum" column (number)
        const currencyColumn = 9  // "Costs" column (currency)
        const rowId = 0  // First row ID
        
        await table.updateRows([{
          rowId,
          cells: [
            {
              columnId: numColumn,
              rawValue: '5000',
              cleanValue: 5000,
              type: 'number'
            },
            {
              columnId: currencyColumn,
              rawValue: '25.50',
              cleanValue: Big('25.50'),
              type: 'currency'
            }
          ]
        }])
        
        const spec = filterQuerySpec(querySpec, { rowIds: [rowId] })
        const result = await table.rowObjects([numColumn, currencyColumn], spec)
        
        expect(result[0].c1).toEqual(Big('5000'))
        expect(result[0].c9).toEqual(Big('25.50'))
      })
      
      it('should update multiple rows with different values', async () => {
        const rowIds = [1, 2]  // Second and third rows
        
        await table.updateRows([
          {
            rowId: rowIds[0],
            cells: [{
              columnId: 2,
              rawValue: '100',
              cleanValue: 100,
              type: 'number'
            }]
          },
          {
            rowId: rowIds[1],
            cells: [{
              columnId: 2,
              rawValue: '200',
              cleanValue: 200,
              type: 'number'
            }]
          }
        ])
        
        const spec = filterQuerySpec(querySpec, { rowIds })
        const result = await table.rowObjects([2], spec)
        
        expect(result[0].c2).toEqual(Big('100'))
        expect(result[1].c2).toEqual(Big('200'))
      })
      
      it('should update multiple rows with the same column values', async () => {
        const rowIds = [3, 4, 5]  // Fourth, fifth, and sixth rows
        const newValue = 999
        
        await table.updateRows(rowIds.map(rowId => ({
          rowId,
          cells: [{
            columnId: 3,
            rawValue: String(newValue),
            cleanValue: newValue,
            type: 'number'
          }]
        })))
        
        const spec = filterQuerySpec(querySpec, { rowIds })
        const result = await table.rowObjects([3], spec)
        
        result.forEach(row => {
          expect(row.c3).toEqual(Big('999'))
        })
      })
      
      it('should handle updates with different data types correctly', async () => {
        const dateCol = 0    // "Date" column (datetime)
        const numCol = 4     // "GR" column (number)
        const currencyCol = 9  // "Costs" column (currency)
        const rowId = 6  // Seventh row
        
        const newDate = 1710374400000  // 2024-03-14
        
        await table.updateRows([{
          rowId,
          cells: [
            {
              columnId: dateCol,
              rawValue: String(newDate),
              cleanValue: newDate,
              type: 'datetime'
            },
            {
              columnId: numCol,
              rawValue: '42.5',
              cleanValue: 42.5,
              type: 'number'
            },
            {
              columnId: currencyCol,
              rawValue: '99.99',
              cleanValue: Big('99.99'),
              type: 'currency'
            }
          ]
        }])
        
        const spec = filterQuerySpec(querySpec, { rowIds: [rowId] })
        const result = await table.rowObjects([dateCol, numCol, currencyCol], spec)
        
        expect(result[0].c0).toBe(newDate)
        expect(result[0].c4).toEqual(Big('42.5'))
        expect(result[0].c9).toEqual(Big('99.99'))
      })
      
      it('should handle large batch updates efficiently', async () => {
        const updates = []
        
        // Create updates for all rows in the test data
        for (let i = 0; i < tableRows.length; i++) {
          updates.push({
            rowId: tableRows[i].id,
            cells: [{
              columnId: [5],
              rawValue: String(i * 10),
              cleanValue: i * 10,
              type: 'number'
            }]
          })
        }
        
        await table.updateRows(updates)
        
        const rowIds = updates.map(update => update.rowId)
        const spec = filterQuerySpec(querySpec, { rowIds })
        const result = await table.rowObjects([5], spec)
        
        for (let i = 0; i < result.length; i++) {
          const rowId = rowIds[i]
          const rowIndex = tableRows.findIndex(r => r.id === rowId)
          expect(result[i].c5).toEqual(Big(rowIndex * 10))
        }
      })
      
      it('should handle null values correctly', async () => {
        const rowId = 7  // Eighth row
        await table.updateRows([{
          rowId,
          cells: [{
            columnId: 4,
            rawValue: null,
            cleanValue: null,
            type: 'number'
          }]
        }])
        
        const spec = filterQuerySpec(querySpec, { rowIds: [rowId] })
        
        const result = await table.rowObjects([4], spec)
        expect(result[0].c4).toBeNull()
      })
      
      it('should update calculated columns and verify their values', async () => {
        // Column 6 is a calculated "Sum" column based on columns 1-5
        const sourceColumns = [1, 2, 3, 4, 5]
        const calcColumn = [6]
        const rowId = 8  // Ninth row
        
        // Update all source columns with known values
        const values = [100, 200, 300, 400, 500]
        const expectedSum = 1500  // Sum of all values
        
        await table.updateRows([{
          rowId,
          cells: sourceColumns.map((col, index) => ({
            columnId: col,
            rawValue: String(values[index]),
            cleanValue: values[index],
            type: 'number'
          }))
        }])
        
        // Fetch both source columns and calculated column
        const spec = filterQuerySpec(querySpec, { rowIds: [rowId] })
        const result = await table.rowObjects([...sourceColumns, ...calcColumn], spec)
        
        // Verify source columns were updated correctly
        sourceColumns.forEach((col, index) => {
          expect(result[0][`c${col}`]).toEqual(Big(values[index]))
        })
        
        // Verify calculated column has correct value
        expect(result[0][`c${calcColumn}`]).toEqual(Big(expectedSum))
      })
      
      it('should update multiple cells across multiple rows with mixed data types', async () => {
        const dateCol = 0    // Date column
        const numCol = 1     // Sum column
        const currencyCol = 9  // Costs column
        const rowIds = [0, 1]  // First and second rows
        
        const newDates = [1709596800000, 1709683200000]  // 2024-03-05, 2024-03-06
        
        await table.updateRows([
          {
            rowId: rowIds[0],
            cells: [
              {
                columnId: dateCol,
                rawValue: String(newDates[0]),
                cleanValue: newDates[0],
                type: 'datetime'
              },
              {
                columnId: numCol,
                rawValue: '1111',
                cleanValue: 1111,
                type: 'number'
              },
              {
                columnId: currencyCol,
                rawValue: '111.11',
                cleanValue: Big('111.11'),
                type: 'currency'
              }
            ]
          },
          {
            rowId: rowIds[1],
            cells: [
              {
                columnId: dateCol,
                rawValue: String(newDates[1]),
                cleanValue: newDates[1],
                type: 'datetime'
              },
              {
                columnId: numCol,
                rawValue: '2222',
                cleanValue: 2222,
                type: 'number'
              },
              {
                columnId: currencyCol,
                rawValue: '222.22',
                cleanValue: Big('222.22'),
                type: 'currency'
              }
            ]
          }
        ])
        
        const spec = filterQuerySpec(querySpec, { rowIds })
        const result = await table.rowObjects([dateCol, numCol, currencyCol], spec)
        
        expect(result[0].c0).toBe(newDates[0])
        expect(result[0].c1).toEqual(Big(1111))
        expect(result[0].c9).toEqual(Big('111.11'))
        
        expect(result[1].c0).toBe(newDates[1])
        expect(result[1].c1).toEqual(Big(2222))
        expect(result[1].c9).toEqual(Big('222.22'))
      })
    })
    
    describe('Row Move Operations', () => {
      
      it('should move a single row to a new position', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowToMoveId = initialRows[3].id
        
        // Move row from position 3 to position 0 (top)
        await table.moveRows([rowToMoveId], 0)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify row is now at position 0
        expect(allRows[0].id).toEqual(rowToMoveId)
      })
      
      it('should move a single row down in the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowToMoveId = initialRows[1].id
        const targetPosition = 5
        
        // When moving down, the target position needs to account for the row being removed first
        await table.moveRows([rowToMoveId], targetPosition + 1)
        const allRows = await verifySequentialPositions()
        expect(allRows[targetPosition].id).toEqual(rowToMoveId)
      })
      
      it('should move multiple consecutive rows to a new position', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowIds = [initialRows[2].id, initialRows[3].id]
        const targetPosition = 0
        
        // Move rows to the beginning
        await table.moveRows(rowIds, targetPosition)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify rows are now at the beginning
        expect(allRows[0].id).toEqual(rowIds[0])
        expect(allRows[1].id).toEqual(rowIds[1])
      })
      
      it('should move multiple consecutive rows down in the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowIds = [initialRows[1].id, initialRows[2].id]
        const targetPosition = 5
        
        // When moving down, the target position needs to account for the rows being removed first
        await table.moveRows(rowIds, targetPosition + 2)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify rows are now at the target position
        expect(allRows[targetPosition].id).toEqual(rowIds[0])
        expect(allRows[targetPosition + 1].id).toEqual(rowIds[1])
      })
      
      it('should move multiple non-consecutive rows to a new position', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowIds = [initialRows[1].id, initialRows[3].id, initialRows[5].id]
        const targetPosition = 0
        
        // Move rows to the beginning
        await table.moveRows(rowIds, targetPosition)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify rows are now at the beginning
        expect(allRows[0].id).toEqual(rowIds[0])
        expect(allRows[1].id).toEqual(rowIds[1])
        expect(allRows[2].id).toEqual(rowIds[2])
      })
      
      it('should move multiple non-consecutive rows down in the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowIds = [initialRows[1].id, initialRows[3].id, initialRows[5].id]
        const targetPosition = 6
        await table.moveRows(rowIds, targetPosition + rowIds.length)
        const allRows = await verifySequentialPositions()
        expect(allRows[targetPosition].id).toEqual(rowIds[0])
        expect(allRows[targetPosition + 1].id).toEqual(rowIds[1])
        expect(allRows[targetPosition + 2].id).toEqual(rowIds[2])
      })
      
      it('should move multiple non-consecutive up in the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowIds = [initialRows[1].id, initialRows[3].id, initialRows[5].id]
        const targetPosition = 0
        await table.moveRows(rowIds, targetPosition)
        const allRows = await verifySequentialPositions()
        expect(allRows[targetPosition].id).toEqual(rowIds[0])
        expect(allRows[targetPosition + 1].id).toEqual(rowIds[1])
        expect(allRows[targetPosition + 2].id).toEqual(rowIds[2])
      })
      
      it('should handle moving rows to their current position', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowIds = [initialRows[3].id, initialRows[4].id, initialRows[5].id]
        const targetPosition = 3
        
        // Move these rows to the same starting position
        await table.moveRows(rowIds, targetPosition)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify rows are still at the same positions
        expect(allRows[3].id).toEqual(rowIds[0])
        expect(allRows[4].id).toEqual(rowIds[1])
        expect(allRows[5].id).toEqual(rowIds[2])
      })
      
      it('should move rows from the end to the beginning', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const rowCount = initialRows.length
        const lastThreeRowIds = initialRows.slice(rowCount - 3).map(row => row.id)
        
        // Move these rows to the beginning
        await table.moveRows(lastThreeRowIds, 0)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify the last three rows are now at the beginning
        expect(allRows[0].id).toEqual(lastThreeRowIds[0])
        expect(allRows[1].id).toEqual(lastThreeRowIds[1])
        expect(allRows[2].id).toEqual(lastThreeRowIds[2])
      })
      
      it('should move rows from the beginning to the end', async () => {
        // Get initial state
        
        const initialRows = await verifySequentialPositions()
        const lastIndex = initialRows.length - 1
        const firstThreeRowIds = initialRows.slice(0, 3).map(row => row.id)
        await table.moveRows(firstThreeRowIds, lastIndex + 3)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify the ids of the last three rows are the ids
        expect(allRows[lastIndex - 2].id).toEqual(firstThreeRowIds[0])
        expect(allRows[lastIndex - 1].id).toEqual(firstThreeRowIds[1])
        expect(allRows[lastIndex].id).toEqual(firstThreeRowIds[2])
      })
      
      it('should handle edge case of moving a single row to its current position', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const middleIndex = Math.floor(initialRows.length / 2)
        const middleRowId = initialRows[middleIndex].id
        
        // Move this row to its current position
        await table.moveRows([middleRowId], middleIndex)
        
        // Get all rows and verify positions
        const allRows = await verifySequentialPositions()
        
        // Verify the row is still at the same position
        expect(allRows[middleIndex].id).toEqual(middleRowId)
      })
    })
    
    describe('Row Insert Operations', () => {
      
      it('should insert a single row at the beginning of the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const initialCount = initialRows.length
        
        // Create a new row with a unique ID
        const newRowId = initialCount + 100
        const newRow = {
          id: newRowId,
          c0: '1710374400000', // Date value
          c1: '123',           // Number value
          c9: '15.75'          // Currency value
        }
        
        // Insert at position 0 (beginning)
        const querySpec = getQuerySpec(tableDef.columns)
        await table.insertRows([newRow], 0)
        
        // Verify positions are still sequential
        const updatedRows = await verifySequentialPositions()
        
        // Verify row count increased
        expect(updatedRows.length).toBe(initialCount + 1)
        
        // Verify new row is at position 0
        const spec = filterQuerySpec(querySpec, { posRange: [0, 1] })
        const firstRow = await table.rowObjects([0, 1, 9], spec)
        expect(firstRow[0].id).toBe(newRowId)
        expect(firstRow[0].c0).toBe(Number(newRow.c0))
        expect(firstRow[0].c1).toEqual(Big(newRow.c1))
        expect(firstRow[0].c9).toEqual(Big(newRow.c9))
      })
      
      it('should insert a single row in the middle of the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const initialCount = initialRows.length
        const middlePosition = Math.floor(initialCount / 2)
        
        // Create a new row with a unique ID
        const newRowId = initialCount + 200
        const newRow = {
          id: newRowId,
          c0: '1710460800000', // Date value
          c1: '456',           // Number value
          c9: '25.50'          // Currency value
        }
        
        // Insert at middle position
        await table.insertRows([newRow], middlePosition)
        
        // Verify positions are still sequential
        const updatedRows = await verifySequentialPositions()
        
        // Verify row count increased
        expect(updatedRows.length).toBe(initialCount + 1)
        
        // Verify new row is at the middle position
        const querySpec = getQuerySpec(tableDef.columns)
        const spec = filterQuerySpec(querySpec, { posRange: [middlePosition, middlePosition + 1] })
        const middleRow = await table.rowObjects([0, 1, 9], spec)
        expect(middleRow[0].id).toBe(newRowId)
        expect(middleRow[0].c0).toBe(Number(newRow.c0))
        expect(middleRow[0].c1).toEqual(Big(newRow.c1))
        expect(middleRow[0].c9).toEqual(Big(newRow.c9))
      })
      
      it('should insert a single row at the end of the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const initialCount = initialRows.length
        
        // Create a new row with a unique ID
        const newRowId = initialCount + 300
        const newRow = {
          id: newRowId,
          c0: '1710547200000', // Date value
          c1: '789',           // Number value
          c9: '35.25'          // Currency value
        }
        
        // Insert at the end position
        await table.insertRows([newRow], initialCount)
        
        // Verify positions are still sequential
        const updatedRows = await verifySequentialPositions()
        
        // Verify row count increased
        expect(updatedRows.length).toBe(initialCount + 1)
        
        // Verify new row is at the end position
        const querySpec = getQuerySpec(tableDef.columns)
        const spec = filterQuerySpec(querySpec, { posRange: [initialCount, initialCount + 1] })
        const lastRow = await table.rowObjects([0, 1, 9], spec)
        expect(lastRow[0].id).toBe(newRowId)
        expect(lastRow[0].c0).toBe(Number(newRow.c0))
        expect(lastRow[0].c1).toEqual(Big(newRow.c1))
        expect(lastRow[0].c9).toEqual(Big(newRow.c9))
      })
      
      it('should insert multiple rows at the beginning of the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const initialCount = initialRows.length
        
        // Create new rows with unique IDs
        const newRows = [
          {
            id: initialCount + 400,
            c0: '1710633600000', // Date value
            c1: '111',           // Number value
            c9: '11.11'          // Currency value
          },
          {
            id: initialCount + 401,
            c0: '1710720000000', // Date value
            c1: '222',           // Number value
            c9: '22.22'          // Currency value
          },
          {
            id: initialCount + 402,
            c0: '1710806400000', // Date value
            c1: '333',           // Number value
            c9: '33.33'          // Currency value
          }
        ]
        
        // Insert at position 0 (beginning)
        await table.insertRows(newRows, 0)
        
        // Verify positions are still sequential
        const updatedRows = await verifySequentialPositions()
        
        // Verify row count increased
        expect(updatedRows.length).toBe(initialCount + newRows.length)
        
        // Verify new rows are at the beginning with correct order
        const querySpec = getQuerySpec(tableDef.columns)
        const spec = filterQuerySpec(querySpec, { posRange: [0, newRows.length] })
        const firstRows = await table.rowObjects([0, 1, 9], spec)
        
        newRows.forEach((newRow, index) => {
          expect(firstRows[index].id).toBe(newRow.id)
          expect(firstRows[index].c0).toBe(Number(newRow.c0))
          expect(firstRows[index].c1).toEqual(Big(newRow.c1))
          expect(firstRows[index].c9).toEqual(Big(newRow.c9))
        })
      })
      
      it('should insert multiple rows in the middle of the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const initialCount = initialRows.length
        const middlePosition = Math.floor(initialCount / 2)
        
        // Create new rows with unique IDs
        const newRows = [
          {
            id: initialCount + 500,
            c0: '1710892800000', // Date value
            c1: '444',           // Number value
            c9: '44.44'          // Currency value
          },
          {
            id: initialCount + 501,
            c0: '1710979200000', // Date value
            c1: '555',           // Number value
            c9: '55.55'          // Currency value
          }
        ]
        
        // Insert at middle position
        await table.insertRows(newRows, middlePosition)
        
        // Verify positions are still sequential
        const updatedRows = await verifySequentialPositions()
        
        // Verify row count increased
        expect(updatedRows.length).toBe(initialCount + newRows.length)
        
        // Verify new rows are at the middle position with correct order
        const querySpec = getQuerySpec(tableDef.columns)
        const spec = filterQuerySpec(querySpec, { posRange: [middlePosition, middlePosition + newRows.length] })
        const middleRows = await table.rowObjects([0, 1, 9], spec)
        
        newRows.forEach((newRow, index) => {
          expect(middleRows[index].id).toBe(newRow.id)
          expect(middleRows[index].c0).toBe(Number(newRow.c0))
          expect(middleRows[index].c1).toEqual(Big(newRow.c1))
          expect(middleRows[index].c9).toEqual(Big(newRow.c9))
        })
      })
      
      it('should insert multiple rows at the end of the table', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const initialCount = initialRows.length
        
        // Create new rows with unique IDs
        const newRows = [
          {
            id: initialCount + 600,
            c0: '1711065600000', // Date value
            c1: '666',           // Number value
            c9: '66.66'          // Currency value
          },
          {
            id: initialCount + 601,
            c0: '1711152000000', // Date value
            c1: '777',           // Number value
            c9: '77.77'          // Currency value
          }
        ]
        
        // Insert at the end position
        await table.insertRows(newRows, initialCount)
        
        // Verify positions are still sequential
        const updatedRows = await verifySequentialPositions()
        
        // Verify row count increased
        expect(updatedRows.length).toBe(initialCount + newRows.length)
        
        // Verify new rows are at the end position with correct order
        const querySpec = getQuerySpec(tableDef.columns)
        const spec = filterQuerySpec(querySpec, { posRange: [initialCount, initialCount + newRows.length] })
        const lastRows = await table.rowObjects([0, 1, 9], spec)
        
        newRows.forEach((newRow, index) => {
          expect(lastRows[index].id).toBe(newRow.id)
          expect(lastRows[index].c0).toBe(Number(newRow.c0))
          expect(lastRows[index].c1).toEqual(Big(newRow.c1))
          expect(lastRows[index].c9).toEqual(Big(newRow.c9))
        })
      })
      
      it('should handle inserting rows with minimal column data', async () => {
        // Get initial state
        const initialRows = await verifySequentialPositions()
        const initialCount = initialRows.length
        
        // Create a new row with only ID and one column
        const newRowId = initialCount + 700
        const newRow = {
          id: newRowId,
          c1: '999' // Only providing one column value
        }
        
        // Insert at position 0
        await table.insertRows([newRow], 0)
        
        // Verify positions are still sequential
        const updatedRows = await verifySequentialPositions()
        
        // Verify row count increased
        expect(updatedRows.length).toBe(initialCount + 1)
        
        // Verify new row is at position 0 with the provided column value
        const querySpec = getQuerySpec(tableDef.columns)
        const spec = filterQuerySpec(querySpec, { posRange: [0, 1] })
        const firstRow = await table.rowObjects([1], spec)
        expect(firstRow[0].id).toBe(newRowId)
        expect(firstRow[0].c1).toEqual(Big(newRow.c1))
      })
      
      it('should maintain correct positions after multiple insert operations', async () => {
        // Get initial state
        await verifySequentialPositions()
        
        // Perform multiple insert operations
        const firstRow = { id: 1001, c1: '101' }
        const secondRow = { id: 1002, c1: '102' }
        const thirdRow = { id: 1003, c1: '103' }
        
        // Insert at beginning
        await table.insertRows([firstRow], 0)
        
        // Insert in middle
        const midPosition = 5
        await table.insertRows([secondRow], midPosition)
        
        // Get current count
        const currentRows = await verifySequentialPositions()
        const currentCount = currentRows.length
        
        // Insert at end
        await table.insertRows([thirdRow], currentCount)
        
        // Verify positions are still sequential
        const finalRows = await verifySequentialPositions()
        
        // Verify specific positions
        const checkRows = await table.rowObjects([1])
        
        // First row should be at position 0
        expect(checkRows[0].id).toBe(firstRow.id)
        expect(checkRows[0].c1).toEqual(Big(firstRow.c1))
        
        expect(checkRows[midPosition].id).toBe(secondRow.id)
        expect(checkRows[midPosition].c1).toEqual(Big(secondRow.c1))
        
        // Third row should be at the end
        expect(checkRows[finalRows.length - 1].id).toBe(thirdRow.id)
        expect(checkRows[finalRows.length - 1].c1).toEqual(Big(thirdRow.c1))
      })
    })
  })
  
  describe('Column Operations', () => {
    const tableId = tableDef.id
    
    beforeEach(async () => {
      await conn.buildTable(tableId, tableDef.columns, tableRows)
      table = conn.tables[tableId]
    })
    
    afterAll(async () => {
      await conn.dropTable(tableId)
    })
    
    it('should add a new column to the table', async () => {
      const newColumn = {
        id: 1000,
        name: 'New Column',
        type: 'number'
      }
      await table.addColumn(newColumn.id, newColumn.type)
      
      // Insert a value to test the new column
      await table.updateRows([{
        rowId: tableRows[0].id,
        cells: [{
          columnId: 1000,
          rawValue: '42',
          cleanValue: 42,
          type: 'number'
        }]
      }])
      
      // Verify we can query the new column
      let spec = getQuerySpec([...tableDef.columns, newColumn])
      spec = filterQuerySpec(spec, { rowIds: [tableRows[0].id] })
      const result = await table.rowObjects([newColumn.id], spec)
      expect(result[0][`c${newColumn.id}`]).toEqual(Big('42'))
    })
    
    it('should remove a column from the table', async () => {
      // First add a column to remove
      const tempColumn = {
        id: 1000,
        name: 'Temp Column',
        type: 'text',
        calc: false
      }
      
      await table.addColumn(tempColumn.id, tempColumn.type)
      await table.removeColumn(1000)
      
      // Try to query it (should not throw but column won't be in results)
      const querySpec = getQuerySpec(tableDef.columns)
      const spec = filterQuerySpec(querySpec, { rowIds: [tableRows[0].id] })
      const result = await table.rowObjects(spec.columns.map(c => c.id), spec)
      expect(result[0].c1000).toBeUndefined()
    })
    
    it('should change a column type', async () => {
      // First add a column to change
      const changeColumn = {
        id: 1000,
        name: 'Change Column',
        type: 'text'
      }
      await table.addColumn(changeColumn.id, changeColumn.type)
      
      // Add some text data
      await table.updateRows([{
        rowId: tableRows[0].id,
        cells: [{
          columnId: 1000,
          rawValue: '123',
          cleanValue: '123',
          type: 'text'
        }]
      }])
      
      // Change column type to number
      changeColumn.type = 'number'
      await table.changeColumn(1000, 'number', {})
      
      // Update with numeric data
      await table.updateRows([{
        rowId: tableRows[0].id,
        cells: [{
          columnId: 1000,
          rawValue: '456',
          cleanValue: 456,
          type: 'number'
        }]
      }])
      
      // Verify we can query as number
      const querySpec = getQuerySpec([...tableDef.columns, changeColumn])
      const spec = filterQuerySpec(querySpec, { rowIds: [tableRows[0].id] })
      const result = await table.rowObjects([1000], spec)
      expect(result[0].c1000).toEqual(Big('456'))
    })
  })
})
