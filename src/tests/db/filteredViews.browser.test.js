// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/filteredViews/table.json'
import tableRows from '@/tests/db/testdata/filteredViews/rows.json'
import { getQuerySpec, tableToCols } from '@/db/query/utils.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

describe('Querying views', () => {
  /*
  Big test set for querying data.
  
  Because this doesn't do any data manipulation, we only need to create the table once for all tests.
  
  The test file this is based on also exists as table-with-views.csv if we need to reimport it.
  */
  let conn
  let table
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  const getResultsFromView = async (table, apiCols, apiView) => {
    /* Wraps around the total we have in terms of cols/views and mapping
     them to 'query' cols/views. */
    const columns = tableToCols(apiCols, apiView)
    const querySpec = getQuerySpec(apiCols, apiView)
    return await table.rowObjects(columns.map(col => col.id), querySpec)
  }
  
  describe('Test view filtering', () => {
    describe('View with no filter', () => {
      it('should map columns correctly in a filtered view', async () => {
        const view = {
          'groups': [],
          'columns': [
            {
              'id': 0,
              'width': 150,
              'header': 'Order date',
              'sourceColumnId': 1
            },
            {
              'id': 1,
              'width': 150,
              'header': 'Tix',
              'sourceColumnId': 2
            },
            {
              'id': 2,
              'width': 150,
              'header': 'Order number',
              'sourceColumnId': 0
            }
          ],
          'filters': null
        }
        
        const data = await getResultsFromView(table, tableDef.columns, view)
        const firstRow = data[0]
        expect(firstRow.c0).toEqual(tableRows[0].c1)
        expect(firstRow.c1.toString()).toEqual(tableRows[0].c2)
        expect(firstRow.c2.toString()).toEqual(tableRows[0].c0)
      })
      
      it('should handle a view with a column multiple times', async () => {
        const view = {
          'groups': [],
          'columns': [
            {
              'id': 0,
              'width': 150,
              'header': 'Order date',
              'sourceColumnId': 1
            },
            {
              'id': 1,
              'width': 150,
              'header': 'Tix',
              'sourceColumnId': 2
            },
            {
              'id': 2,
              'width': 150,
              'header': 'Order number',
              'sourceColumnId': 0
            },
            {
              'id': 3,
              'width': 150,
              'header': 'Tix again',
              'sourceColumnId': 2
            }
          ],
          'filters': null
        }
        
        const data = await getResultsFromView(table, tableDef.columns, view)
        const firstRow = data[0]
        expect(firstRow.c0).toEqual(tableRows[0].c1)
        expect(firstRow.c1.toString()).toEqual(tableRows[0].c2)
        expect(firstRow.c2.toString()).toEqual(tableRows[0].c0)
        expect(firstRow.c3.toString()).toEqual(tableRows[0].c2)
        
      })
    })
    
    describe('View with filter', () => {
      it('should filter correctly', async () => {
        const view = {
          'groups': [],
          'columns': [
            {
              'id': 0,
              'width': 150,
              'header': 'Order date',
              'sourceColumnId': 1
            },
            {
              'id': 1,
              'width': 150,
              'header': 'Tix',
              'sourceColumnId': 2
            },
            {
              'id': 2,
              'width': 150,
              'header': 'Order number',
              'sourceColumnId': 0
            }
          ],
          'filters': {
            
            // This is the source column id (which is id 0 in the view output)
            '2': [
              {
                'operator': 'equals',
                'value': '3'
              }
            ]
          }
        }
        
        // Check they are all 3
        const data = await getResultsFromView(table, tableDef.columns, view)
        expect(data.length).toBe(7)
        data.forEach(row => {
          expect(row.c1.toString()).toBe('3')
        })
        
      })
      
      it('Should filter on 2 columns in the view', async () => {
        const view = {
          'groups': [],
          'columns': [
            {
              'id': 0,
              'width': 150,
              'header': 'Order date',
              'sourceColumnId': 1
            },
            {
              'id': 1,
              'width': 150,
              'header': 'Tix',
              'sourceColumnId': 2
            },
            {
              'id': 2,
              'width': 150,
              'header': 'Order number',
              'sourceColumnId': 0
            }
          ],
          'filters': {
            '2': [
              {
                'operator': 'equals',
                'value': '3'
              }
            ],
            '0': [
              {
                'operator': 'begins_with',
                'value': '2VPT'
              }
            ]
          }
        }
        
        const data = await getResultsFromView(table, tableDef.columns, view)
        expect(data.length).toBe(2)
        data.forEach(row => {
          expect(row.c1.toString()).toBe('3')
          expect(row.c2.startsWith('2VPT')).toBe(true)
        })
      })
      
      it('Should filter on a column not in the view', async () => {
        const view = {
          'groups': [],
          'columns': [
            {
              'id': 0,
              'width': 150,
              'header': 'Order date',
              'sourceColumnId': 1
            },
            {
              'id': 1,
              'width': 150,
              'header': 'Tix',
              'sourceColumnId': 2
            }
          ],
          'filters': {
            '0': [
              {
                'operator': 'begins_with',
                'value': '2VPT'
              }
            ]
          }
        }
        
        const data = await getResultsFromView(table, tableDef.columns, view)
        expect(data.length).toBe(6)
      })
    })
    
    describe('Grouped view with filter', () => {
      it('should filter correctly', async () => {
        const view = {
          'groups': [
            {
              'by': 'year-week',
              'sourceColumnId': 1,
              'id': 0
            }
          ],
          // Get a sum of tickets by week, where 3 tickets were sold in the purchase
          'columns': [
            {
              'id': 1,
              'sourceColumnId': 2,
              'summarize': 'sum'
            }
          ],
          'filters': {
            '2': [
              {
                'operator': 'equals',
                'value': '3'
              }
            ]
          }
        }
        
        const data = await getResultsFromView(table, tableDef.columns, view)
        expect(data.length).toBe(4)
        expect(data[0].c1.toString()).toBe('6')
        expect(data[1].c1.toString()).toBe('6')
        expect(data[2].c1.toString()).toBe('6')
        expect(data[3].c1.toString()).toBe('3')
      })
    })
  })
})
