// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/views/table-with-views.json'
import tableRows from '@/tests/db/testdata/views/table-with-views-rows.json'
import Big from 'big.js'
import { buildColumnFilters } from '@/db/query/filters.js'
import { getQuerySpec } from '@/db/query/utils.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

describe('Test queries', () => {
  /*
  Big test set for querying data.
  
  Because this doesn't do any data manipulation, we only need to create the table once for all tests.
  
  The test file this is based on also exists as table-with-views.csv if we need to reimport it.
  */
  let conn
  let table
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  describe('Table filtering', () => {
    describe('Filtering on raw columns', () => {
      describe('Filtering on string columns', () => {
        it('should maintain calcs and window function values', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'equals', value: '2VPT-PHXS-GRG'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(1)
          expect(results[0].c12).toEqual(Big('14'))
          expect(results[0].c13).toEqual(Big(17))
          expect(results[0].c14).toEqual(Big(72))
          expect(results[0].c15).toEqual(Big(75))
        })
        
        it('should do a not_equals check', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'not_equals', value: '2VPT-PHXS-GRG'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length - 1)
        })
        
        it('should handle bad operators with full results', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{ operator: 'not_real' }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length)
        })
        
        it('should do begins_with filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'begins_with', value: '2VPT'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results).toBeDefined()
          expect(results.length).toBe(6)
        })
        
        it('should be case insensitive', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'begins_with', value: '2vpt'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(6)
        })
        
        it('should return zero rows without error', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'begins_with', value: 'XXX'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(0)
        })
        
        it('empty filter should be fine', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'begins_with', value: ''
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length)
        })
        
        it('should do a begins with exact match', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'begins_with', value: '2VPT-8LK3-SNS'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(1)
        })
        
        it('should do contains filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'contains', value: 'PHXS'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(1)
          expect(results[0].c0).toBe('2VPT-PHXS-GRG')
        })
        
        it('should do not_contains filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'not_contains', value: 'PHXS'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length - 1)
        })
        
        it('should do ends_with filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'ends_with', value: 'GRG'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(1)
          expect(results[0].c0).toBe('2VPT-PHXS-GRG')
        })
        
        it('should handle blank filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{ operator: 'blank' }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          // Assuming no blank values in the test data
          expect(results.length).toBe(0)
        })
        
        it('should handle not_blank filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{ operator: 'not_blank' }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length)
        })
      })
      
      describe('Filtering on numeric columns', () => {
        it('should do equals filtering on numeric column', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{ operator: 'equals', value: '3' }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(16)
        })
        
        it('should do not_equals filtering on numeric column', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{
            operator: 'not_equals', value: '3'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length - 16)
        })
        
        it('should do greater_than filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{
            operator: 'greater_than', value: '5'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(4)
        })
        
        it('should do less_than_or_equals filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{
            operator: 'less_than_or_equals', value: '5'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length - 4)
        })
        
        it('should do less_than filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{
            operator: 'less_than', value: '3'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(124)
        })
        
        it('should do greater_than_or_equals filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{
            operator: 'greater_than_or_equals', value: '3'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toBe(tableRows.length - 124)
        })
        
        it('should do between filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{
            operator: 'between', value: ['2', '4']
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          // Should include values 2, 3, and 4
          expect(results.length).toEqual(85)
        })
        
        it('should do not_between filtering', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{
            operator: 'not_between', value: ['2', '4']
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(tableRows.length - 85)
        })
      })
      
      describe('Filtering on date columns', () => {
        it('should do equals filtering on date column', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[1], [{
            operator: 'equals', value: '2024-01-31'
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(0)
        })
        
        it('should do greater_than filtering on date column', async () => {
          // convert '2024-02-19' to ISO string
          const value = new Date('2024-02-19').getTime()
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[1], [{ operator: 'greater_than', value }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(132)
        })
        
        it('should do less_than filtering on date column', async () => {
          const value = new Date('2024-02-19').getTime()
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[1], [{ operator: 'less_than', value }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(tableRows.length - 132)
        })
        
        it('should do between filtering on date column', async () => {
          const value = [new Date('2024-02-19').getTime(), new Date('2024-02-26').getTime()]
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[1], [{ operator: 'between', value }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(8)
        })
      })
      
      it('should filter on calculated column', async () => {
        const column = tableDef.columns.find(col => col.id === 14)
        const querySpec = getQuerySpec(tableDef.columns)
        querySpec.filtering.columns = [buildColumnFilters(column, [{ operator: 'greater_than', value: '100' }])]
        const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
        expect(results.length).toEqual(16)
      })
      
      it('should filter on window function column', async () => {
        const column = tableDef.columns.find(col => col.id === 12)
        const querySpec = getQuerySpec(tableDef.columns)
        querySpec.filtering.columns = [buildColumnFilters(column, [{ operator: 'greater_than', value: '50' }])]
        const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
        expect(results.length).toEqual(134)
      })
      
      describe('Edge cases and combined filters', () => {
        it('should handle between', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [
            buildColumnFilters(tableDef.columns[2], [{ operator: 'between', value: ['2', '6'] }])
          ]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(88)
        })
        
        it('multiple filters on the same column in one array should be same as a between', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [
            buildColumnFilters(tableDef.columns[2], [{ operator: 'greater_than_or_equals', value: '2' }]),
            buildColumnFilters(tableDef.columns[2], [{ operator: 'less_than_or_equals', value: '6' }])
          ]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(88)
        })
        
        it('should handle filters on multiple columns', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [
            buildColumnFilters(tableDef.columns[0], [{ operator: 'begins_with', value: '2VPT' }]),
            buildColumnFilters(tableDef.columns[2], [{ operator: 'equals', value: '3' }])
          ]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results.length).toEqual(2)
        })
        
        it('should handle empty filter values gracefully', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{ operator: 'equals', value: '' }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results).toBeDefined()
        })
        
        it('should handle null filter values gracefully', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{ operator: 'equals', value: null }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results).toBeDefined()
        })
        
        it('should handle undefined filter values gracefully', async () => {
          const querySpec = getQuerySpec(tableDef.columns)
          querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
            operator: 'equals', value: undefined
          }])]
          const results = await table.rowObjects(tableDef.columns.map(c => c.id), querySpec)
          expect(results).toBeDefined()
        })
      })
    })
  })
  
})
