// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/views/table-with-views.json'
import tableRows from '@/tests/db/testdata/views/table-with-views-rows.json'
import { filterQuerySpec, getQuerySpec } from '@/db/query/utils.js'
import { getGridDataFromSelection } from '@/utils/grid/grid.js'
import { buildColumnFilters } from '@/db/query/filters.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

describe('Selecting data', () => {
  let conn
  let table
  let querySpec
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    querySpec = getQuerySpec(tableDef.columns)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  const getGridData = async (params, filtering) => {
    params.dbTable = table
    params.querySpec = querySpec
    params.columns = Object.values(querySpec.columns)
    if (filtering) {
      params.querySpec = filterQuerySpec(params.querySpec, filtering)
    }
    return await getGridDataFromSelection(params)
  }
  
  describe('Range selection', () => {
    
    it('Should select one column of text data', async () => {
      const data = await getGridData({
        cellRange: {
          rows: [0, 4],
          cols: [0, 0]
        }
      })
      // assert it equals the first 5 rows of tableRows, first column
      expect(data.map(row => row[0])).toEqual(tableRows.slice(0, 5).map(row => row.c0))
    })
    
    it('Should select one column of numeric data', async () => {
      const data = await getGridData({
        cellRange: {
          rows: [0, 4],
          cols: [2, 2]
        }
      })
      // assert it equals the first 5 rows of tableRows, third column (number of tickets)
      expect(data.map(row => row[0])).toEqual(tableRows.slice(0, 5).map(row => row.c2))
    })
    
    it('Should select one column of date data', async () => {
      const data = await getGridData({
        cellRange: {
          rows: [0, 4],
          cols: [1, 1]
        }
      })
      // assert it equals the first 5 rows of tableRows, second column (order date)
      const origData = tableRows.slice(0, 5).map(row => new Date(row.c1).toISOString().slice(0, 19).replace('T', ' '))
      expect(data.map(row => row[0])).toEqual(origData)
    })
    
    it('Should select multiple columns with different data types', async () => {
      const data = await getGridData({
        cellRange: {
          rows: [0, 4],
          cols: [0, 2]
        }
      })
      const sourceRows = tableRows.slice(0, 5)
      // Check text column (order number)
      expect(data.map(row => row[0])).toEqual(sourceRows.map(row => row.c0))
      // Check date column (order date)
      expect(data.map(row => row[1])).toEqual(sourceRows.map(row => new Date(row.c1).toISOString().slice(0, 19).replace('T', ' ')))
      // Check number column (number of tickets)
      expect(data.map(row => row[2])).toEqual(sourceRows.map(row => row.c2))
    })
    
    it('Should select rows 3 to 5', async () => {
      const data = await getGridData({
        cellRange: {
          rows: [3, 5],
          cols: [0, 2]
        }
      })
      const sourceRows = tableRows.slice(3, 6)
      // Check text column (order number)
      expect(data.map(row => row[0])).toEqual(sourceRows.map(row => row.c0))
      // Check date column (order date)
      expect(data.map(row => row[1])).toEqual(sourceRows.map(row => new Date(row.c1).toISOString().slice(0, 19).replace('T', ' ')))
      // Check number column (number of tickets)
      expect(data.map(row => row[2])).toEqual(sourceRows.map(row => row.c2))
    })
    
    it('Should select filtered rows containing X in first column', async () => {
      const data = await getGridData({
        cellRange: {
          rows: [2, 6],
          cols: [0, 2]
        }
      }, {
        columns: [buildColumnFilters(tableDef.columns[0], [{
          operator: 'contains', value: 'X'
        }])]
      })
      const sourceRows = tableRows.filter(row => row.c0.includes('X')).slice(2, 7)
      expect(data.map(row => row[0])).toEqual(sourceRows.map(row => row.c0))
      expect(data.map(row => row[1])).toEqual(sourceRows.map(row => new Date(row.c1).toISOString().slice(0, 19).replace('T', ' ')))
      expect(data.map(row => row[2])).toEqual(sourceRows.map(row => row.c2))
    })
    
  })
})
