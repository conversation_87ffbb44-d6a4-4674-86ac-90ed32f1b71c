// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/badData/table.json'
import tableRows from '@/tests/db/testdata/badData/rows.json'
import Big from 'big.js'
import { getQuerySpec, tableToCols } from '@/db/query/utils.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

const getResultsFromView = async (table, apiCols, apiView) => {
  /* Wraps around the total we have in terms of cols/views and mapping
   them to 'query' cols/views. */
  const columns = tableToCols(apiCols, apiView)
  const querySpec = getQuerySpec(columns, apiView)
  return await table.rowObjects(columns.map(col => col.id), querySpec)
}

describe('Bad Data Handling', () => {
  /*
  Test set for querying data with bad values.

  This test verifies that rowObjects correctly handles bad data by using the 'c' data for columns
  when available, but falling back to the 'raw' value when necessary.

  According to the requirements:
  - rowObjects should always get back 'c' data for columns
  - It should pick the good cell if present
  - If not, it should get the bad 'raw' value
  - For example, in row id 2, c7 is a number column but has the word HAM in it
  - A rowObjects call should still return "HAM" as a string in c7
  - Other c7 values should be bigs (Big.js objects)
  */
  let conn
  let table
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  describe('Row Objects with Bad Data', () => {
    it('should return all rows with correct data types', async () => {
      const results = await getResultsFromView(table, tableDef.columns)
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Verify that all rows are returned with their IDs
      const rowIds = results.map(row => row.id).sort((a, b) => a - b)
      expect(rowIds).toEqual([0, 1, 2, 3, 4, 5])
      
      // Verify that good data is returned as the correct type (Big.js for numbers)
      const row0 = results.find(row => row.id === 0)
      expect(row0.c6).toEqual(Big('12')) // Stock column
      expect(row0.c7).toEqual(Big('12')) // RRP column
      
      // Verify that bad data is returned as raw strings
      const row2 = results.find(row => row.id === 2)
      expect(row2.c7).toBe('HAM') // Bad number data
      
      const row5 = results.find(row => row.id === 5)
      expect(row5.c6).toBe('papercuts lol') // Bad number data
      
      const row4 = results.find(row => row.id === 4)
      expect(row4.c12).toBe('no vibes') // Bad percent data
    })
    
    it('should handle bad number data by returning the raw value as string', async () => {
      const results = await getResultsFromView(table, tableDef.columns)
      
      expect(results).toBeDefined()
      
      // Verify all rows are returned
      expect(results.length).toBe(tableRows.length)
      
      // Row id 2 has "HAM" in column c7 which is a number column
      const rowWithBadData = results.find(row => row.id === 2)
      expect(rowWithBadData).toBeDefined()
      expect(rowWithBadData.c7).toBe('HAM') // Should return the raw value as a string
      
      // Other rows should have proper number values as Big.js objects
      const goodRows = [
        { id: 0, value: '12' },
        { id: 1, value: '12.235' },
        { id: 3, value: '23.2' },
        { id: 4, value: '12' },
        { id: 5, value: '0.45' }
      ]
      
      for (const { id, value } of goodRows) {
        const row = results.find(r => r.id === id)
        expect(row).toBeDefined()
        expect(row.c7).toEqual(Big(value))
      }
    })
    
    it('should handle bad data in multiple rows and columns', async () => {
      const results = await getResultsFromView(table, tableDef.columns)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Test each row for correct handling of good and bad data
      
      // Row 0 - All good data
      const row0 = results.find(row => row.id === 0)
      expect(row0).toBeDefined()
      expect(row0.c6).toEqual(Big('12')) // Stock
      expect(row0.c7).toEqual(Big('12')) // RRP
      expect(row0.c12).toEqual(Big('0.1')) // Vibe factor
      
      // Row 1 - Missing c12 (Vibe factor)
      const row1 = results.find(row => row.id === 1)
      expect(row1).toBeDefined()
      expect(row1.c6).toEqual(Big('24')) // Stock
      expect(row1.c7).toEqual(Big('12.235')) // RRP
      expect(row1.c12).toBeNull() // Missing Vibe factor
      
      // Row 2 - Has "HAM" in column c7 (RRP) and missing c6 (Stock)
      const row2 = results.find(row => row.id === 2)
      expect(row2).toBeDefined()
      expect(row2.c6).toBeNull() // Missing Stock
      expect(row2.c7).toBe('HAM') // Bad RRP data
      expect(row2.c12).toEqual(Big('0.43')) // Vibe factor
      
      // Row 3 - All good data
      const row3 = results.find(row => row.id === 3)
      expect(row3).toBeDefined()
      expect(row3.c6).toEqual(Big('12')) // Stock
      expect(row3.c7).toEqual(Big('23.2')) // RRP
      expect(row3.c12).toEqual(Big('0.2')) // Vibe factor
      
      // Row 4 - Has "no vibes" in column c12 (Vibe factor)
      const row4 = results.find(row => row.id === 4)
      expect(row4).toBeDefined()
      expect(row4.c6).toEqual(Big('24')) // Stock
      expect(row4.c7).toEqual(Big('12')) // RRP
      expect(row4.c12).toBe('no vibes') // Bad Vibe factor data
      
      // Row 5 - Has "papercuts lol" in column c6 (Stock)
      const row5 = results.find(row => row.id === 5)
      expect(row5).toBeDefined()
      expect(row5.c6).toBe('papercuts lol') // Bad Stock data
      expect(row5.c7).toEqual(Big('0.45')) // RRP
      expect(row5.c12).toEqual(Big('0.15')) // Vibe factor
    })
    
    it('should handle calculated columns with bad input data', async () => {
      const results = await getResultsFromView(table, tableDef.columns)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // For rows with good data in both source columns, the calculation should work
      const row0 = results.find(row => row.id === 0)
      expect(row0).toBeDefined()
      expect(row0.c6).toEqual(Big('12')) // Stock
      expect(row0.c7).toEqual(Big('12')) // RRP
      expect(row0.c8).toEqual(Big('144')) // Stock value = 12 * 12 = 144
      
      const row1 = results.find(row => row.id === 1)
      expect(row1).toBeDefined()
      expect(row1.c6).toEqual(Big('24')) // Stock
      expect(row1.c7).toEqual(Big('12.235')) // RRP
      expect(row1.c8).toEqual(Big('293.64')) // Stock value = 24 * 12.235 = 293.64
      
      // For rows with missing data in one of the source columns, the calculation should handle it appropriately
      const row2 = results.find(row => row.id === 2)
      expect(row2).toBeDefined()
      expect(row2.c6).toBeNull() // Missing Stock
      expect(row2.c7).toBe('HAM') // Bad RRP data
      // The calculation can't be performed with missing/bad data, so c2 should be undefined or null
      expect(row2.c8).toBeNull()
      
      // For rows with bad data in one of the source columns, the calculation should handle it appropriately
      const row5 = results.find(row => row.id === 5)
      expect(row5).toBeDefined()
      expect(row5.c6).toBe('papercuts lol') // Bad Stock data
      expect(row5.c7).toEqual(Big('0.45')) // RRP
      // The calculation can't be performed with bad data, so c2 should be undefined or null
      expect(row5.c8).toBeNull()
      
      // For rows with good data in both source columns, the calculation should work
      const row3 = results.find(row => row.id === 3)
      expect(row3).toBeDefined()
      expect(row3.c6).toEqual(Big('12')) // Stock
      expect(row3.c7).toEqual(Big('23.2')) // RRP
      expect(row3.c8).toEqual(Big('278.4')) // Stock value = 12 * 23.2 = 278.4
      
      const row4 = results.find(row => row.id === 4)
      expect(row4).toBeDefined()
      expect(row4.c6).toEqual(Big('24')) // Stock
      expect(row4.c7).toEqual(Big('12')) // RRP
      expect(row4.c8).toEqual(Big('288')) // Stock value = 24 * 12 = 288
    })
  })
  
  describe('Row Arrays with Bad Data', () => {
    it('should return all rows with correct data types as arrays', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.rowArray(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Find column indexes for testing
      const stockIndex = columns.findIndex(col => col.id === 6)
      const rrpIndex = columns.findIndex(col => col.id === 7)
      const vibeIndex = columns.findIndex(col => col.id === 12)
      
      // Verify that good data is returned as the correct type (Big.js for numbers)
      const row0 = results[0]
      expect(row0[stockIndex]).toEqual(Big('12')) // Stock column
      expect(row0[rrpIndex]).toEqual(Big('12')) // RRP column
      
      const row2 = results[2]
      expect(row2[rrpIndex]).toEqual('HAM')
      
      const row5 = results[5]
      expect(row5[stockIndex]).toEqual('papercuts lol')
      
      const row4 = results[4]
      expect(row4[vibeIndex]).toEqual('no vibes')
    })
    
    it('should handle bad number data by returning null instead of raw value', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.rowArray(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Find column index for RRP (c7)
      const rrpIndex = columns.findIndex(col => col.id === 7) // +2 for id and pos
      
      // Row id 2 has "HAM" in column c7 which is a number column
      const rowWithBadData = results[2]
      expect(rowWithBadData).toBeDefined()
      expect(rowWithBadData[rrpIndex]).toEqual('HAM')
      
      // Other rows should have proper number values as Big.js objects
      const goodRows = [
        { id: 0, value: '12' },
        { id: 1, value: '12.235' },
        { id: 3, value: '23.2' },
        { id: 4, value: '12' },
        { id: 5, value: '0.45' }
      ]
      
      for (const { id, value } of goodRows) {
        const row = results[id]
        expect(row).toBeDefined()
        expect(row[rrpIndex]).toEqual(Big(value))
      }
    })
    
    it('should handle calculated columns with bad input data in arrays', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.rowArray(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Find column indexes
      const stockIndex = columns.findIndex(col => col.id === 6)
      const rrpIndex = columns.findIndex(col => col.id === 7)
      const stockValueIndex = columns.findIndex(col => col.id === 8)
      
      // For rows with good data in both source columns, the calculation should work
      const row0 = results[0]
      expect(row0).toBeDefined()
      expect(row0[stockIndex]).toEqual(Big('12')) // Stock
      expect(row0[rrpIndex]).toEqual(Big('12')) // RRP
      expect(row0[stockValueIndex]).toEqual(Big('144')) // Stock value = 12 * 12 = 144
      
      // For rows with bad data in one of the source columns, the calculation should be null
      const row2 = results[2]
      expect(row2).toBeDefined()
      expect(row2[stockIndex]).toBeNull() // Missing Stock
      expect(row2[rrpIndex]).toEqual('HAM')
      expect(row2[stockValueIndex]).toBeUndefined() // Calculation should be null
      
      const row5 = results[5]
      expect(row5).toBeDefined()
      expect(row5[stockIndex]).toEqual('papercuts lol')
      expect(row5[rrpIndex]).toEqual(Big('0.45')) // RRP
      expect(row5[stockValueIndex]).toBeUndefined() // Calculation should be null
    })
    
    it('should handle multiple rows with various bad data in arrays', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.rowArray(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Find column indexes
      const stockIndex = columns.findIndex(col => col.id === 6)
      const rrpIndex = columns.findIndex(col => col.id === 7)
      const vibeIndex = columns.findIndex(col => col.id === 12)
      
      // Row 0 - All good data
      const row0 = results[0]
      expect(row0).toBeDefined()
      expect(row0[stockIndex]).toEqual(Big('12')) // Stock
      expect(row0[rrpIndex]).toEqual(Big('12')) // RRP
      expect(row0[vibeIndex]).toEqual(Big('0.1')) // Vibe factor
      
      // Row 1 - Missing c12 (Vibe factor)
      const row1 = results[1]
      expect(row1).toBeDefined()
      expect(row1[stockIndex]).toEqual(Big('24')) // Stock
      expect(row1[rrpIndex]).toEqual(Big('12.235')) // RRP
      expect(row1[vibeIndex]).toBeNull() // Missing Vibe factor
      
      // Row 2 - Has "HAM" in column c7 (RRP) and missing c6 (Stock)
      const row2 = results[2]
      expect(row2).toBeDefined()
      expect(row2[stockIndex]).toBeNull() // Missing Stock
      expect(row2[rrpIndex]).toEqual('HAM') // Bad RRP data
      expect(row2[vibeIndex]).toEqual(Big('0.43')) // Vibe factor
      
      // Row 4 - Has "no vibes" in column c12 (Vibe factor)
      const row4 = results[4]
      expect(row4).toBeDefined()
      expect(row4[stockIndex]).toEqual(Big('24')) // Stock
      expect(row4[rrpIndex]).toEqual(Big('12')) // RRP
      expect(row4[vibeIndex]).toEqual('no vibes') // Bad Vibe factor data
      
      // Row 5 - Has "papercuts lol" in column c6 (Stock)
      const row5 = results[5]
      expect(row5).toBeDefined()
      expect(row5[stockIndex]).toEqual('papercuts lol') // Bad Stock data
      expect(row5[rrpIndex]).toEqual(Big('0.45')) // RRP
      expect(row5[vibeIndex]).toEqual(Big('0.15')) // Vibe factor
    })
  })
  
  describe('Chart Data with Bad Data', () => {
    it('should return all rows with correct data types for charts', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.chartData(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Verify that good data is returned as the correct type (Big.js for numbers)
      const row0 = results.find(row => row.id === 0)
      expect(row0.c6).toEqual(Big('12')) // Stock column
      expect(row0.c7).toEqual(Big('12')) // RRP column
      expect(row0.c12).toEqual(Big('0.1')) // Vibe factor
    })
    
    it('should handle bad number data by returning null for charts', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.chartData(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Row id 2 has "HAM" in column c7 which is a number column
      // For chartData, this should be null instead of the raw value
      const rowWithBadData = results.find(row => row.id === 2)
      expect(rowWithBadData).toBeDefined()
      expect(rowWithBadData.c7).toBeNull() // Should return null instead of 'HAM'
      
      // Row 5 has "papercuts lol" in column c6 which is a number column
      const anotherBadRow = results.find(row => row.id === 5)
      expect(anotherBadRow).toBeDefined()
      expect(anotherBadRow.c6).toBeNull() // Should return null instead of 'papercuts lol'
      
      // Row 4 has "no vibes" in column c12 which is a number column
      const thirdBadRow = results.find(row => row.id === 4)
      expect(thirdBadRow).toBeDefined()
      expect(thirdBadRow.c12).toBeNull() // Should return null instead of 'no vibes'
    })
    
    it('should handle calculated columns with bad input data for charts', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.chartData(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // For rows with good data in both source columns, the calculation should work
      const row0 = results.find(row => row.id === 0)
      expect(row0).toBeDefined()
      expect(row0.c6).toEqual(Big('12')) // Stock
      expect(row0.c7).toEqual(Big('12')) // RRP
      expect(row0.c8).toEqual(Big('144')) // Stock value = 12 * 12 = 144
      
      // For rows with bad data in one of the source columns, the calculation should be null
      const row2 = results.find(row => row.id === 2)
      expect(row2).toBeDefined()
      expect(row2.c6).toBeNull() // Missing Stock
      expect(row2.c7).toBeNull() // Bad RRP data (should be null for chartData)
      expect(row2.c8).toBeNull() // Calculation should be null
      
      const row5 = results.find(row => row.id === 5)
      expect(row5).toBeDefined()
      expect(row5.c6).toBeNull() // Bad Stock data (should be null for chartData)
      expect(row5.c7).toEqual(Big('0.45')) // RRP
      expect(row5.c8).toBeNull() // Calculation should be null
    })
    
    it('should handle multiple rows with various bad data for charts', async () => {
      const columns = tableToCols(tableDef.columns)
      const querySpec = getQuerySpec(tableDef.columns)
      const results = await table.chartData(columns.map(col => col.id), querySpec)
      
      expect(results).toBeDefined()
      expect(results.length).toBe(tableRows.length)
      
      // Row 0 - All good data
      const row0 = results.find(row => row.id === 0)
      expect(row0).toBeDefined()
      expect(row0.c6).toEqual(Big('12')) // Stock
      expect(row0.c7).toEqual(Big('12')) // RRP
      expect(row0.c12).toEqual(Big('0.1')) // Vibe factor
      
      // Row 1 - Missing c12 (Vibe factor)
      const row1 = results.find(row => row.id === 1)
      expect(row1).toBeDefined()
      expect(row1.c6).toEqual(Big('24')) // Stock
      expect(row1.c7).toEqual(Big('12.235')) // RRP
      expect(row1.c12).toBeNull() // Missing Vibe factor
      
      // Row 2 - Has "HAM" in column c7 (RRP) and missing c6 (Stock)
      const row2 = results.find(row => row.id === 2)
      expect(row2).toBeDefined()
      expect(row2.c6).toBeNull() // Missing Stock
      expect(row2.c7).toBeNull() // Bad RRP data (should be null for chartData)
      expect(row2.c12).toEqual(Big('0.43')) // Vibe factor
      
      // Row 4 - Has "no vibes" in column c12 (Vibe factor)
      const row4 = results.find(row => row.id === 4)
      expect(row4).toBeDefined()
      expect(row4.c6).toEqual(Big('24')) // Stock
      expect(row4.c7).toEqual(Big('12')) // RRP
      expect(row4.c12).toBeNull() // Bad Vibe factor data (should be null for chartData)
      
      // Row 5 - Has "papercuts lol" in column c6 (Stock)
      const row5 = results.find(row => row.id === 5)
      expect(row5).toBeDefined()
      expect(row5.c6).toBeNull() // Bad Stock data (should be null for chartData)
      expect(row5.c7).toEqual(Big('0.45')) // RRP
      expect(row5.c12).toEqual(Big('0.15')) // Vibe factor
    })
  })
})
