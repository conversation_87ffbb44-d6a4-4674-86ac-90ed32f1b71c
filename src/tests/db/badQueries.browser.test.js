// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/basic/table.json'
import tableRows from '@/tests/db/testdata/basic/table-rows.json'
import { Big } from 'big.js'
import { calcIsValid, getQuerySpec } from '@/db/query/utils.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

describe('DuckDB Browser Integration Tests', () => {
  let conn
  let table
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.disconnect()
    }
  })
  
  describe('Working queries', () => {
    const tableId = tableDef.id
    beforeAll(async () => {
      await conn.buildTable(tableId, tableDef.columns, tableRows)
      table = conn.tables[tableId]
    })
    
    afterAll(async () => {
      await conn.dropTable(tableId)
    })
    
    describe('Basic Count', () => {
      it('should verify row count matches source data', async () => {
        const count = await table.count()
        expect(count).toBe(tableRows.length)
      })
    })
    
    describe('Basic rowObject queries', () => {
      it('should handle datetime column correctly', async () => {
        const result = await table.rowObjects([0])
        expect(result[0].c0).toBe(Number(tableRows.find(row => row.id === 8).c0))
      })
      
      it('should handle currency values correctly', async () => {
        const result = await table.rowObjects([9])
        expect(result[0].c9).toEqual(Big(tableRows.find(row => row.id === 8).c9))
      })
      
      it('should get a calculated column correctly', async () => {
        const result = await table.rowObjects([6])
        expect(result[0].c6).toEqual(Big('3455'))
      })
      
    })
  })
  
  describe('Bad queries', () => {
    const tableId = tableDef.id
    beforeAll(async () => {
      await conn.buildTable(tableId, tableDef.columns, tableRows)
      table = conn.tables[tableId]
    })
    
    afterAll(async () => {
      await conn.dropTable(tableId)
    })
    
    describe('Bad grouping', () => {
      const baseView = {
        groups: [{ by: 'year-week', sourceColumnId: 0, id: 0 }],
        columns: [
          // Try grouping on a regular and a calc col
          { sourceColumnId: 3, id: 1, summarize: 'avg' },
          { sourceColumnId: 6, id: 2, summarize: 'avg' }
        ]
      }
      
      it('Should get a result with a good grouping', async () => {
        const querySpec = getQuerySpec(tableDef.columns, baseView)
        const result = await table.rowObjects([0, 1, 2], querySpec)
        expect(result.length).toBeGreaterThan(0)
        result.forEach(row => {
          expect(row.c0).toBeDefined()
          expect(row.c1).toBeDefined()
          expect(row.c2).toBeDefined()
        })
      })
      
      it('Should fail gracefully with a bad group by', async () => {
        const view = JSON.parse(JSON.stringify(baseView))
        view.groups[0].by = 'bad'
        const querySpec = getQuerySpec(tableDef.columns, view)
        const result = await table.rowObjects([0, 1, 2], querySpec)
        expect(result.length).toBeDefined()
      })
      
      it('Should fail gracefully with a bad summarize', async () => {
        const view = JSON.parse(JSON.stringify(baseView))
        view.columns[0].summarize = 'bad'
        const querySpec = getQuerySpec(tableDef.columns, view)
        const result = await table.rowObjects([0, 1, 2], querySpec)
        expect(result.length).toBeGreaterThan(0)
        result.forEach(row => {
          expect(row.c0).toBeDefined()
          expect(row.c1).toBeNull()
          expect(row.c2).toBeDefined()
        })
      })
      
      it('Should fail gracefully with no summarize', async () => {
        const view = JSON.parse(JSON.stringify(baseView))
        view.columns[0].summarize = undefined
        const querySpec = getQuerySpec(tableDef.columns, view)
        const result = await table.rowObjects([0, 1, 2], querySpec)
        expect(result.length).toBeGreaterThan(0)
        result.forEach(row => {
          expect(row.c0).toBeDefined()
          expect(row.c1).toBeNull()
          expect(row.c2).toBeDefined()
        })
      })
    })
    
    describe('Bad aggregate query', () => {
      it('Should fail gracefully with a bad aggregate', async () => {
        const result = await table.aggregate(0, ['bad'])
        expect(result).toBeUndefined()
      })
      it('Should fail gracefully with one bad and one good', async () => {
        const result = await table.aggregate(0, ['bad', 'min'])
        expect(result).toBeDefined()
        expect(result.min).toBeDefined()
        expect(result.bad).toBeUndefined()
      })
    })
    
    describe('Bad calculated column', () => {
      
      it('It should handle bad source cols gracefully', async () => {
        /* We have a test below that does this "properly" at database creation, but
           because calcs aren't actually stored at the db level, most tests can be done
           here sort of 'faked' at the querySpec.
         */
        const cols = JSON.parse(JSON.stringify(tableDef.columns))
        cols[7].calc.sourceCols.push(999)
        const querySpec = getQuerySpec(cols)
        const colMap = Object.fromEntries(querySpec.columns.map(col => [col.id, col]))
        expect(calcIsValid(cols[7].calc, colMap)).toBe(false)
        const result = await table.rowObjects([6], querySpec)
        expect(result[0].c6).toBeNull()
        
      })
      
      it('It should handle a bad calc name gracefully', async () => {
        const cols = JSON.parse(JSON.stringify(tableDef.columns))
        cols[7].calc.type = 'bad'
        const querySpec = getQuerySpec(cols)
        const colMap = Object.fromEntries(querySpec.columns.map(col => [col.id, col]))
        expect(calcIsValid(cols[7].calc, colMap)).toBe(false)
        const result = await table.rowObjects([6], querySpec)
        expect(result[0].c6).toBeNull()
      })
      
      it('It should handle a bad calc requiring multi-source cols gracefully', async () => {
        // Sum column needs to have two or more source cols
        const cols = JSON.parse(JSON.stringify(tableDef.columns))
        cols[7].calc.sourceCols = [1]
        const querySpec = getQuerySpec(cols)
        const colMap = Object.fromEntries(querySpec.columns.map(col => [col.id, col]))
        expect(calcIsValid(cols[7].calc, colMap)).toBe(false)
        const result = await table.rowObjects([6], querySpec)
        expect(result[0].c6).toBeNull()
      })
      
      it('It should handle a bad calc requiring one-source col gracefully', async () => {
        // This is the cumulative, which should only have one source col
        const cols = JSON.parse(JSON.stringify(tableDef.columns))
        cols[8].calc.sourceCols = [1, 2]
        const querySpec = getQuerySpec(cols)
        const colMap = Object.fromEntries(querySpec.columns.map(col => [col.id, col]))
        expect(calcIsValid(cols[8].calc, colMap)).toBe(false)
        const result = await table.rowObjects([7], querySpec)
        expect(result[0].c7).toBeNull()
      })
    })
  })
  
  /* This one test builds up a new db with a bad calc column from scratch just to test
     something close to reality; but tests above modifying the queryspec have the same effect.
   */
  describe('Bad calculated column from scratch', () => {
    const tableId = tableDef.id
    
    // Always drop, but we build on each test individually
    afterAll(async () => {
      await conn.dropTable(tableId)
    })
    
    it('It should handle a bad calc gracefully', async () => {
      const cols = JSON.parse(JSON.stringify(tableDef.columns))
      cols[7].calc.sourceCols.push(999)
      await conn.buildTable(tableId, cols, tableRows)
      table = conn.tables[tableId]
      
      const result = await table.rowObjects([6])
      expect(result[0].c6).toBeNull()
      
    })
    
  })
})
