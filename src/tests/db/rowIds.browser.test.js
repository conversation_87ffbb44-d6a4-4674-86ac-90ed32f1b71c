// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/views/table-with-views.json'
import tableRows from '@/tests/db/testdata/views/table-with-views-rows.json'
import { getQuerySpec } from '@/db/query/utils.js'
import { buildColumnFilters } from '@/db/query/filters.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

describe('rowIds Method', () => {
  let conn
  let table
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  describe('Basic rowIds functionality', () => {
    it('should return all row ids when no parameters are provided', async () => {
      const ids = await table.rowIds()
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(tableRows.length)
      // IDs should be sequential starting from 0
      expect(ids[0]).toBe(0)
      expect(ids[ids.length - 1]).toBe(tableRows.length - 1)
    })
    
    it('should return specific row ids when given a position list', async () => {
      const posList = [3, 7, 12]
      const ids = await table.rowIds(posList)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(posList.length)
      // IDs should match the positions in the list
      expect(ids).toEqual(posList)
    })
    
    it('should return row ids within a position range', async () => {
      const posRange = { from: 5, to: 10 }
      const ids = await table.rowIds(posRange)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(posRange.to - posRange.from + 1)
      // IDs should be sequential within the range
      expect(ids[0]).toBe(5)
      expect(ids[ids.length - 1]).toBe(10)
    })
  })
  
  describe('rowIds with filtering', () => {
    it('should return filtered row ids when filter columns are provided', async () => {
      const querySpec = getQuerySpec(tableDef.columns)
      querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
        operator: 'begins_with', value: '2VPT'
      }])]
      const ids = await table.rowIds(null, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      
      // Verify the count matches what we expect from filtering.browser.test.js
      expect(ids.length).toBe(6)
    })
    
    it('should return filtered row ids with numeric column filter', async () => {
      const querySpec = getQuerySpec(tableDef.columns)
      querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[2], [{ operator: 'equals', value: '3' }])]
      const ids = await table.rowIds(null, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      
      // Verify the count matches what we expect from filtering.browser.test.js
      expect(ids.length).toBe(16)
    })
    
    it('should return filtered row ids with multiple column filters', async () => {
      const querySpec = getQuerySpec(tableDef.columns)
      querySpec.filtering.columns = [
        buildColumnFilters(tableDef.columns[0], [{ operator: 'begins_with', value: '2VPT' }]),
        buildColumnFilters(tableDef.columns[2], [{ operator: 'equals', value: '3' }])
      ]
      const ids = await table.rowIds(null, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      
      // Verify the count matches what we expect from filtering.browser.test.js
      expect(ids.length).toBe(2)
    })
    
    it('should return filtered row ids with position range', async () => {
      const querySpec = getQuerySpec(tableDef.columns)
      querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{ operator: 'contains', value: 'S' }])]
      const posRange = { from: 0, to: 9 }
      const ids = await table.rowIds(posRange, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(10)
      
      // Filter rows client-side to verify results
      const filteredRows = tableRows.filter(row => row.c0.includes('S')).slice(0, 10)
      ids.forEach((id, index) => {
        expect(id).toEqual(filteredRows[index].id)
      })
    })
  })
  
  describe('rowIds with grouping', () => {
    it('should return row ids when grouped by year-week', async () => {
      const querySpec = getQuerySpec(tableDef.columns, {
        groups: [{ by: 'year-week', sourceColumnId: 1, id: 0 }]
      })
      const ids = await table.rowIds(undefined, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      
      // Based on select.browser.test.js, we expect 10 groups
      expect(ids.length).toBe(10)
    })
    
    it('should return specific row ids when grouped and given a position list', async () => {
      const view = { groups: [{ by: 'year-week', sourceColumnId: 1, id: 0 }] }
      const posList = [0, 2, 4]
      const querySpec = getQuerySpec(tableDef.columns, view)
      const ids = await table.rowIds(posList, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(posList.length)
    })
    
    it('should return row ids within a position range when grouped', async () => {
      const view = { groups: [{ by: 'year-week', sourceColumnId: 1, id: 0 }] }
      const posRange = { from: 0, to: 3 }
      const querySpec = getQuerySpec(tableDef.columns, view)
      const ids = await table.rowIds(posRange, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(posRange.to - posRange.from + 1)
    })
  })
  
  describe('Edge cases', () => {
    it('should handle empty position list and get full results', async () => {
      const ids = await table.rowIds([])
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(0)
    })
    
    it('should handle out-of-bounds position range', async () => {
      const posRange = { from: 1000, to: 1010 }
      const ids = await table.rowIds(posRange)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(0)
    })
    
    it('should handle filters that return no results', async () => {
      const querySpec = getQuerySpec(tableDef.columns)
      querySpec.filtering.columns = [buildColumnFilters(tableDef.columns[0], [{
        operator: 'equals', value: 'non-existent-value'
      }])]
      const ids = await table.rowIds(null, querySpec)
      expect(ids).toBeDefined()
      expect(Array.isArray(ids)).toBe(true)
      expect(ids.length).toBe(0)
    })
  })
})
