{"id": "gViXQqS4iC6kmdYgrQ85WV", "organization": null, "date_created": "2025-01-25T14:53:21.599189Z", "date_last_updated": "2025-04-26T10:38:29.535569Z", "date_deleted": null, "title": "Stationary", "charts": [{"id": "GM2JHcHBsKbcxYcfZ22TL2", "date_created": "2025-01-25T14:54:05.584279Z", "date_last_updated": "2025-04-04T16:00:48.258884Z", "table": "gViXQqS4iC6kmdYgrQ85WV", "title": "", "theme": null, "view": null, "data": {"axis": {"max": null, "min": null, "show": true, "column": 5}, "rows": {}, "byRow": false, "design": {"colors": {"system": "step"}}, "datasets": [{"axis": {"show": true}, "series": [8], "values": [], "stacking": "", "chartType": "bar"}], "subtitle": ""}}], "views": [{"id": "kGVQEkg57sSjTn3ktCZQmg", "date_created": "2025-01-25T15:05:36.961919Z", "date_last_updated": "2025-04-09T12:26:42.445317Z", "table": "gViXQqS4iC6kmdYgrQ85WV", "title": "Grouped View", "groups": [{"sourceColumnId": 5, "id": 0, "width": 150, "header": "<PERSON><PERSON>"}], "columns": [{"sourceColumnId": 6, "id": 1, "width": 207, "header": "Stock (Sum)", "summarize": "sum"}, {"sourceColumnId": 8, "id": 2, "width": 193, "header": "Stock value (Sum)", "summarize": "sum"}]}, {"id": "dGV4dQZLHw85kzac5Z3ZYQ", "date_created": "2025-01-25T15:07:56.499955Z", "date_last_updated": "2025-04-21T18:00:11.852238Z", "table": "gViXQqS4iC6kmdYgrQ85WV", "title": "Promos", "groups": [{"sourceColumnId": 5, "id": 1, "sort": {"desc": true, "order": 2}, "width": 150, "header": "Items"}], "columns": [{"sourceColumnId": 1, "id": "id", "sort": {"desc": false, "order": 1}, "width": 150, "header": "Row count", "summarize": "count"}, {"sourceColumnId": 7, "id": 2, "sort": {"desc": false, "order": 0}, "width": 150, "header": "RRP (Sum)", "summarize": "sum"}]}, {"id": "Sxo6JxeWxef34bxWVhaaXu", "date_created": "2025-04-21T18:00:50.302999Z", "date_last_updated": "2025-04-21T18:00:50.302999Z", "table": "gViXQqS4iC6kmdYgrQ85WV", "title": "Filtered View", "groups": [], "columns": [{"id": 5, "width": 150, "header": "Items"}, {"id": 4, "width": 150, "header": "Color"}]}], "columns": [{"id": 5, "type": "text", "props": {}, "width": 150, "header": "Items", "filters": [], "isLabel": false, "aggregate": "count"}, {"id": 6, "type": "number", "props": {"force": true, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 136, "header": "Stock", "filters": [], "isLabel": false, "aggregate": "sum"}, {"id": 7, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 150, "header": "RRP", "filters": [], "isLabel": false, "aggregate": "avg"}, {"id": 8, "calc": {"data": {}, "type": "multiply", "sourceCols": [6, 7]}, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 172, "header": "Stock value", "filters": [], "isLabel": false, "aggregate": "sum"}, {"id": 12, "type": "percent", "props": {"decimals": 2, "decimalsPad": false}, "width": 178, "header": "Vibe factor", "filters": [], "isLabel": false, "aggregate": "avg"}, {"id": 10, "type": "datetime", "props": {"format": "yyyy-MM-dd", "dateType": "date"}, "width": 191, "header": "Last purchased", "filters": [], "isLabel": false, "aggregate": "count"}, {"id": 4, "type": "text", "props": {}, "width": 143, "header": "Color", "filters": [], "isLabel": false, "aggregate": "count"}, {"id": 11, "type": "boolean", "props": {}, "width": 150, "header": "Dope?", "filters": [], "isLabel": false, "aggregate": "count"}], "project": "S68bQ28Dyej3RXUQhrHXC8"}