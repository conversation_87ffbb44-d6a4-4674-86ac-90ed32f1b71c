{"id": "2sQCLU3gzfiBemo8bsev3S", "title": "Test table", "columns": [{"id": 0, "pin": "l", "type": "datetime", "props": {"format": "yyyy-MM-dd", "dateType": "date"}, "width": 182, "header": "Date", "isLabel": false, "aggregate": "count"}, {"id": 9, "type": "currency", "props": {"force": false, "locale": "en-US", "display": "symbol", "currency": "USD", "accounting": false}, "width": 138, "header": "Costs", "isLabel": false, "aggregate": "sum"}, {"id": 4, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 96, "header": "GR", "isLabel": false, "aggregate": "sum"}, {"id": 1, "type": "number", "props": {}, "width": 107, "header": "Sum", "isLabel": false, "aggregate": "max"}, {"id": 2, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 93, "header": "F5", "isLabel": false, "aggregate": "sum"}, {"id": 3, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "PRO", "isLabel": false, "aggregate": "sum"}, {"id": 5, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Shield", "isLabel": false, "aggregate": "sum"}, {"id": 6, "calc": {"type": "sum", "sourceCols": [2, 3, 4, 1, 5]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 143, "header": "Sum", "isLabel": false, "aggregate": "sum"}, {"id": 7, "calc": {"type": "cumulative", "sourceCols": [6]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Cumulative", "isLabel": false, "aggregate": "count"}, {"id": 8, "calc": {"type": "sum", "sourceCols": [7, 6, 2]}, "type": "number", "props": {"force": false, "compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Sum", "isLabel": false, "aggregate": "skew"}]}