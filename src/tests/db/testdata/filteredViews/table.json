{"id": "4oxxJirRjBuDTeuJDFGfzT", "organization": null, "date_created": "2025-05-19T18:37:00.401959Z", "date_last_updated": "2025-05-19T18:37:53.449027Z", "date_deleted": null, "title": "Table With Views", "charts": [], "columns": [{"id": 1, "type": "datetime", "props": {"format": "yyyy-MM-dd HH:mm:ss", "dateType": "datetime"}, "width": 253, "header": "Order date", "isLabel": false, "aggregate": "diff"}, {"id": 0, "type": "text", "props": {}, "width": 173, "header": "Order number", "isLabel": false, "aggregate": "count"}, {"id": 2, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 225, "header": "Number of tickets", "isLabel": false, "aggregate": "sum"}], "project": null}