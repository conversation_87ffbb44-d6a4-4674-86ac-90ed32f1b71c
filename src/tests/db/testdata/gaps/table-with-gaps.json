{"id": "gndzzLNrRdhs4NHNCLSu5o", "organization": null, "date_created": "2025-04-02T13:20:42.062444Z", "date_last_updated": "2025-04-02T15:03:03.705720Z", "date_deleted": null, "title": "Table with gaps", "charts": [], "views": [], "columns": [{"id": 0, "type": "datetime", "props": {"format": "yyyy-MM-dd", "dateType": "date"}, "width": 150, "header": "#date", "isLabel": true, "aggregate": "diff"}, {"id": 3, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "No gapped value", "isLabel": false, "aggregate": "sum"}, {"id": 1, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Gapped value", "isLabel": false, "aggregate": "sum"}, {"id": 2, "calc": {"data": {"up": 4}, "type": "change", "sourceCols": [1]}, "type": "percent", "props": {"decimals": 2, "decimalsPad": false}, "width": 258, "header": "Calc on gapped value", "isLabel": false, "aggregate": "avg"}], "project": null}