{"id": "asdfasdfasdf", "title": "Views test", "views": [{"id": "group-of-window-function", "title": "Group of window function should fail cleanly", "groups": [{"sourceColumnId": 12, "id": 0, "width": 150}], "columns": [{"sourceColumnId": "id", "id": 1, "summarize": "count"}]}, {"id": "agg-of-calc-of-calc", "title": "Aggregated calc of calc", "groups": [{"by": "year-month-day", "sourceColumnId": 1, "id": 0}], "columns": [{"sourceColumnId": 15, "id": 1, "summarize": "max"}]}], "columns": [{"id": 0, "type": "text", "props": {}, "width": 150, "header": "Order number", "isLabel": false, "aggregate": "count"}, {"id": 1, "type": "datetime", "props": {"format": "yyyy-MM-dd HH:mm:ss", "dateType": "datetime"}, "width": 150, "header": "Order date", "isLabel": false, "aggregate": "diff"}, {"id": 2, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 150, "header": "Number of tickets", "isLabel": false, "aggregate": "sum"}, {"id": 12, "calc": {"type": "cumulative", "sourceCols": [2]}, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 203, "header": "Cumulative of Number of tickets", "isLabel": false, "aggregate": "sum"}, {"id": 13, "calc": {"type": "sum", "sourceCols": [12, 2]}, "type": "number", "props": {"compact": false, "decimals": 2, "thousands": false, "decimalsPad": false}, "width": 225, "header": "Some weird total", "isLabel": false, "aggregate": "sum"}, {"id": 5, "type": "currency", "props": {"locale": "en-GB", "display": "symbol", "currency": "GBP", "accounting": false}, "width": 150, "header": "Ticket total", "isLabel": false, "aggregate": "sum"}, {"id": 14, "calc": {"type": "multiply", "sourceCols": [2, 5]}, "type": "currency", "props": {"locale": "en-GB", "display": "symbol", "currency": "GBP", "accounting": false}, "width": 186, "header": "Multiply of Number of tickets and Ticket total", "isLabel": false, "aggregate": "sum"}, {"id": 15, "calc": {"type": "sum", "sourceCols": [14, 2]}, "type": "currency", "props": {"locale": "en-GB", "display": "symbol", "currency": "GBP", "accounting": false}, "width": 212, "header": "Sum of Multiply of Number of tickets and Ticket total and Number of tickets", "isLabel": false, "aggregate": "sum"}, {"id": 10, "type": "currency", "props": {"locale": "en-GB", "display": "symbol", "currency": "GBP", "accounting": false}, "width": 150, "header": "Buyer paid", "isLabel": false, "aggregate": "sum"}, {"id": 11, "type": "currency", "props": {"locale": "en-GB", "display": "symbol", "currency": "GBP", "accounting": false}, "width": 150, "header": "Your revenue", "isLabel": false, "aggregate": "sum"}]}