// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/gaps/table-with-gaps.json'
import tableRows from '@/tests/db/testdata/gaps/table-with-gaps-rows.json'
import { filterQuerySpec, getQuerySpec, tableToCols } from '@/db/query/utils.js'

const TIMEOUT = 30000
/*
Test out getting auto gapping, used for charting columns.

This data source has 3 columns in a table of 36 rows
- c1 has gaps in the first 9 cells, and last 4
- c2 is a comparison of c1 up 4 values, so cells don't start until row 14
- c3 has no gaps; all rows have values, and we should get no row restrictions

Combining these should take the widest range.

This can be created with table-with-gaps.csv - just be wary the order is different
the last col is a calculated 4-gap changed % col The view is a year-week view

 */

describe('Test autoRowLimits', () => {
  let conn
  let table
  const tableId = tableDef.id
  let querySpec
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
    querySpec = getQuerySpec(tableDef.columns)
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  describe('Charting single columns', () => {
    
    it('Column c3 should return no row limits', async () => {
      const limits = await table.autoRowLimits([3])
      expect(limits).toEqual({})
    })
    
    it('Column c1 should return row limits', async () => {
      const limits = await table.autoRowLimits([1])
      expect(limits).toEqual({ from: 9, to: 31 })
    })
    
    it('Column c2 should return row limits', async () => {
      const limits = await table.autoRowLimits([2])
      expect(limits).toEqual({ from: 13, to: 31 })
    })
  })
  
  describe('Charting multiple columns should take the wider range', () => {
    
    it('Column c1 and c2 should return widest row limits', async () => {
      const limits = await table.autoRowLimits([1, 2], querySpec)
      expect(limits).toEqual({ from: 9, to: 31 })
    })
    
    it('Column c3 and c2 should return no row limits limits', async () => {
      const limits = await table.autoRowLimits([3, 2], querySpec)
      expect(limits).toEqual({})
    })
  })
  
  describe('Charting single column of view', () => {
    /*
    This view groups on each column; c2 misses the first row, c3 misses the first 2
     */
    const view = {
      groups: [
        {
          by: 'year-week',
          sourceColumnId: 0,
          id: 0,
          width: 150
        }
      ],
      columns: [
        {
          sourceColumnId: 3,
          id: 1,
          summarize: 'avg'
        },
        {
          sourceColumnId: 1,
          id: 2,
          header: 'Gapped value (Average)',
          summarize: 'avg'
        },
        {
          sourceColumnId: 2,
          id: 3,
          header: 'Calc on gapped value (Average)',
          summarize: 'avg'
        }
      ]
    }
    const querySpec = getQuerySpec(tableDef.columns, view)
    
    // Note here that the ids are confusing - they are index-based on a group report (they get automatic
    // ids matching their order), starting at 0 with the group. The 'ids' above are what map to the ids
    // of the raw table
    
    /*
    This is what the grouped view looks like

      2018-01	87.66
      2018-02	87.3		86.9
      2018-03	86.84		86.84		-0.4%
      2018-04	86.96		86.96		0.06%
      2018-05	87.6		87.6		0.2%
      2018-06	86.38		86.38		-0.46%
      2018-07	86.72		86.72		0.12%
      2018-08	84.94		86.6		-0.3%

     */
    it('View c1 should return no row limits', async () => {
      const limits = await table.autoRowLimits([1], querySpec)
      expect(limits).toEqual({})
    })
    
    it('View c2 should return row limits', async () => {
      const limits = await table.autoRowLimits([2], querySpec)
      expect(limits).toEqual({ from: 1 })
    })
    
    it('View c3 should return row limits', async () => {
      const limits = await table.autoRowLimits([3], querySpec)
      expect(limits).toEqual({ from: 2 })
    })
  })
})

describe('Test rowArray method', () => {
  let conn
  let table
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  describe('Basic rowArray functionality', () => {
    
    it('Should return all rows when no filters are applied', async () => {
      const result = await table.rowArray([1, 3])
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(tableRows.length)
    })
    
    it('Should return filtered rows when posRange is specified', async () => {
      const posRange = { from: 10, to: 15 }
      const querySpec = filterQuerySpec(getQuerySpec(tableDef.columns), { posRange })
      const result = await table.rowArray([1, 3], querySpec)
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(6) // 15 - 10 + 1 = 6 rows
    })
    
    it('Should return filtered rows when rowIds are specified', async () => {
      const rowIds = [1, 3, 5]
      const querySpec = filterQuerySpec(getQuerySpec(tableDef.columns), { rowIds })
      const result = await table.rowArray([1, 3], querySpec)
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(3)
    })
    
    it('Should return representation values when reprValues is true', async () => {
      const querySpec = filterQuerySpec(getQuerySpec(tableDef.columns), { posRange: { from: 0, to: 5 } })
      const result = await table.rowArray([3], querySpec, true)
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(6)
      // Check that values are strings (representation values)
      result.forEach(row => {
        expect(typeof row[0]).toBe('string')
      })
    })
  })
  
  describe('rowArray with view parameter', () => {
    const apiView = {
      groups: [
        {
          by: 'year-week',
          sourceColumnId: 0,
          id: 0,
          width: 150
        }
      ],
      columns: [
        {
          sourceColumnId: 3,
          id: 1,
          summarize: 'avg'
        },
        {
          sourceColumnId: 1,
          id: 2,
          header: 'Gapped value (Average)',
          summarize: 'avg'
        }
      ]
    }
    
    const columns = tableToCols(tableDef.columns, apiView)
    const querySpec = getQuerySpec(tableDef.columns, apiView)
    it('Should return grouped data when view is specified', async () => {
      const result = await table.rowArray(columns.map(c => c.id), querySpec)
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      // The result should be grouped by year-week, so fewer rows than the original data
      expect(result.length).toBeLessThan(tableRows.length)
    })
  })
})

describe('Test chartData method', () => {
  let conn
  let table
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  describe('Basic chartData functionality', () => {
    
    it('Should return chart data for all rows when no filter is applied', async () => {
      const result = await table.chartData([1, 3])
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(tableRows.length)
      // Check that the result has the expected structure
      expect(result[0]).toHaveProperty('id')
      expect(result[0]).toHaveProperty('c1')
      expect(result[0]).toHaveProperty('c3')
    })
    
    it('Should return filtered chart data when posRange is specified', async () => {
      const querySpec = getQuerySpec(tableDef.columns)
      const spec = filterQuerySpec(querySpec, { posRange: { from: 10, to: 15 } })
      const result = await table.chartData([1, 3], spec)
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(6) // 15 - 10 + 1 = 6 rows
    })
    
    it('Should return filtered chart data when rowIds are specified', async () => {
      const querySpec = getQuerySpec(tableDef.columns)
      const spec = filterQuerySpec(querySpec, { rowIds: [1, 3, 5] })
      const result = await table.chartData([1, 3], spec)
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(3)
    })
  })
  
})
