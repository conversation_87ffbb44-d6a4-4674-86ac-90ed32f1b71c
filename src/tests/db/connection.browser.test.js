import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

describe('DuckDB Browser Integration Tests', () => {
  let connection
  
  beforeAll(async () => {
    connection = createConnection()
    await connection.connect(false)
  }, TIMEOUT)
  
  afterAll(async () => {
    if (connection?.conn) {
      await connection.disconnect()
    }
  })
  
  describe('Connection Management', () => {
    it('should create a valid connection object', () => {
      expect(connection).toBeDefined()
      expect(connection.tables).toEqual({})
      expect(connection.firstTableId).toBeNull()
      expect(connection.lastTableId).toBeNull()
    })
    
    it('should have a working connection', () => {
      expect(connection.conn).toBeDefined()
      expect(connection.db).toBeDefined()
    })
    
    it('should execute a simple SQL query', async () => {
      const result = await connection.conn.query('SELECT 1 as value;')
      expect(result).toBeDefined()
      const rows = result.toArray()
      expect(rows).toHaveLength(1)
      expect(rows[0].value).toBe(1)
    })
    
    it('should properly format SQL queries', async () => {
      const result = await connection.conn.query(`
        SELECT
          1 as value
      `)
      expect(result).toBeDefined()
      const rows = result.toArray()
      expect(rows).toHaveLength(1)
      expect(rows[0].value).toBe(1)
    })
    
    it('should handle disconnection properly', async () => {
      const newConn = createConnection()
      await newConn.connect(false)
      
      await newConn.disconnect()
      expect(newConn.conn).toBeNull()
      expect(newConn.tables).toEqual({})
      expect(newConn.firstTableId).toBeNull()
      expect(newConn.lastTableId).toBeNull()
    })
    
    it('should handle multiple connections and disconnections', async () => {
      const newConn = createConnection()
      await newConn.connect(false)
      await newConn.disconnect()
      await newConn.connect(false)
      
      expect(newConn.conn).toBeDefined()
      expect(newConn.db).toBeDefined()
      
      await newConn.disconnect()
    })
  })
})
