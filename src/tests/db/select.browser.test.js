// noinspection DuplicatedCode

import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { createConnection } from '@/db/connection.js'
import tableDef from '@/tests/db/testdata/views/table-with-views.json'
import tableRows from '@/tests/db/testdata/views/table-with-views-rows.json'
import Big from 'big.js'
import { getChartData, getChartProps } from '@/charts/chartProps.js'
import { getQuerySpec, tableToCols } from '@/db/query/utils.js'

// Longer timeout for WebAssembly initialization
const TIMEOUT = 30000

const getResultsFromView = async (table, apiCols, apiView) => {
  /* Wraps around the total we have in terms of cols/views and mapping
   them to 'query' cols/views. */
  const columns = tableToCols(apiCols, apiView)
  const querySpec = getQuerySpec(apiCols, apiView)
  return await table.rowObjects(columns.map(col => col.id), querySpec)
}

describe('Selecting data', () => {
  /*
  Big test set for querying data.

  Because this doesn't do any data manipulation, we only need to create the table once for all tests.

  The test file this is based on also exists as table-with-views.csv if we need to reimport it.
  */
  let conn
  let table
  const tableId = tableDef.id
  
  beforeAll(async () => {
    conn = createConnection()
    await conn.connect(false)
    await conn.buildTable(tableId, tableDef.columns, tableRows)
    table = conn.tables[tableId]
  }, TIMEOUT)
  
  afterAll(async () => {
    if (conn?.conn) {
      await conn.dropTable(tableId)
      await conn.disconnect()
    }
  })
  
  describe('Test Views', () => {
    
    describe('Grouped views that should work', () => {
      it('should execute a basic grouped view query', async () => {
        const view = {
          groups: [{ by: 'year-week', sourceColumnId: 1, id: 0 }],
          columns: [{ sourceColumnId: 2, id: 1, summarize: 'sum' }]
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        expect(results.length).toBe(10)
        expect(results[0].c0).toBe('2024-05')
        expect(results[results.length - 1].c0).toBe('2024-14')
        expect(results[0].c1).toEqual(Big('17'))
        expect(results[results.length - 1].c1).toEqual(Big('87'))
      })
      
      it('should execute a grouping on a calculated column', async () => {
        const view = {
          groups: [{ sourceColumnId: 1, id: 0, by: 'year-week' }],
          columns: [{ sourceColumnId: 14, id: 1, summarize: 'sum' }]
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        expect(results.length).toBe(10)
        expect(results[0].c0).toBe('2024-05')
        expect(results[results.length - 1].c0).toBe('2024-14')
        
        expect(results[0].c1).toEqual(Big('680'))
        expect(results[results.length - 1].c1).toEqual(Big('2110'))
        
      })
      
      it('Should execute a grouping of calc-on-calc column', async () => {
        const view = {
          groups: [{ sourceColumnId: 1, id: 0, by: 'year-week' }],
          columns: [{ sourceColumnId: 15, id: 1, summarize: 'sum' }]  // This is a calc that itself has a calc
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        expect(results.length).toBe(10)
        expect(results[0].c0).toBe('2024-05')
        expect(results[results.length - 1].c0).toBe('2024-14')
        
        expect(results[0].c1).toEqual(Big('697'))
        expect(results[results.length - 1].c1).toEqual(Big('2197'))
      })
      
      it('should execute a summary of a window column', async () => {
        const view = {
          groups: [{ sourceColumnId: 1, id: 0, by: 'year-week' }],
          columns: [{ sourceColumnId: 12, id: 1, summarize: 'sum' }]  // This is a window function - not valid!
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        expect(results.length).toBe(10)
        expect(results[0].c0).toBe('2024-05')
        expect(results[0].c1).toEqual(Big('48'))
        expect(results[results.length - 1].c0).toBe('2024-14')
        expect(results[results.length - 1].c1).toEqual(Big('13153'))
        
      })
    })
    
    describe('Grouped views around window functions', () => {
      
      it('should execute a grouping on a window column without failure', async () => {
        const view = {
          groups: [{ sourceColumnId: 12, id: 0 }],  // This is a window function - is now valid!
          columns: [{ sourceColumnId: 'id', id: 1, summarize: 'count' }]
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        expect(results.length).toBe(156)
        expect(results[0].c0).toEqual(Big('1'))
        expect(results[0].c1).toBe(1)
        
      })
    })
  })
  
  describe('Column Sorting', () => {
    
    describe('Basic sorting', () => {
      it('should sort by a single column in ascending order', async () => {
        const view = {
          columns: [
            { sourceColumnId: 0, id: 0 }, // First column (label)
            { sourceColumnId: 2, id: 1, sort: { order: 0, desc: false } } // Sort by second column ascending
          ]
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        
        // Check that results are sorted by column 2 in ascending order
        for (let i = 1; i < results.length; i++) {
          const prev = Number(results[i - 1].c1)
          const curr = Number(results[i].c1)
          expect(prev).toBeLessThanOrEqual(curr)
        }
      })
      
      it('should sort by a single column in descending order', async () => {
        const view = {
          columns: [
            { sourceColumnId: 0, id: 0 }, // First column (label)
            { sourceColumnId: 2, id: 1, sort: { order: 0, desc: true } } // Sort by second column descending
          ]
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        
        // Check that results are sorted by column 2 in descending order
        for (let i = 1; i < results.length; i++) {
          const prev = Number(results[i - 1].c1)
          const curr = Number(results[i].c1)
          expect(prev).toBeGreaterThanOrEqual(curr)
        }
      })
    })
    
    describe('Multi-column sorting', () => {
      it('should sort by multiple columns with different priorities', async () => {
        const view = {
          columns: [
            { sourceColumnId: 0, id: 0 }, // First column (label)
            { sourceColumnId: 1, id: 1, sort: { order: 1, desc: false } }, // Secondary sort by column 2 ascending
            { sourceColumnId: 2, id: 2, sort: { order: 0, desc: false } }  // Primary sort by column 3 ascending
          ]
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        
        // For simplicity, we'll just check that the results are returned
        // A more thorough test would verify the multi-column sort order
        expect(results.length).toBeGreaterThan(0)
      })
    })
    
    describe('Sorting with grouped data', () => {
      it('should sort grouped data by the group column', async () => {
        const view = {
          groups: [{ sourceColumnId: 1, id: 0, by: 'year-week', sort: { order: 0, desc: true } }], // Sort groups in descending order
          columns: [{ sourceColumnId: 2, id: 1, summarize: 'sum' }]
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        expect(results.length).toBe(10)
        
        // Check that results are sorted by group column in descending order
        for (let i = 1; i < results.length; i++) {
          const prev = results[i - 1].c0
          const curr = results[i].c0
          
          // Check a string comparison like 2024-05 < 2024-06
          expect(prev.localeCompare(curr)).toBeGreaterThanOrEqual(0)
        }
      })
      
      it('should sort grouped data by the value column', async () => {
        const view = {
          groups: [{ sourceColumnId: 1, id: 0, by: 'year-week' }],
          columns: [{ sourceColumnId: 2, id: 1, summarize: 'sum', sort: { order: 0, desc: true } }] // Sort by sum in descending order
        }
        const results = await getResultsFromView(table, tableDef.columns, view)
        expect(results).toBeDefined()
        expect(results.length).toBe(10)
        
        // Check that results are sorted by value column in descending order
        for (let i = 1; i < results.length; i++) {
          const prev = Number(results[i - 1].c1)
          const curr = Number(results[i].c1)
          expect(prev).toBeGreaterThanOrEqual(curr)
        }
      })
    })
  })
  
  describe('Aggregation with column sorting', () => {
    
    it('Sorting with a count query should work', async () => {
      const view = {
        columns: [
          { sourceColumnId: 0, id: 0 }, // First column (label)
          { sourceColumnId: 2, id: 1, sort: { order: 0, desc: false } } // Sort by second column ascending
        ]
      }
      const querySpec = getQuerySpec(tableDef.columns, view)
      const count = await table.count(querySpec)
      expect(count).toBeDefined()
      expect(count).toEqual(156)
    })
    it('Sorting with an aggregate query should work', async () => {
      const view = {
        columns: [
          { sourceColumnId: 0, id: 0 }, // First column (label)
          { sourceColumnId: 2, id: 1, sort: { order: 0, desc: false } } // Sort by second column ascending
        ]
      }
      const querySpec = getQuerySpec(tableDef.columns, view)
      const aggs = await table.aggregate(1, ['count', 'max'], querySpec)
      expect(aggs).toBeDefined()
      expect(aggs.count).toEqual(156)
      expect(aggs.max).toEqual(Big(8))
    })
  })
  
  describe('Test Chart Queries', () => {
    
    const cols = tableDef.columns.map(col => ({
      ...col,
      field: `c${col.id}`,
      inView: false,
      inGroupedView: false
    }))
    
    const labelCol = 0
    const querySpec = getQuerySpec(tableDef.columns)
    
    const getChartDataFromDb = async (csd) => {
      const props = getChartProps('', csd, cols, querySpec)
      return await getChartData(props, table)
    }
    
    describe('Charts of a non-view table', () => {
      
      it('Should get basic line chart data', async () => {
        const data = await getChartDataFromDb({
          axis: { column: 0 },
          datasets: [
            { series: [2], chartType: 'line' }
          ]
        })
        expect(data).toBeDefined()
        expect(data.rows).toBeDefined()
        expect(Array.isArray(data.rows)).toBe(true)
        
        // rows should be length of original row data
        expect(data.rows.length).toBe(tableRows.length)
        
      })
      
      it('Should get basic line chart data with row picking', async () => {
        const valueCol = 2
        const data = await getChartDataFromDb({
          axis: { column: labelCol },
          rows: { from: 4, to: 7 },
          datasets: [
            { series: [valueCol], chartType: 'line' }
          ]
        })
        
        expect(data).toBeDefined()
        expect(data.rows).toBeDefined()
        expect(Array.isArray(data.rows)).toBe(true)
        
        const rows = data.rows
        expect(rows.length).toBe(4)
        
        // Check all labels and values
        expect(rows.map(row => row[`c${labelCol}`])).toEqual(['2VPT-PHXS-GRG', '2VPT-S916-HLN', '2VPW-8QTB-MQD', '2VPX-98XN-09K'])
        expect(rows.map(row => Number(row[`c${valueCol}`]))).toEqual([3, 3, 2, 6])
        
      })
      
      it('Should get basic line chart data with row picking on calc column', async () => {
        const valueCol = 14 // This is a calc column on 2 static values
        const data = await getChartDataFromDb({
          axis: { column: labelCol },
          rows: { from: 4, to: 7 },
          datasets: [
            { series: [valueCol], chartType: 'line' }
          ]
        })
        
        expect(data).toBeDefined()
        expect(data.rows).toBeDefined()
        expect(Array.isArray(data.rows)).toBe(true)
        
        const rows = data.rows
        expect(rows.length).toBe(4)
        
        // Check all labels and values
        expect(rows.map(row => row[`c${labelCol}`])).toEqual(['2VPT-PHXS-GRG', '2VPT-S916-HLN', '2VPW-8QTB-MQD', '2VPX-98XN-09K'])
        expect(rows.map(row => Number(row[`c${valueCol}`]))).toEqual([72, 72, 32, 288])
        
      })
      
      it('Should get basic line chart data on window calc column', async () => {
        const valueCol = 13  // / This is a window calc - a cumulative
        const data = await getChartDataFromDb({
          axis: { column: labelCol },
          datasets: [
            { series: [valueCol], chartType: 'line' }
          ]
        })
        expect(data).toBeDefined()
        expect(data.rows).toBeDefined()
        expect(Array.isArray(data.rows)).toBe(true)
        const rows = data.rows
        expect(rows.length).toBe(tableRows.length)
        
        // Check rows 4 to 7 - as we start query at 0 this should be 'easier' as we get the entire window function
        const rows4to7 = rows.slice(4, 8)
        expect(rows4to7.map(row => row[`c${labelCol}`])).toEqual(['2VPT-PHXS-GRG', '2VPT-S916-HLN', '2VPW-8QTB-MQD', '2VPX-98XN-09K'])
        expect(rows4to7.map(row => Number(row[`c${valueCol}`]))).toEqual([17, 20, 21, 31])
      })
      
      it('Should get basic line chart data with row picking on window calc column', async () => {
        // Same as above except row picking happens at the db, which (in my first nieve implementation) broke as the
        // window function started where we cut off the rows
        
        const valueCol = 13  // / This is a window calc - a cumulative
        const data = await getChartDataFromDb({
          axis: { column: labelCol },
          rows: { from: 4, to: 7 },
          datasets: [
            { series: [valueCol], chartType: 'line' }
          ]
        })
        
        expect(data).toBeDefined()
        expect(data.rows).toBeDefined()
        expect(Array.isArray(data.rows)).toBe(true)
        
        const rows = data.rows
        expect(rows.length).toBe(4)
        
        // Check all labels and values
        expect(rows.map(row => row[`c${labelCol}`])).toEqual(['2VPT-PHXS-GRG', '2VPT-S916-HLN', '2VPW-8QTB-MQD', '2VPX-98XN-09K'])
        expect(rows.map(row => Number(row[`c${valueCol}`]))).toEqual([17, 20, 21, 31])
        
      })
      
    })
  })
})
