<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Node tests" type="JavaScriptTestRunnerVitest">
    <node-interpreter value="project" />
    <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
    <working-dir value="$PROJECT_DIR$" />
    <vitest-options value="--exclude **/*.browser.test.js --run" />
    <envs />
    <scope-kind value="ALL" />
    <method v="2" />
  </configuration>
</component>