<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Browser tests" type="JavaScriptTestRunnerVitest">
    <node-interpreter value="project" />
    <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
    <working-dir value="$PROJECT_DIR$" />
    <vitest-options value="--mode browser-test --run" />
    <envs />
    <scope-kind value="ALL" />
    <method v="2" />
  </configuration>
</component>