<!DOCTYPE html>
<html lang="" class="h-full">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="apple-touch-icon" sizes="180x180" href="favicon/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="192x192" href="favicon/favicon-192x192.png">
  <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
  <link rel="icon" type="image/png" sizes="48x48" href="favicon/favicon-48x48.png">
  <link rel="icon" type="image/png" sizes="32x32" href="favicon/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="favicon/favicon-16x16.png">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="shortcut icon" href="favicon.ico">
  <title>DataHero</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;300;400;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Material+Icons" rel="stylesheet">
  <script defer data-domain="dataherohq.com"
          src="https://plausible.io/js/script.file-downloads.outbound-links.pageview-props.tagged-events.js"></script>
  <script>window.plausible = window.plausible || function () { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>
</head>
<body class="h-full">
<noscript>
  <strong>We're sorry but DataHero doesn't work properly without JavaScript enabled.
    Please enable it to continue.</strong>
</noscript>
<div id="datahero"></div>
<script type="module" src="/src/main.js"></script>
</body>
</html>
